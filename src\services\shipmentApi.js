const API_BASE_URL = 'https://dd-ops.com/donuttracking/api';


export const fetchAllShipments = async (params = {}) => {
  try {
    const queryParams = new URLSearchParams();
    
    
    if (params.page) queryParams.append('page', params.page);
    if (params.per_page) queryParams.append('per_page', params.per_page);
    
    const url = `${API_BASE_URL}/shipments/get-all-shipments${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

    const storedToken = JSON.parse(localStorage.getItem("currentUser"))
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + storedToken.token,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching shipments:', error);
    throw error;
  }
};


export const addShipment = async (shipmentData) => {
  try {
    const url = `${API_BASE_URL}/shipments/add-shipments`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        
        
      },
      body: JSON.stringify(shipmentData),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error adding shipment:', error);
    throw error;
  }
};


export const fetchRegions = async () => {
  try {
    const url = `${API_BASE_URL}/regions`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.data || [];
  } catch (error) {
    console.error('Error fetching regions:', error);
    throw error;
  }
};


export const fetchStores = async () => {
  try {
    const url = `${API_BASE_URL}/stores`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.data || [];
  } catch (error) {
    console.error('Error fetching stores:', error);
    throw error;
  }
};


const parseApiDate = (dateString) => {
  if (!dateString) return null;
  
  try {
    
    
    const parts = dateString.split(' ');
    if (parts.length >= 2) {
      const datePart = parts[0]; 
      const timePart = parts.slice(1).join(' '); 
      
      
      const dateComponents = datePart.split('/');
      if (dateComponents.length === 3) {
        const day = parseInt(dateComponents[0], 10);
        const month = parseInt(dateComponents[1], 10) - 1; 
        const year = parseInt(dateComponents[2], 10);
        
        
        let hours = 0, minutes = 0;
        if (timePart.includes('AM') || timePart.includes('PM')) {
          const timeMatch = timePart.match(/(\d{1,2}):(\d{2})\s*(AM|PM)/i);
          if (timeMatch) {
            hours = parseInt(timeMatch[1], 10);
            minutes = parseInt(timeMatch[2], 10);
            const ampm = timeMatch[3].toUpperCase();
            
            if (ampm === 'PM' && hours !== 12) {
              hours += 12;
            } else if (ampm === 'AM' && hours === 12) {
              hours = 0;
            }
          }
        } else {
          
          const timeComponents = timePart.split(':');
          if (timeComponents.length >= 2) {
            hours = parseInt(timeComponents[0], 10);
            minutes = parseInt(timeComponents[1], 10);
          }
        }
        
        return new Date(year, month, day, hours, minutes);
      }
    }
    
    
    const parsed = new Date(dateString);
    return isNaN(parsed.getTime()) ? null : parsed;
  } catch (error) {
    console.warn('Failed to parse date:', dateString, error);
    return null;
  }
};


export const transformApiShipments = (apiShipments) => {
  return apiShipments.map(shipment => ({
    id: shipment.id,
    bakeryType: shipment.bakery_type,
    quantity: shipment.quantity,
    driver: shipment.distributor_name, 
    vehicleNumber: shipment.vehicle_number,
    region: shipment.region,
    route: shipment.route,
    status: mapApiStatusToLocal(shipment.status),
    qualityStatus: mapApiQualityStatusToLocal(shipment.quality_status),
    storeStatus: "Pending", 
    qualityNotes: "", 
    storesTickets: shipment.store_names ? shipment.store_names.map(storeName => ({
      store: storeName,
      storeCode: "12345", 
      status: "Pending",
      updatedAt: new Date(),
      notes: "",
      vehicleNumber: shipment.vehicle_number,
      images: []
    })) : null,
    comments: [],
    timestamps: {
      created: parseApiDate(shipment.created_at) || new Date(),
      driverPickup: null,
      delivery: null,
      branchReceive: null,
      display: null,
      qualityCheckedAt: null,
      storeStatusUpdatedAt: null,
    },
    
    productionName: shipment.production_name,
    storesCount: shipment.stores_count,
    storeNames: shipment.store_names,
    date: shipment.date,
    time: shipment.time,
    rawCreatedAt: shipment.created_at, 
  }));
};


export const transformLocalShipmentToApi = (localShipment) => {
  return {
    region_id: localShipment.region_id || 1,
    production_id: localShipment.production_id || 1,
    route_id: localShipment.route_id || localShipment.store_id || 1, 
    vehicle_number: localShipment.vehicleNumber,
    bakery_type: localShipment.bakeryType,
    quantity: localShipment.quantity,
    date: localShipment.date,
    time: localShipment.time,
    created: localShipment.created_at,
    distributor_id: localShipment.distributor_id || 2,
    status: mapLocalStatusToApi(localShipment.status),
    quality_status: mapLocalQualityStatusToApi(localShipment.qualityStatus),
  };
};


const mapApiStatusToLocal = (apiStatus) => {
  const statusMap = {
    'created': 'Pending Pickup',
    'in_transit': 'In Transit',
    'delivered': 'Delivered',
    'received': 'Received',
  };
  return statusMap[apiStatus] || apiStatus;
};

const mapLocalStatusToApi = (localStatus) => {
  const statusMap = {
    'Pending Pickup': 'created',
    'In Transit': 'in_transit',
    'Delivered': 'delivered',
    'Received': 'received',
  };
  return statusMap[localStatus] || 'created';
};


const mapApiQualityStatusToLocal = (apiQualityStatus) => {
  const qualityStatusMap = {
    'pending': 'Pending',
    'confirmed': 'Confirmed',
    'rejected': 'Rejected',
  };
  return qualityStatusMap[apiQualityStatus] || apiQualityStatus;
};

const mapLocalQualityStatusToApi = (localQualityStatus) => {
  const qualityStatusMap = {
    'Pending': 'pending',
    'Confirmed': 'confirmed',
    'Rejected': 'rejected',
  };
  return qualityStatusMap[localQualityStatus] || 'pending';
};


export const getShipmentsData = async (params = {}) => {
  try {
    const response = await fetchAllShipments(params);
    
    return {
      shipments: transformApiShipments(response.data),
      pagination: response.pagination,
      error: null,
    };
  } catch (error) {
    console.error('Error in getShipmentsData:', error);
    return {
      shipments: [],
      pagination: null,
      error: error.message,
    };
  }
};


export const addShipmentData = async (shipmentData) => {
  try {
    const apiData = transformLocalShipmentToApi(shipmentData);
    const response = await addShipment(apiData);
    
    return {
      success: true,
      data: response,
      error: null,
    };
  } catch (error) {
    console.error('Error in addShipmentData:', error);
    return {
      success: false,
      data: null,
      error: error.message,
    };
  }
};


export const getRegionsData = async () => {
  try {
    const regions = await fetchRegions();
    return {
      regions,
      error: null,
    };
  } catch (error) {
    console.error('Error in getRegionsData:', error);
    return {
      regions: [],
      error: error.message,
    };
  }
};

export const getStoresData = async () => {
  try {
    const stores = await fetchStores();
    return {
      stores,
      error: null,
    };
  } catch (error) {
    console.error('Error in getStoresData:', error);
    return {
      stores: [],
      error: error.message,
    };
  }
};

// Quality API functions
export const fetchQualityShipments = async (productionId) => {
  try {
    const url = `${API_BASE_URL}/quality/shipments`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ production_id: productionId }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching quality shipments:', error);
    throw error;
  }
};

export const submitQualityAnswers = async (shipmentId, productionId, answers, qualityId) => {
  try {
    const url = `${API_BASE_URL}/quality/shipments/${shipmentId}/answers`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        shipment_id: shipmentId,
        production_id: productionId,
        answers: answers,
        quality_id: qualityId
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error submitting quality answers:', error);
    throw error;
  }
};

export const fetchQualityAnswers = async (shipmentId) => {
  try {
    const url = `${API_BASE_URL}/quality/shipments/${shipmentId}/get-answers`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching quality answers:', error);
    throw error;
  }
};

export const fetchProductions = async () => {
  try {
    const url = `${API_BASE_URL}/productions`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.data || [];
  } catch (error) {
    console.error('Error fetching productions:', error);
    throw error;
  }
};

// Transform quality API data to local format
export const transformQualityShipments = (apiQualityShipments) => {
  return apiQualityShipments.map(shipment => ({
    id: shipment.id,
    bakeryType: shipment.bakery_type || shipment.production_name,
    quantity: shipment.quantity,
    driver: shipment.distributor_name || shipment.driver,
    vehicleNumber: shipment.vehicle_number,
    region: shipment.region,
    route: shipment.route,
    status: mapApiStatusToLocal(shipment.status),
    qualityStatus: mapApiQualityStatusToLocal(shipment.quality_status),
    storeStatus: "Pending", 
    qualityNotes: shipment.quality_notes || "", 
    storesTickets: shipment.store_names ? shipment.store_names.map(storeName => ({
      store: storeName,
      storeCode: "12345", 
      status: "Pending",
      updatedAt: new Date(),
      notes: "",
      vehicleNumber: shipment.vehicle_number,
      images: []
    })) : null,
    comments: [],
    timestamps: {
      created: parseApiDate(shipment.created_at) || new Date(),
      driverPickup: null,
      delivery: null,
      branchReceive: null,
      display: null,
      qualityCheckedAt: parseApiDate(shipment.quality_checked_at),
      storeStatusUpdatedAt: null,
    },
    productionName: shipment.production_name,
    storesCount: shipment.stores_count,
    storeNames: shipment.store_names,
    date: shipment.date,
    time: shipment.time,
    rawCreatedAt: shipment.created_at,
    productionId: shipment.production_id,
  }));
};

// Quality API data functions
export const getQualityShipmentsData = async (productionId) => {
  try {
    const response = await fetchQualityShipments(productionId);
    
    return {
      shipments: transformQualityShipments(response.data || []),
      error: null,
    };
  } catch (error) {
    console.error('Error in getQualityShipmentsData:', error);
    return {
      shipments: [],
      error: error.message,
    };
  }
};

export const submitQualityAnswersData = async (shipmentId, productionId, answers, qualityId) => {
  try {
    const response = await submitQualityAnswers(shipmentId, productionId, answers, qualityId);
    
    return {
      success: true,
      data: response,
      error: null,
    };
  } catch (error) {
    console.error('Error in submitQualityAnswersData:', error);
    return {
      success: false,
      data: null,
      error: error.message,
    };
  }
};

export const getQualityAnswersData = async (shipmentId) => {
  try {
    const response = await fetchQualityAnswers(shipmentId);
    
    return {
      answers: response.data?.answers || [],
      error: null,
    };
  } catch (error) {
    console.error('Error in getQualityAnswersData:', error);
    return {
      answers: [],
      error: error.message,
    };
  }
};

export const getProductionsData = async () => {
  try {
    const productions = await fetchProductions();
    return {
      productions,
      error: null,
    };
  } catch (error) {
    console.error('Error in getProductionsData:', error);
    return {
      productions: [],
      error: error.message,
    };
  }
};
