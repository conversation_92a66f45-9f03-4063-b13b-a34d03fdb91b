import { useEffect, useMemo, useState } from "react";
import { Eye, EyeOff } from "lucide-react";
import { Select } from "antd";
import regions from "../../../data/regions";
import factories from "../../../data/factories";

const EditDistributorModal = ({ onClose, distributor, onSave }) => {
  const [name, setName] = useState(distributor?.name ?? "");
  const [email, setEmail] = useState(distributor?.email ?? "");
  const [password, setPassword] = useState("");
  const [status, setStatus] = useState(distributor?.status ?? "active");
  const [showPassword, setShowPassword] = useState(false);
  const [selectedRegionId, setSelectedRegionId] = useState(distributor?.region?.id ?? null);
  const [selectedRouteId, setSelectedRouteId] = useState(distributor?.route?.id ?? null);
  const [selectedFactoryId, setSelectedFactoryId] = useState(distributor?.factory?.id ?? null);

  const selectedRegion = useMemo(
    () => regions.find((r) => r.id === selectedRegionId) || null,
    [selectedRegionId]
  );
  const regionRoutes = selectedRegion?.routes || [];

  useEffect(() => {
    setName(distributor?.name ?? "");
    setEmail(distributor?.email ?? "");
    setStatus(distributor?.status ?? "active");
    setSelectedRegionId(distributor?.region?.id ?? null);
    setSelectedRouteId(distributor?.route?.id ?? null);
    setSelectedFactoryId(distributor?.factory?.id ?? null);
  }, [distributor]);

  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === "Escape") onClose();
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [onClose]);

  const handleSubmit = () => {
    const trimmedName = name.trim();
    const trimmedEmail = email.trim();
    if (!trimmedName || !trimmedEmail) return;
    const selectedRoute = regionRoutes.find((rt) => rt.id === selectedRouteId) || null;
    const selectedFactory = factories.find((f) => f.id === selectedFactoryId) || null;
    const payload = {
      ...distributor,
      name: trimmedName,
      email: trimmedEmail,
      // update password only if provided
      ...(password.trim() ? { password: password.trim() } : {}),
      status,
      region: selectedRegion
        ? { id: selectedRegion.id, name: selectedRegion.name }
        : null,
      route: selectedRoute
        ? { id: selectedRoute.id, name: selectedRoute.name }
        : null,
      factory: selectedFactory
        ? { id: selectedFactory.id, name: selectedFactory.name }
        : null,
      updated_at: new Date(),
    };
    onSave && onSave(payload);
    onClose && onClose();
  };

  if (!distributor) return null;

  return (
    <div
      onClick={() => onClose()}
      className=" !m-0 fixed  !p-10 inset-0 top-0 bottom-0 left-0 right-0 bg-black bg-opacity-50 flex items-center justify-center z-50 "
    >
      <div
        onClick={(e) => e.stopPropagation()}
        className="bg-white rounded-xl shadow-xl w-full max-w-lg max-h-[95vh] overflow-y-auto custom-scrollbar"
      >
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900">Edit Distributor</h2>
          <p className="text-sm text-gray-600 mt-1">Update distributor details.</p>
        </div>

        <div className="p-6 space-y-5">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Full Name <span className="text-red-500">*</span></label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              placeholder="Enter full name"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Email <span className="text-red-500">*</span></label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Password</label>
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 pr-10"
                placeholder="Leave blank to keep current"
              />
              <button
                type="button"
                onClick={() => setShowPassword((v) => !v)}
                className="absolute inset-y-0 right-0 px-3 flex items-center text-gray-500 hover:text-gray-700"
                aria-label={showPassword ? "Hide password" : "Show password"}
              >
                {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
            <select
              value={status}
              onChange={(e) => setStatus(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            >
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Region</label>
            <Select
              showSearch
              placeholder="Select region"
              value={selectedRegionId}
              onChange={(value) => {
                setSelectedRegionId(value);
              }}
              optionFilterProp="label"
              className="w-full"
              options={regions.map((r) => ({ value: r.id, label: r.name }))}
            />
          </div>

       

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Factory</label>
            <Select
              showSearch
              placeholder="Select factory"
              value={selectedFactoryId}
              onChange={setSelectedFactoryId}
              optionFilterProp="label"
              className="w-full"
              options={factories.map((f) => ({ value: f.id, label: f.name }))}
            />
          </div>
        </div>

        <div className="p-6 border-t border-gray-200 flex gap-3">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            className="flex-1 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors disabled:opacity-50"
            disabled={!name.trim() || !email.trim()}
          >
            Save Changes
          </button>
        </div>
      </div>
    </div>
  );
};

export default EditDistributorModal;


