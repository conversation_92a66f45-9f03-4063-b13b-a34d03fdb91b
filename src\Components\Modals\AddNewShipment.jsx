import { useMemo, useState, useEffect } from "react";
import { <PERSON>R<PERSON>, Camera } from "lucide-react";
import UploadImage from "../../utils/UploadImage";
import MultipleImageUpload from "../../utils/MultipleImageUpload";
import { Select } from "antd";
import { getRegionsData, getStoresData } from "../../services/shipmentApi";
import {
  CreateShipment,
  getAllRoutes,
  getRegionRoutes,
  getStoresByRouteId,
  getVihiclesByRegionId,
} from "../../api/apiService";
import { useUser } from "../../context/UserContext";

const AddShipmentModal = ({
  showAddShipment,
  setShowAddShipment,
  setShipments,
  shipments,
  defaultFactoryId,
  defaultRegionId,
  defaultRouteId,
}) => {
  const [formData, setFormData] = useState({
    bakeryType: "",
    quantity: "",
    vehicleNumber: "",
    vehicle_id: "",
    exitDateTime: "",
    shift: "am",
    remarks: "",
    distributor_id: 2, // Default distributor ID
    region_id: 1, // Default region ID
    production_id: 1, // Default production ID
    route_id: null, // Will be set when store is selected
    status: "created",
    quality_status: "pending",
  });

  const { currentUser, hasRole } = useUser();

  const [selectedRegionId, setSelectedRegionId] = useState(
    defaultRegionId || 1
  );
  const [selectedStoreId, setSelectedStoreId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [regions, setRegions] = useState([]);
  const [stores, setStores] = useState([]);
  const [vehicles, setVehicles] = useState([]);
  const [regionRoutes, setRouteRegion] = useState([]);
  const [selectedRouteStores , setSelectedRouteStores]  = useState([])

  const getSelectedRegionRoutes = async () => {
    try {
      const data = await getRegionRoutes(currentUser?.user?.region_id);

      setRouteRegion(data?.data);
      console.log("routesData", data.data);
    } catch (err) {
      console.error("Error fetching routes:", err);
    }
  };

  useEffect(() => {
    getSelectedRegionRoutes();
  }, []);

  // Fetch regions and stores on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [regionsResult, storesResult] = await Promise.all([
          getRegionsData(),
          getStoresData(),
        ]);

        if (regionsResult.error) {
          console.error("Error fetching regions:", regionsResult.error);
        } else {
          setRegions(regionsResult.regions);
        }

        if (storesResult.error) {
          console.error("Error fetching stores:", storesResult.error);
        } else {
          setStores(storesResult.stores);
        }
      } catch (err) {
        console.error("Error fetching data:", err);
        setError("Failed to load regions and stores data");
      }
    };

    if (showAddShipment) {
      fetchData();
    }
  }, [showAddShipment]);

  // Fetch vehicles when region changes and modal is open
  useEffect(() => {
    const fetchVehicles = async () => {
      try {
        const regionIdForVehicles =
          hasRole && hasRole("admin")
            ? selectedRegionId
            : currentUser?.user?.region_id;
        const data = await getVihiclesByRegionId(regionIdForVehicles);
        setVehicles(
          Array.isArray(data?.data)
            ? data.data
            : Array.isArray(data)
            ? data
            : []
        );
      } catch (err) {
        console.error("Error fetching vehicles:", err);
      }
    };

    if (showAddShipment && (selectedRegionId || currentUser?.user?.region_id)) {
      fetchVehicles();
    }
  }, [showAddShipment, selectedRegionId, currentUser, hasRole]);

  // If not admin, lock region to user's region id
  useEffect(() => {
    if (!showAddShipment) return;
    const userRegion = currentUser?.user?.region_id;
    if (hasRole && !hasRole("admin") && userRegion) {
      setSelectedRegionId(userRegion);
      setFormData((prev) => ({ ...prev, region_id: userRegion }));
    }
  }, [showAddShipment, currentUser, hasRole]);

  const selectedRegion = useMemo(
    () => regions.find((r) => r.id === selectedRegionId) || null,
    [selectedRegionId, regions]
  );

  // Filter stores by selected region
  const regionStores = useMemo(() => {
    return stores.filter((store) => store.region_id === selectedRegionId);
  }, [stores, selectedRegionId]);

  // Close modal on ESC key press
  useState(() => {
    if (!showAddShipment) return;
    const handleKeyDown = (e) => {
      if (e.key === "Escape") {
        setShowAddShipment(false);
      }
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [showAddShipment]);

  return (
    <div
      onClick={(e) => {
        setShowAddShipment(false);
      }}
      className=" !m-0 fixed  !p-10 inset-0 top-0 bottom-0 left-0 right-0 bg-black bg-opacity-50 flex items-center justify-center z-50 "
    >
      <div
        onClick={(e) => {
          e.stopPropagation();
        }}
        className="bg-white rounded-xl shadow-xl w-full max-w-md max-h-[95vh] overflow-y-auto custom-scrollbar"
      >
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900">Add New Shipment</h2>
        </div>

        <div className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Bakery Type
            </label>
            <input
              type="text"
              value={formData.bakeryType}
              onChange={(e) =>
                setFormData({ ...formData, bakeryType: e.target.value })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              placeholder="e.g. Croissants"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Vehicle
            </label>
            <Select
              showSearch
              placeholder={
                selectedRegion ? "Select vehicle" : "Select region first"
              }
              value={formData.vehicle_id || null}
              onChange={(value) => {
                setFormData({ ...formData, vehicle_id: value });
              }}
              optionFilterProp="label"
              className="w-full"
              disabled={!selectedRegion}
              options={vehicles.map((v) => ({
                value: v.id,
                label: v.vehicle_number || v.plate_number || `Vehicle #${v.id}`,
              }))}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Quantity
            </label>
            <input
              onWheel={(e) => e.target.blur()}
              type="number"
              min={1}
              value={formData.quantity}
              onChange={(e) =>
                setFormData({ ...formData, quantity: parseInt(e.target.value) })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Exit Time
            </label>
            <input
              type="datetime-local"
              value={formData.exitDateTime}
              onChange={(e) =>
                setFormData({ ...formData, exitDateTime: e.target.value })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Shift
            </label>
            <Select
              value={formData.shift}
              onChange={(value) => setFormData({ ...formData, shift: value })}
              className="w-full"
              options={[
                { value: "am", label: "AM" },
                { value: "pm", label: "PM" },
                { value: "midnight", label: "Midnight" },
              ]}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Remarks
            </label>
            <textarea
              value={formData.remarks}
              onChange={(e) => setFormData({ ...formData, remarks: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              placeholder="Type remarks..."
              rows={3}
            />
          </div>

          {hasRole && hasRole("admin") ? (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Region
              </label>
              <Select
                showSearch
                placeholder="Select region"
                value={selectedRegionId}
                onChange={(value) => {
                  handleSelectRoute(value);
                  setSelectedRegionId(value);
                  setSelectedStoreId(null);
                  setFormData({
                    ...formData,
                    region_id: value,
                    route_id: null,
                  });
                }}
                optionFilterProp="label"
                className="w-full"
                options={regions.map((r) => ({ value: r.id, label: r.name }))}
              />
            </div>
          ) : null}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
               Route
            </label>
            <Select
              showSearch
              allowClear
              placeholder={
                selectedRegion ? "Select store" : "Select region first"
              }
              value={selectedStoreId}
              onChange={ async(value) => {
                setSelectedStoreId(value);
                setFormData({ ...formData, route_id: value });
                 const stores = await getStoresByRouteId(value);
                 console.log("stores" , stores)
                 setSelectedRouteStores(stores.stores || []);
              }}
              disabled={!selectedRegion}
              optionFilterProp="label"
              className="w-full"
              options={regionRoutes.map((route) => ({
                value: route?.id,
                label: `${route?.name}`,
              }))}
            />
          </div>
          {
            selectedRouteStores.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Stores
                </label>
                  {selectedRouteStores.length === 0 ? (
                    <div className="text-gray-500 italic px-4 py-2">No stores found for this route.</div>
                  ) : (
                    <ul className="pl-4 list-none flex items-center">
                      {selectedRouteStores?.map((store, i) => (
                        <div className="flex items-center" key={store.id}>
                          <li className="bg-orange-100 rounded-lg p-2 border border-orange-500">
                            {store.store_name}
                          </li>
                          {i < selectedRouteStores.length - 1 ? (
                            <ArrowRight className="text-sm w-4 mx-1 " />
                          ) : (
                            ""
                          )}
                        </div>
                      ))}
                    </ul>
                  )}
                  
                 

              </div>
            )
          }
        </div>

        {error && (
          <div className="p-6 border-t border-gray-200">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <div className="text-red-600 mr-3">
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <p className="text-red-800 text-sm">{error}</p>
              </div>
            </div>
          </div>
        )}

        <div className="p-6 border-t border-gray-200 flex gap-3">
          <button
            onClick={() => {
              setShowAddShipment(false);
              setError(null);
              setFormData({
                bakeryType: "",
                quantity: "",
                vehicleNumber: "",
                vehicle_id: "",
                exitDateTime: "",
                shift: "am",
                remarks: "",
                distributor_id: 2,
                region_id: 1,
                production_id: 1,
                route_id: null,
                status: "created",
                quality_status: "pending",
              });
            }}
            className="flex-1 px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            disabled={loading}
          >
            Cancel
          </button>
          <button
            onClick={async () => {
              // Validate form
              if (
                !formData.bakeryType ||
                !formData.quantity ||
                !formData.vehicle_id ||
                !formData.exitDateTime ||
                !selectedStoreId
              ) {
                setError("Please fill in all required fields");
                return;
              }

              try {
                setLoading(true);
                setError(null);

                const exit_time = (formData.exitDateTime || "").replace(
                  "T",
                  " "
                ); // YYYY-MM-DD HH:mm

                const payload = {
                  region_id: formData.region_id,
                  production_id: formData.production_id,
                  route_id: formData.route_id,
                  vehicle_id: formData.vehicle_id,
                  bakery_type: formData.bakeryType,
                  quantity: formData.quantity,
                  distributor_id: formData.distributor_id,
                  status: formData.status,
                  quality_status: formData.quality_status,
                  remarks: formData.remarks,
                  shift: formData.shift,
                  exit_time,
                };

                await CreateShipment(payload);
                window.location.reload();
              } catch (err) {
                console.error("Error adding shipment:", err);
                setError("Failed to add shipment. Please try again.");
              } finally {
                setLoading(false);
              }
            }}
            className="flex-1 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={loading}
          >
            {loading ? "Adding..." : "Add Shipment"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default AddShipmentModal;
