import React, { useState, useRef, useEffect } from "react";
import { X, Upload, Camera, AlertCircle, Loader2, CheckCircle } from "lucide-react";
import UploadImage from "../../utils/UploadImage";

const MarkDeliveredModal = ({ 
  isOpen, 
  onClose, 
  shipment, 
  onSubmit, 
  isLoading = false 
}) => {
  const [deliveryImages, setDeliveryImages] = useState([]);
  const [comment, setComment] = useState("");
  const [errors, setErrors] = useState({});
  const modalRef = useRef(null);
  const firstInputRef = useRef(null);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setDeliveryImages([]);
      setComment("");
      setErrors({});
      // Focus first input when modal opens
      setTimeout(() => {
        if (firstInputRef.current) {
          firstInputRef.current.focus();
        }
      }, 100);
    }
  }, [isOpen]);

  // Handle ESC key
  useEffect(() => {
    const handleEsc = (e) => {
      if (e.key === "Escape" && isOpen) {
        onClose();
      }
    };
    document.addEventListener("keydown", handleEsc);
    return () => document.removeEventListener("keydown", handleEsc);
  }, [isOpen, onClose]);

  // Focus trap
  useEffect(() => {
    if (!isOpen) return;

    const modal = modalRef.current;
    if (!modal) return;

    const focusableElements = modal.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    const handleTabKey = (e) => {
      if (e.key === "Tab") {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            lastElement.focus();
            e.preventDefault();
          }
        } else {
          if (document.activeElement === lastElement) {
            firstElement.focus();
            e.preventDefault();
          }
        }
      }
    };

    modal.addEventListener("keydown", handleTabKey);
    return () => modal.removeEventListener("keydown", handleTabKey);
  }, [isOpen]);

  const validateForm = () => {
    const newErrors = {};

    if (deliveryImages.length === 0) {
      newErrors.deliveryImages = "At least one delivery proof image is required";
    }

    if (comment.trim().length < 10) {
      newErrors.comment = "Comment must be at least 10 characters";
    }

    if (comment.length > 300) {
      newErrors.comment = "Comment must be 300 characters or less";
    }

    // Check file sizes for delivery images
    const oversizedFiles = deliveryImages.filter(file => file.size > 5 * 1024 * 1024);
    if (oversizedFiles.length > 0) {
      newErrors.fileSize = "Each image must be 5MB or less";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const commentData = {
      user: "Driver",
      message: comment.trim(),
      time: new Date().toLocaleTimeString(),
      timestamp: new Date()
    };

    onSubmit({
      shipmentId: shipment.id,
      deliveryImages,
      comment: commentData,
      timestamp: new Date()
    });
  };

  const handleDeliveryImageUpload = (images) => {
    setDeliveryImages(images);
    if (errors.deliveryImages) {
      setErrors(prev => ({ ...prev, deliveryImages: "" }));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed overflow-auto inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 p-4">
      <div 
        ref={modalRef}
        className="bg-white rounded-xl shadow-xl w-full max-w-3xl max-h-[90vh] overflow-y-auto"
        role="dialog"
        aria-modal="true"
        aria-labelledby="modal-title"
      >
        {/* Header */}
        <div className="p-6 border-b border-gray-200 flex justify-between items-center">
          <div className="text-right">
            <h2 id="modal-title" className="text-xl font-bold text-gray-900">
              Mark as Delivered
            </h2>
            <p className="text-gray-600 text-sm mt-1">
              Shipment #{shipment.id} • {shipment.bakeryType} • {shipment.branch}
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-2xl"
            aria-label="Close modal"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Delivery Images Section */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Camera className="w-5 h-5 text-green-600" />
              <h3 className="font-medium text-gray-900">
                Delivery Images <span className="text-red-500">*</span>
              </h3>
            </div>
            <p className="text-sm text-gray-600">
              Upload clear photos proving the items were delivered to the destination
            </p>
            
            <UploadImage
              onImagesUpload={handleDeliveryImageUpload}
              label=""
              placeholder="Click to upload delivery photos"
              multiple={true}
              maxImages={10}
            />
            
            {errors.deliveryImages && (
              <div className="flex items-center gap-2 text-red-600 text-sm">
                <AlertCircle className="w-4 h-4" />
                {errors.deliveryImages}
              </div>
            )}
            
            {deliveryImages.length > 0 && (
              <div className="text-sm text-green-600">
                ✓ {deliveryImages.length} delivery image(s) selected
              </div>
            )}
          </div>

          {/* Required Comment Section */}
          <div className="space-y-3">
            <label htmlFor="comment" className="block font-medium text-gray-900">
              Delivery Comment <span className="text-red-500">*</span>
            </label>
            <textarea
              ref={firstInputRef}
              id="comment"
              value={comment}
              onChange={(e) => {
                setComment(e.target.value);
                if (errors.comment) {
                  setErrors(prev => ({ ...prev, comment: "" }));
                }
              }}
              placeholder="Describe the delivery status, any issues, or additional notes (10-300 characters)"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
              rows={4}
              minLength={10}
              maxLength={300}
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>Required information about the delivery</span>
              <span>{comment.length}/300</span>
            </div>
            {errors.comment && (
              <div className="flex items-center gap-2 text-red-600 text-sm">
                <AlertCircle className="w-4 h-4" />
                {errors.comment}
              </div>
            )}
          </div>

          {/* File Size Error */}
          {errors.fileSize && (
            <div className="flex items-center gap-2 text-red-600 text-sm bg-red-50 p-3 rounded-lg">
              <AlertCircle className="w-4 h-4" />
              {errors.fileSize}
            </div>
          )}

          {/* Requirements Notice */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex gap-3">
              <AlertCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
              <div className="text-sm text-green-800">
                <p className="font-medium mb-2">📋 Requirements:</p>
                <ul className="space-y-1 text-xs">
                  <li>• At least 1 delivery image is required</li>
                  <li>• Comment must be 10-300 characters</li>
                  <li>• Each image must be 5MB or less</li>
                  <li>• Up to 10 delivery images allowed</li>
                  <li>• Images should clearly show the delivery completion</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-gray-100 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-200 transition-colors font-medium"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading || deliveryImages.length === 0 || comment.trim().length < 10}
              className="flex-1 bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center gap-2 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <CheckCircle className="w-4 h-4" />
                  Mark as Delivered
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default MarkDeliveredModal;
