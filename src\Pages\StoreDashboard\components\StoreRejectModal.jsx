import { X } from "lucide-react";
import { useState, useEffect } from "react";
import { useUser } from "../../../context/UserContext";
import { getVihiclesByRegionId } from "../../../api/apiService";
import UploadImage from "../../../utils/UploadImage";

const StoreRejectModal = ({ show, shipment, onCancel, onSubmit , loading }) => {
  const [vehicleNumber, setVehicleNumber] = useState("");
  const [reason, setReason] = useState("");
  const [rejectionImages, setRejectionImages] = useState([]);
  const [vehicles, setVehicles] = useState([]);
  const { currentUser } = useUser();
  const [deliveryDatetime, setDeliveryDatetime] = useState("");

  useEffect(() => {
    if (show) {
      setVehicleNumber("");
      setReason("");
      setRejectionImages([]);
      setDeliveryDatetime("");
    }
  }, [show]);

  // Fetch vehicles for user's region when modal opens
  useEffect(() => {
    const fetchVehicles = async () => {
      try {
        const regionId = currentUser?.user?.region_id;
        if (!regionId) return;
        const data = await getVihiclesByRegionId(regionId);
        const list = Array.isArray(data?.data)
          ? data.data
          : Array.isArray(data)
          ? data
          : [];
        setVehicles(list);
      } catch (err) {
        console.error("Failed to load vehicles", err);
        setVehicles([]);
      }
    };

    if (show) {
      fetchVehicles();
    }
  }, [show, currentUser]);

  if (!show) return null;

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log("rejectionImages" , rejectionImages)
    // return

    if ( !reason.trim() || !deliveryDatetime) return;
    onSubmit({
      vehicle_id: shipment?.vehicleId,
      reason: reason.trim(),
      images: rejectionImages,
      delivery_datetime: deliveryDatetime,
      store_id: currentUser?.user?.store_id || currentUser?.user?.storeId,
      updatedAt: new Date().toISOString(),
    });
  };

  return (
    <div
      className="fixed overflow-auto !m-0 inset-0 bg-black/60 backdrop-blur-sm flex items-start justify-center z-50 p-4"
      onClick={onCancel}
    >
      <div
        className="bg-white rounded-2xl shadow-2xl w-full max-w-lg overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-5 border-b border-gray-200">
          <div>
            <h2 className="text-lg font-bold text-gray-900">Reject Shipment</h2>
            <p className="text-sm text-gray-600">Shipment #{shipment?.id}</p>
          </div>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Body */}
        <form onSubmit={handleSubmit} className="p-5 space-y-4">
          <div>
            <label onClick={()=> console.log(shipment)} className="block text-sm font-medium text-gray-700 mb-1">
              Vehicle Number
            </label>
            <div>
              {shipment?.vehicleNumber}
            </div>
            {/* <select
            disabled
            
              value={shipment?.vehicleId}
              onChange={(e) => setVehicleNumber(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 bg-white"
              required
            >
              <option value="" disabled>
                {vehicles.length ? "Select vehicle" : "No vehicles found"}
              </option>
              {vehicles.map((v) => {
                const label = v.vehicle_number || v.plate_number || `Vehicle #${v.id}`;
                return (
                  <option key={v.id} value={label}>
                    {label}
                  </option>
                );
              })}
            </select> */}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Notes / Reason
            </label>
            <textarea
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder="Why is this shipment rejected?"
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 resize-y"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Delivery Date & Time
            </label>
            <input
              type="datetime-local"
              value={deliveryDatetime}
              onChange={(e) => setDeliveryDatetime(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
              required
            />
          </div>

          <div>
            <UploadImage
              onImagesUpload={setRejectionImages}
              label="Attach Photos "
              placeholder="Upload evidence images"
              multiple
              maxImages={5}
              className="mt-2"
            />
          </div>

          <div className="flex items-center justify-end gap-2 pt-2">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
             disabled={loading}
              type="submit"
              className="px-4 py-2 rounded-lg bg-red-600 text-white hover:bg-red-700"
            >
              Reject
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default StoreRejectModal;
