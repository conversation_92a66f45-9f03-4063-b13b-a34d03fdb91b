import {
  Table,
  Tag as AntdTag,
  Button,
  Space,
  Popconfirm,
  Input,
  Select,
  DatePicker,
  Row,
  Col,
  Card,
} from "antd";
import {
  Eye,
  Edit,
  Trash2,
  Search,
  Filter,
  X,
  Hash,
  Image,
  Package,
  Hash as NumberIcon,
  Building,
  Tag,
  Calendar,
  Settings,
  Search as SearchIcon,
  Filter as FilterIcon,
  Calendar as CalendarIcon,
  Clock,
  AlertTriangle,
} from "lucide-react";
import {
  SearchOutlined,
  FilterOutlined,
  ClearOutlined,
} from "@ant-design/icons";
import { useState, useMemo, useEffect } from "react";
import { useUser } from "../../../context/UserContext";
import DataTable, {
  getColumnDateProps,
  getColumnFiltersProps,
  getColumnNumberRange,
  getColumnSearchProps,
} from "../../../utils/DataTable";
import TableImage from "../../../utils/TableImage";
import dayjs from "dayjs";
import { formatDate } from "../../../lib/formateDate";
import { useNavigate } from "react-router-dom";
import { DeleteRegion } from "../../../api/apiService";
import { toast } from "react-toastify";

const { RangePicker } = DatePicker;

const RejionsTable = ({
  setSelectedShipment,
  data,
  setShipments,
  onEditRejion,
  loading,
  onAssignRejion,
  onDeleteRegion,
}) => {
  const { currentUser, setUser, clearUser, isLoggedIn, hasRole } = useUser();

  const navigate = useNavigate();

  // Filter states
  const [filters, setFilters] = useState({
    search: "",
    dateRange: null,
  });
  const [showFilters, setShowFilters] = useState(false);

  // Normalize regions dataset to rows suitable for the table
  const rows = useMemo(() => {
    return (data || []).map((r) => {
      const points = Array.isArray(r?.points) ? r.points : [];
      return {
        id: r.id,
        name: r.name,
        routes: r.routes,
        code: r.code,
        status: r.status,
        points,
        pointsCount: points.length,
        createdAt: r.createdAt || new Date(),
      };
    });
  }, [data]);

  // Sync filters with URLSearchParams
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const q = params.get("q") || "";
    const dateStart = params.get("dateStart");
    const dateEnd = params.get("dateEnd");
    setFilters((prev) => ({
      ...prev,
      search: q,
      dateRange:
        dateStart && dateEnd ? [dayjs(dateStart), dayjs(dateEnd)] : null,
    }));
  }, []);

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    if (filters.search) params.set("q", filters.search);
    else params.delete("q");
    if (filters.dateRange && filters.dateRange.length === 2) {
      params.set("dateStart", filters.dateRange[0].format("YYYY-MM-DD"));
      params.set("dateEnd", filters.dateRange[1].format("YYYY-MM-DD"));
    } else {
      params.delete("dateStart");
      params.delete("dateEnd");
    }
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    window.history.replaceState({}, "", newUrl);
  }, [filters.search, filters.dateRange]);

  // Reset filters
  const resetFilters = () => {
    setFilters({
      search: "",
      dateRange: null,
    });
  };

  // Update filter
  const updateFilter = (key, value) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const columns = [
    {
      title: (
        <div className="flex items-center gap-2">
          <Hash className="w-4 h-4 text-gray-600" />
          <span>ID</span>
        </div>
      ),
      dataIndex: "id",
      key: "id",
      fixed: true,
      render: (id) => (
        <div className="flex items-center gap-2">
          <Hash className="w-4 h-4 text-gray-600" />
          <span>{id}</span>
        </div>
      ),
      sorter: (a, b) => a.id - b.id,
      ...getColumnNumberRange("id"),
    },
    // Image column removed for regions dataset
    {
      title: (
        <div className="flex items-center gap-2">
          <Package className="w-4 h-4 text-gray-600" />
          <span className="font-bold">Name</span>
        </div>
      ),
      dataIndex: "name",
      key: "name",
      sorter: (a, b) => a.name.localeCompare(b.name),
      ...getColumnSearchProps("name"),
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <Tag className="w-4 h-4 text-gray-600" />
          <span>Routes</span>
        </div>
      ),
      dataIndex: "routes",
      key: "routes",
      render: (routes = []) => (
        <div className="flex flex-wrap  max-w-md gap-1 items-center">
          {Array.isArray(routes) && routes.length > 0 ? (
            [...routes].map((p, idx) => (
              <div key={idx} className=" not-target flex flex-col items-center">
                <div className="flex items-center px-3 text-xs font-semibold  text-gray-600 gap-1 border border-black p-1 rounded-md">
                  <span>{p.name}</span>
                </div>
              </div>
            ))
          ) : (
            <span className="text-xs text-gray-500">No Routes</span>
          )}
        </div>
      ),
    },

    {
      title: (
        <div className="flex items-center gap-2">
          <Package className="w-4 h-4 text-gray-600" />
          <span className="font-bold">Code</span>
        </div>
      ),
      dataIndex: "name",
      key: "name",
      sorter: (a, b) => a.code.localeCompare(b.code),
      ...getColumnSearchProps("code"),
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <Package className="w-4 h-4 text-gray-600" />
          <span className="font-bold">status</span>
        </div>
      ),
      dataIndex: "status",
      key: "status",
      render: (status) => (
        <div
          className={`flex items-center ${
            status == "Active" ? "text-green-600" : "text-red-600"
          } gap-2`}
        >
          <span>{status == "Active" ? "Active" : "Inactive"}</span>
        </div>
      ),
    },

    // Branch, Status and 24h Progress columns removed for regions dataset
    {
      title: (
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-gray-600" />
          <span>Created At</span>
        </div>
      ),
      dataIndex: "createdAt",
      key: "createdAt",
      render: (text, record) => {
        return (
          <div className="flex flex-col gap-2">
            <div className="">
              {formatDate(record.createdAt).split("at")[0]}
            </div>
            <div className="text-xs text-gray-500 ">
              {formatDate(record.createdAt).split("at")[1]}
            </div>
          </div>
        );
      },
      ...getColumnDateProps("createdAt"),
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <Settings className="w-4 h-4 text-gray-600" />
          <span>Actions</span>
        </div>
      ),
      key: "actions",
      render: (_, record) => (
        <Space size="middle" className="not-target">
          <Button
            onClick={() => onEditRejion && onEditRejion(record)}
            className="text-blue-600 hover:text-blue-700"
          >
            <Edit className="w-4 h-4" />
          </Button>

          {["super_admin"].includes(currentUser?.user?.role) && (
            <Popconfirm
              className="not-target"
              rootClassName="not-target"
              title="Delete the region?"
              
              description="Are you sure to delete this region?"
              onConfirm={async () => {
                try {
                  await DeleteRegion(record.id).then(e=>{
                    console.log(e);
                    toast.success("Region deleted successfully");
                    onDeleteRegion();
                  })
                  
                  
                  
                } catch (error) {
                  // Optionally show error feedback here
                  console.error("Failed to delete region", error);
                }
              }}
              onCancel={() => {}}
              okText="Delete"
              okButtonProps={{
                danger: true,
              }}
              cancelText="No"
            >
              <Button danger>
                <Trash2 className="w-4 h-4" />
              </Button>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200">
      <div className="p-6">
        <div className="space-y-4">
          {/* Data Table */}
          <div className="overflow-x-auto">
            <DataTable
              loading={loading}
              searchPlaceholder={"Search Rejion"}
              searchText={filters.search}
              onSearchTextChange={(val) =>
                setFilters((prev) => ({ ...prev, search: val }))
              }
              onAddClick={() => console.log("add region")}
              table={{
                header: columns,
                rows: rows,
              }}
              rowClassName={(record) =>
                "hover:bg-orange-100 hover:scale-[1.01] transition-all  cursor-pointer"
              }
              onRow={(record, rowIndex) => {
                return {
                  onClick: (e) => {
                    if (e.target.closest(".not-target")) {
                      return;
                    }

                    navigate(`/regions/${record?.id}`);
                  },
                };
              }}
            />
          </div>

          {/* Edit modal removed for regions dataset */}
        </div>
      </div>
    </div>
  );
};

export default RejionsTable;
