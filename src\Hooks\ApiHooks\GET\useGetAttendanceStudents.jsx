import { useState, useEffect } from 'react';
import { getAttendanceStudents } from '../../../api/apiService';

export const useGetAttendanceStudents = (params = {}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [data, setData] = useState(null);

  const fetchAttendanceStudents = async (newParams = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await getAttendanceStudents({ ...params, ...newParams });
      setData(response.data);
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.message || err.message || 'حدث خطأ أثناء جلب بيانات حضور الطلاب';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Auto-fetch on mount if required params are provided
  useEffect(() => {
    if (params.user_id && params.generation_id && params.lesson_id) {
      fetchAttendanceStudents();
    }
  }, [params.user_id, params.generation_id, params.lesson_id]);

  return {
    attendanceData: data?.data || null,
    students: data?.data?.students || [],
    totalStudents: data?.data?.total_students || 0,
    loading,
    error,
    fetchAttendanceStudents,
    reset: () => {
      setError(null);
      setData(null);
    }
  };
};
