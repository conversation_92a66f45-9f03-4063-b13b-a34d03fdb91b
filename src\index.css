

@import 'react-image-gallery/styles/css/image-gallery.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* React Image Gallery Styles */

/* RTL افتراضي داخل هذا المحرر */
.ql-rtl .ql-editor {
  direction: rtl;
  text-align: right;
}

/* خلي الـ placeholder يبدأ من اليمين */
.ql-rtl .ql-editor.ql-blank::before {
  left: auto;
  right: 12px; /* عدّل المسافة حسب ذوقك */
  text-align: right;
}

button.ant-btn.ant-btn-color-primary {
  background: #0f7490 !important;
}

.no-padding-modal .ant-modal-content,
.no-padding-modal .ant-modal-body {
  padding: 0 !important;
  @apply rounded-3xl;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* RTL Pagination Styles */
.rtl-pagination .ant-pagination-item,
.rtl-pagination .ant-pagination-prev,
.rtl-pagination .ant-pagination-next,
.rtl-pagination .ant-pagination-jump-prev,
.rtl-pagination .ant-pagination-jump-next {
  direction: rtl;
}

.rtl-pagination .ant-pagination-total-text {
  direction: rtl;
  text-align: right;
}

.rtl-pagination .ant-pagination-options {
  direction: rtl;
}

.rtl-pagination .ant-pagination-options .ant-select {
  direction: rtl;
}


/*  custom scrollbar style  for an element */

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #555;
}