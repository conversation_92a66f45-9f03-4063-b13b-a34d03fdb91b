import { Select } from "antd";
import { X } from "lucide-react";
import { useState } from "react";

const AddNewStoreModal = ({
  setShowAddModal,
  onSubmit = () => null,
}) => {
  const [formData, setFormData] = useState({
    name: "",
    nameEn: "",
    address: "",
    phone: "",
    email: "",
    manager: "",
    managerPhone: "",
    storeCode: "",
    openingHours: "07:00 - 22:00",
    status: "active",
    region: "",
  });

  const [errors, setErrors] = useState({});

  const regionOptions = [
    { value: "cairo", label: "Cairo" },
    { value: "giza", label: "Giza" },
    { value: "alexandria", label: "Alexandria" },
  ];

  const validateForm = () => {
    const newErrors = {};
    if (!formData.name.trim()) newErrors.name = "Store name is required";
    if (!formData.storeCode.trim())
      newErrors.storeCode = "Store code is required";
    if (!formData.region) newErrors.region = "Rejion is required";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      const newBranch = {
        id: stores.length + 1,
        ...formData,
        establishedDate: new Date().toISOString().split("T")[0],
        totalShipments: 0,
        monthlyShipments: 0,
        averageDeliveryTime: "0 hours",
        lastShipment: null,
        coordinates: { lat: 30.0444, lng: 31.2357 },
        performance: {
          onTimeDelivery: 0,
          qualityScore: 0,
          customerSatisfaction: 0,
        },
      };
      onSubmit(newBranch);
      // setBranches([...stores, newBranch]);
      setShowAddModal(false);
    }
  };

  return (
    <div className="fixed overflow-auto  !m-0 inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-xl font-bold text-gray-900">Add New Store</h2>
          <button
            onClick={() => setShowAddModal(false)}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Store Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) =>
                  setFormData({ ...formData, name: e.target.value })
                }
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-orange-500 ${
                  errors.name ? "border-red-300" : "border-gray-300"
                }`}
                placeholder="Example: Maadi Store"
              />
              {errors.name && (
                <p className="text-red-500 text-sm mt-1">{errors.name}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Store Code <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formData.storeCode}
                onChange={(e) =>
                  setFormData({ ...formData, storeCode: e.target.value })
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500"
                placeholder="12345"
              />
              {errors.storeCode && (
                <p className="text-red-500 text-sm mt-1">{errors.storeCode}</p>
              )}
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Rejion <span className="text-red-500">*</span>
              </label>
              <Select
                size="large"
                placeholder="Select Rejion"
                value={formData.region || undefined}
                onChange={(value) =>
                  setFormData({ ...formData, region: value })
                }
                options={regionOptions}
                className="w-full"
              />
              {errors.region && (
                <p className="text-red-500 text-sm mt-1">{errors.region}</p>
              )}
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Address Location
              </label>
              <input
                value={formData.address}
                onChange={(e) =>
                  setFormData({ ...formData, address: e.target.value })
                }
                rows="3"
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-orange-500 border-gray-300`}
                placeholder="Complete store address"
              />
            </div>

            {/* <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number <span className="text-red-500">*</span>
                </label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) =>
                    setFormData({ ...formData, phone: e.target.value })
                  }
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-orange-500 ${
                    errors.phone ? "border-red-300" : "border-gray-300"
                  }`}
                  placeholder="+20 2 1234-5678"
                />
                {errors.phone && (
                  <p className="text-red-500 text-sm mt-1">{errors.phone}</p>
                )}
              </div> */}

            {/* <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) =>
                    setFormData({ ...formData, email: e.target.value })
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500"
                  placeholder="<EMAIL>"
                />
              </div> */}

            {/* <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Manager Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.manager}
                  onChange={(e) =>
                    setFormData({ ...formData, manager: e.target.value })
                  }
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-orange-500 ${
                    errors.manager ? "border-red-300" : "border-gray-300"
                  }`}
                  placeholder="Store manager name"
                />
                {errors.manager && (
                  <p className="text-red-500 text-sm mt-1">{errors.manager}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Manager Phone
                </label>
                <input
                  type="tel"
                  value={formData.managerPhone}
                  onChange={(e) =>
                    setFormData({ ...formData, managerPhone: e.target.value })
                    storeCode:"12345",
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500"
                  placeholder="+20 10 1234-5678"
                />
              </div> */}

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Opening Hours (optional)
              </label>
              <input
                type="text"
                value={formData.openingHours}
                onChange={(e) =>
                  setFormData({ ...formData, openingHours: e.target.value })
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500"
                placeholder="07:00 - 22:00"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Store Status (optional)
              </label>
              <select
                value={formData.status}
                onChange={(e) =>
                  setFormData({ ...formData, status: e.target.value })
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500"
              >
                <option value="active">Active</option>
                <option value="maintenance">Maintenance</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>

          <div className="flex gap-3 mt-8">
            <button
              type="button"
              onClick={() => setShowAddModal(false)}
              className="flex-1 px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
            >
              Add Store
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddNewStoreModal;
