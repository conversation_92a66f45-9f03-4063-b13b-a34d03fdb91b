import { useState, useCallback } from 'react';
import { createExam } from '../../../api/apiService';

export const useCreateExam = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const createExamData = useCallback(async (examData) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await createExam(examData);
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.message || err.message || 'حدث خطأ أثناء إنشاء الامتحان';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const reset = useCallback(() => setError(null), []);

  return {
    createExam: createExamData,
    loading,
    error,
    reset
  };
};
