import { useState, useCallback } from 'react';
import { updateExam } from '../../../api/apiService';

export const useUpdateExam = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const updateExamData = useCallback(async (examData) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await updateExam(examData);
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.message || err.message || 'حدث خطأ أثناء تحديث الامتحان';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const reset = useCallback(() => setError(null), []);

  return {
    updateExam: updateExamData,
    loading,
    error,
    reset
  };
};
