import { useState, useEffect } from 'react';
import { getStudents } from '../../../api/apiService';

export const useGetStudents = (params = {}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [data, setData] = useState(null);

  const fetchStudents = async (newParams = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await getStudents({ ...params, ...newParams });
      setData(response.data);
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.message || err.message || 'حدث خطأ أثناء جلب بيانات الطلاب';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Auto-fetch on mount if params are provided
  useEffect(() => {
    if (params.user_id) {
      fetchStudents();
    }
  }, [params.user_id, params.generation_id, params.collection_id]);

  return {
    students: data?.data || [],
    loading,
    error,
    fetchStudents,
    reset: () => {
      setError(null);
      setData(null);
    }
  };
};
