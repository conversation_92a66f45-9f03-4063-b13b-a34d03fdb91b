import React, { useState } from "react";
import {
  User,
  Package,
  Truck,
  Store,
  Camera,
  MessageSquare,
  Clock,
  CheckCircle,
  AlertCircle,
  Plus,
  Eye,
  Edit,
  Trash2,
  Bar<PERSON>hart3,
  <PERSON><PERSON>s,
  LogOut,
  Bell,
} from "lucide-react";
import UploadImage from "../utils/UploadImage";

// Mock data for demonstration
const initialShipments = [
  {
    id: 1,
    bakeryType: "White Bread",
    quantity: 100,
    driver: "<PERSON>",
    branch: "Maadi Branch",
    status: "In Transit",
    factoryImage: null,
    driverPickupImage: null,
    driverDeliveryImage: null,
    branchReceiveImage: null,
    displayImage: null,
    comments: [],
    timestamps: {
      created: new Date("2024-01-15T08:00:00"),
      driverPickup: new Date("2024-01-15T09:00:00"),
      delivery: null,
      branchReceive: null,
      display: null,
    },
  },
  {
    id: 2,
    bakeryType: "Cakes",
    quantity: 50,
    driver: "<PERSON><PERSON><PERSON>",
    branch: "Riyadh Branch",
    status: "Delivered",
    factoryImage: null,
    driverPickupImage: null,
    driverDeliveryImage: null,
    branchReceiveImage: null,
    displayImage: null,
    comments: [
      { user: "Driver", message: "Received in good condition", time: "10:30" },
    ],
    timestamps: {
      created: new Date("2024-01-15T07:00:00"),
      driverPickup: new Date("2024-01-15T08:00:00"),
      delivery: new Date("2024-01-15T10:30:00"),
      branchReceive: new Date("2024-01-15T10:35:00"),
      display: null,
    },
  },
];

const users = [
  { id: 1, name: "Ahmed Mohamed", role: "driver", branch: null },
  { id: 2, name: "Fatima Ali", role: "branch", branch: "Maadi Branch" },
  { id: 3, name: "Mahmoud Hassan", role: "factory", branch: null },
];

const branches = [
  "Maadi Branch",
  "Riyadh Branch",
  "Zamalek Branch",
  "Engineers Branch",
];

const BakeryTrackingSystem = () => {
  const [currentUser, setCurrentUser] = useState({
    role: "super_admin",
    name: "General Manager",
  });
  const [currentPage, setCurrentPage] = useState("dashboard");
  const [shipments, setShipments] = useState(initialShipments);
  const [selectedShipment, setSelectedShipment] = useState(null);
  const [showAddShipment, setShowAddShipment] = useState(false);
  const [showUserManagement, setShowUserManagement] = useState(false);

  // Login Component
  const LoginPage = () => {
    const [loginMethod, setLoginMethod] = useState("username");

    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-400 to-red-500 flex items-center justify-center p-4">
        <div className="bg-white rounded-2xl shadow-2xl p-8 w-full max-w-md">
          <div className="text-center mb-8">
            <div className="bg-orange-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4">
              <Package className="w-10 h-10 text-orange-600" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900">
              Bakery Tracking System
            </h1>
            <p className="text-gray-600 mt-2">Login to the system</p>
          </div>

          <div className="mb-6">
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setLoginMethod("username")}
                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                  loginMethod === "username"
                    ? "bg-white text-gray-900 shadow-sm"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                Username
              </button>
              <button
                onClick={() => setLoginMethod("phone")}
                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                  loginMethod === "phone"
                    ? "bg-white text-gray-900 shadow-sm"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                Phone Number
              </button>
            </div>
          </div>

          {loginMethod === "username" ? (
            <form className="space-y-4">
              <input
                type="text"
                placeholder="Username"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 "
              />
              <input
                type="password"
                placeholder="Password"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 "
              />
              <button
                type="submit"
                onClick={() => setCurrentPage("dashboard")}
                className="w-full bg-orange-600 text-white py-3 px-4 rounded-lg hover:bg-orange-700 transition-colors font-medium"
              >
                Login
              </button>
            </form>
          ) : (
            <form className="space-y-4">
              <input
                type="tel"
                placeholder="Phone Number"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 "
              />
              <button
                type="button"
                className="w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors font-medium flex items-center justify-center gap-2"
              >
                Send verification code via WhatsApp
                <MessageSquare className="w-4 h-4" />
              </button>
              <input
                type="text"
                placeholder="Verification Code"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 "
              />
              <button
                type="submit"
                onClick={() => setCurrentPage("dashboard")}
                className="w-full bg-orange-600 text-white py-3 px-4 rounded-lg hover:bg-orange-700 transition-colors font-medium"
              >
                Confirm Login
              </button>
            </form>
          )}

          <div className="mt-8 text-center">
            <p className="text-sm text-gray-500">
              For testing, choose user role:
            </p>
            <div className="flex flex-wrap gap-2 mt-2 justify-center">
              {[
                { role: "super_admin", name: "General Manager" },
                { role: "factory", name: "Factory" },
                { role: "driver", name: "Driver" },
                { role: "branch", name: "Branch" },
              ].map((user) => (
                <button
                  key={user.role}
                  onClick={() => {
                    setCurrentUser({ role: user.role, name: user.name });
                    setCurrentPage("dashboard");
                  }}
                  className="px-3 py-1 bg-gray-200 text-gray-700 rounded-full text-xs hover:bg-gray-300 transition-colors"
                >
                  {user.name}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Dashboard Components
  const SuperAdminDashboard = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="bg-blue-100 p-3 rounded-full">
              <Package className="w-6 h-6 text-blue-600" />
            </div>
            <div className="mr-4">
              <p className="text-2xl font-bold text-gray-900">
                {shipments.length}
              </p>
              <p className="text-gray-600">Total Shipments</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="bg-green-100 p-3 rounded-full">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
            <div className="mr-4">
              <p className="text-2xl font-bold text-gray-900">
                {shipments.filter((s) => s.status === "Delivered").length}
              </p>
              <p className="text-gray-600">Delivered</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="bg-yellow-100 p-3 rounded-full">
              <Truck className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="mr-4">
              <p className="text-2xl font-bold text-gray-900">
                {shipments.filter((s) => s.status === "In Transit").length}
              </p>
              <p className="text-gray-600">In Transit</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="bg-purple-100 p-3 rounded-full">
              <Clock className="w-6 h-6 text-purple-600" />
            </div>
            <div className="mr-4">
              <p className="text-2xl font-bold text-gray-900">2.5 hours</p>
              <p className="text-gray-600">Average Transit Time</p>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900">Current Shipments</h2>
        </div>
        <div className="p-6">
          <ShipmentsList />
        </div>
      </div>
    </div>
  );

  const FactoryDashboard = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Factory Dashboard</h1>
        <button
          onClick={() => setShowAddShipment(true)}
          className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          Add New Shipment
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="bg-blue-100 p-3 rounded-full">
              <Package className="w-6 h-6 text-blue-600" />
            </div>
            <div className="mr-4">
              <p className="text-2xl font-bold text-gray-900">
                {shipments.length}
              </p>
              <p className="text-gray-600">Outgoing Shipments</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="bg-yellow-100 p-3 rounded-full">
              <Truck className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="mr-4">
              <p className="text-2xl font-bold text-gray-900">
                {shipments.filter((s) => s.status === "In Transit").length}
              </p>
              <p className="text-gray-600">In Transit</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="bg-green-100 p-3 rounded-full">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
            <div className="mr-4">
              <p className="text-2xl font-bold text-gray-900">
                {shipments.filter((s) => s.status === "Delivered").length}
              </p>
              <p className="text-gray-600">Completed</p>
            </div>
          </div>
        </div>
      </div>

      <ShipmentsList />
    </div>
  );

  const DriverDashboard = () => (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">Driver Dashboard</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="bg-orange-100 p-3 rounded-full">
              <Package className="w-6 h-6 text-orange-600" />
            </div>
            <div className="mr-4">
              <p className="text-2xl font-bold text-gray-900">
                {
                  shipments.filter(
                    (s) =>
                      s.status === "Pending Pickup" || s.status === "In Transit"
                  ).length
                }
              </p>
              <p className="text-gray-600">Assigned Shipments</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="bg-green-100 p-3 rounded-full">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
            <div className="mr-4">
              <p className="text-2xl font-bold text-gray-900">
                {shipments.filter((s) => s.status === "Delivered").length}
              </p>
              <p className="text-gray-600">Delivered</p>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900">
            Shipments to Pick Up
          </h2>
        </div>
        <div className="divide-y divide-gray-200">
          {shipments
            .filter((s) => s.driver === currentUser?.user?.name)
            .map((shipment) => (
              <div key={shipment.id} className="p-6 hover:bg-gray-50">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-medium text-gray-900">
                      {shipment.bakeryType}
                    </h3>
                    <p className="text-gray-600">
                      Quantity: {shipment.quantity}
                    </p>
                    <p className="text-gray-600">
                      Destination: {shipment.branch}
                    </p>
                    <span
                      className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-2 ${
                        shipment.status === "In Transit"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-blue-100 text-blue-800"
                      }`}
                    >
                      {shipment.status}
                    </span>
                  </div>
                  <div className="flex gap-2">
                    <button className="bg-green-600 text-white px-3 py-1 rounded-md text-sm hover:bg-green-700 transition-colors flex items-center gap-1">
                      <Camera className="w-3 h-3" />
                      Upload Image
                    </button>
                    <button className="bg-blue-600 text-white px-3 py-1 rounded-md text-sm hover:bg-blue-700 transition-colors">
                      Details
                    </button>
                  </div>
                </div>
              </div>
            ))}
        </div>
      </div>
    </div>
  );

  const BranchDashboard = () => (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">Branch Dashboard</h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="bg-blue-100 p-3 rounded-full">
              <Package className="w-6 h-6 text-blue-600" />
            </div>
            <div className="mr-4">
              <p className="text-2xl font-bold text-gray-900">
                {shipments.filter((s) => s.status === "Delivered").length}
              </p>
              <p className="text-gray-600">Received Shipments</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="bg-purple-100 p-3 rounded-full">
              <Store className="w-6 h-6 text-purple-600" />
            </div>
            <div className="mr-4">
              <p className="text-2xl font-bold text-gray-900">0</p>
              <p className="text-gray-600">On Display</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="bg-green-100 p-3 rounded-full">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
            <div className="mr-4">
              <p className="text-2xl font-bold text-gray-900">0</p>
              <p className="text-gray-600">Sold</p>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900">
            Incoming Shipments
          </h2>
        </div>
        <div className="divide-y divide-gray-200">
          {shipments
            .filter((s) => s.status === "Delivered")
            .map((shipment) => (
              <div key={shipment.id} className="p-6 hover:bg-gray-50">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-medium text-gray-900">
                      {shipment.bakeryType}
                    </h3>
                    <p className="text-gray-600">
                      Quantity: {shipment.quantity}
                    </p>
                    <p className="text-gray-600">Driver: {shipment.driver}</p>
                    <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-2 bg-green-100 text-green-800">
                      {shipment.status}
                    </span>
                  </div>
                  <div className="flex gap-2">
                    <button className="bg-green-600 text-white px-3 py-1 rounded-md text-sm hover:bg-green-700 transition-colors flex items-center gap-1">
                      <Camera className="w-3 h-3" />
                      Upload Image
                    </button>
                    <button className="bg-blue-600 text-white px-3 py-1 rounded-md text-sm hover:bg-blue-700 transition-colors">
                      Update Status
                    </button>
                  </div>
                </div>
              </div>
            ))}
        </div>
      </div>
    </div>
  );

  // Shipments List Component
  const ShipmentsList = () => (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead>
          <tr className="border-b border-gray-200">
            <th className=" py-3 px-4 font-medium text-gray-700">
              Bakery Type
            </th>
            <th className=" py-3 px-4 font-medium text-gray-700">
              Quantity
            </th>
            <th className=" py-3 px-4 font-medium text-gray-700">
              Driver
            </th>
            <th className=" py-3 px-4 font-medium text-gray-700">
              Branch
            </th>
            <th className=" py-3 px-4 font-medium text-gray-700">
              Status
            </th>
            <th className=" py-3 px-4 font-medium text-gray-700">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {shipments.map((shipment) => (
            <tr key={shipment.id} className="hover:bg-gray-50">
              <td className="py-3 px-4">{shipment.bakeryType}</td>
              <td className="py-3 px-4">{shipment.quantity}</td>
              <td className="py-3 px-4">{shipment.driver}</td>
              <td className="py-3 px-4">{shipment.branch}</td>
              <td className="py-3 px-4">
                <span
                  className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    shipment.status === "Delivered"
                      ? "bg-green-100 text-green-800"
                      : shipment.status === "In Transit"
                      ? "bg-yellow-100 text-yellow-800"
                      : "bg-blue-100 text-blue-800"
                  }`}
                >
                  {shipment.status}
                </span>
              </td>
              <td className="py-3 px-4">
                <button
                  onClick={() => setSelectedShipment(shipment)}
                  className="text-blue-600 hover:text-blue-700 mr-2"
                >
                  <Eye className="w-4 h-4" />
                </button>
                {currentUser?.user?.role === "super_admin" && (
                  <>
                    <button className="text-gray-600 hover:text-gray-700 mr-2">
                      <Edit className="w-4 h-4" />
                    </button>
                    <button className="text-red-600 hover:text-red-700">
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </>
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  // Add Shipment Modal
  const AddShipmentModal = () => {
    const [formData, setFormData] = useState({
      bakeryType: "",
      quantity: "",
      driver: "",
      branch: "",
      image: null,
    });

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-xl shadow-xl w-full max-w-md">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-bold text-gray-900">
              Add New Shipment
            </h2>
          </div>

          <div className="p-6 space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Bakery Type
              </label>
              <input
                type="text"
                value={formData.bakeryType}
                onChange={(e) =>
                  setFormData({ ...formData, bakeryType: e.target.value })
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                placeholder="e.g. White Bread"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Quantity
              </label>
              <input
                type="number"
                value={formData.quantity}
                onChange={(e) =>
                  setFormData({ ...formData, quantity: e.target.value })
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Driver
              </label>
              <select
                value={formData.driver}
                onChange={(e) =>
                  setFormData({ ...formData, driver: e.target.value })
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              >
                <option value="">Select Driver</option>
                {users
                  .filter((u) => u.role === "driver")
                  .map((driver) => (
                    <option key={driver.id} value={driver.name}>
                      {driver.name}
                    </option>
                  ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Branch
              </label>
              <select
                value={formData.branch}
                onChange={(e) =>
                  setFormData({ ...formData, branch: e.target.value })
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              >
                <option value="">Select Branch</option>
                {branches.map((branch) => (
                  <option key={branch} value={branch}>
                    {branch}
                  </option>
                ))}
              </select>
            </div>

            <UploadImage />
          </div>

          <div className="p-6 border-t border-gray-200 flex gap-3">
            <button
              onClick={() => setShowAddShipment(false)}
              className="flex-1 px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={() => {
                const newShipment = {
                  id: shipments.length + 1,
                  ...formData,
                  quantity: parseInt(formData.quantity),
                  status: "Pending Pickup",
                  factoryImage: null,
                  driverPickupImage: null,
                  driverDeliveryImage: null,
                  branchReceiveImage: null,
                  displayImage: null,
                  comments: [],
                  timestamps: {
                    created: new Date(),
                    driverPickup: null,
                    delivery: null,
                    branchReceive: null,
                    display: null,
                  },
                };
                setShipments([...shipments, newShipment]);
                setShowAddShipment(false);
              }}
              className="flex-1 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
            >
              Add Shipment
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Shipment Details Modal
  const ShipmentDetailsModal = () => {
    if (!selectedShipment) return null;

    const stages = [
      {
        key: "created",
        label: "Issued from Factory",
        icon: Package,
        status: "completed",
      },
      {
        key: "driverPickup",
        label: "Driver Pickup",
        icon: Truck,
        status: selectedShipment.timestamps.driverPickup
          ? "completed"
          : "pending",
      },
      {
        key: "delivery",
        label: "Delivery to Branch",
        icon: Store,
        status: selectedShipment.timestamps.delivery ? "completed" : "pending",
      },
      {
        key: "branchReceive",
        label: "Branch Receipt",
        icon: CheckCircle,
        status: selectedShipment.timestamps.branchReceive
          ? "completed"
          : "pending",
      },
      {
        key: "display",
        label: "Store Display",
        icon: Store,
        status: selectedShipment.timestamps.display ? "completed" : "pending",
      },
    ];

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
          <div className="p-6 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-xl font-bold text-gray-900">
              Shipment Details #{selectedShipment.id}
            </h2>
            <button
              onClick={() => setSelectedShipment(null)}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Shipment Info */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-medium text-gray-900 mb-3">
                  Shipment Information
                </h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Bakery Type:</span>
                    <span className="font-medium">
                      {selectedShipment.bakeryType}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Quantity:</span>
                    <span className="font-medium">
                      {selectedShipment.quantity}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Driver:</span>
                    <span className="font-medium">
                      {selectedShipment.driver}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Branch:</span>
                    <span className="font-medium">
                      {selectedShipment.branch}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Status:</span>
                    <span
                      className={`px-2 py-1 text-xs font-semibold rounded-full ${
                        selectedShipment.status === "Delivered"
                          ? "bg-green-100 text-green-800"
                          : selectedShipment.status === "In Transit"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-blue-100 text-blue-800"
                      }`}
                    >
                      {selectedShipment.status}
                    </span>
                  </div>
                </div>
              </div>

              {/* Timeline */}
              <div>
                <h3 className="font-medium text-gray-900 mb-3">
                  Shipment Stages
                </h3>
                <div className="space-y-4">
                  {stages.map((stage, index) => {
                    const Icon = stage.icon;
                    const isCompleted = stage.status === "completed";
                    const timestamp = selectedShipment.timestamps[stage.key];

                    return (
                      <div key={stage.key} className="flex items-start">
                        <div
                          className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                            isCompleted
                              ? "bg-green-100 text-green-600"
                              : "bg-gray-100 text-gray-400"
                          }`}
                        >
                          <Icon className="w-4 h-4" />
                        </div>
                        <div className="mr-3 flex-1">
                          <div className="flex items-center justify-between">
                            <p
                              className={`text-sm font-medium ${
                                isCompleted ? "text-gray-900" : "text-gray-500"
                              }`}
                            >
                              {stage.label}
                            </p>
                            {timestamp && (
                              <span className="text-xs text-gray-500">
                                {timestamp.toLocaleString("ar-EG")}
                              </span>
                            )}
                          </div>
                          {isCompleted && (
                            <p className="text-xs text-green-600 mt-1">
                              Completed
                            </p>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* Images Section */}
            <div className="mt-6">
              <h3 className="font-medium text-gray-900 mb-3">
                Uploaded Images
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                {[
                  "factoryImage",
                  "driverPickupImage",
                  "driverDeliveryImage",
                  "branchReceiveImage",
                  "displayImage",
                ].map((imageKey, index) => (
                  <div
                    key={imageKey}
                    className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300"
                  >
                    <div className="text-center">
                      <Camera className="w-6 h-6 text-gray-400 mx-auto mb-1" />
                      <p className="text-xs text-gray-500">
                        {index === 0 && "Factory Image"}
                        {index === 1 && "Driver Pickup"}
                        {index === 2 && "Driver Delivery"}
                        {index === 3 && "Branch Receipt"}
                        {index === 4 && "Store Display"}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Comments Section */}
            <div className="mt-6">
              <h3 className="font-medium text-gray-900 mb-3">Comments</h3>
              <div className="space-y-3">
                {selectedShipment.comments.length > 0 ? (
                  selectedShipment.comments.map((comment, index) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-3">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium text-sm text-gray-900">
                            {comment.user}
                          </p>
                          <p className="text-gray-600 text-sm mt-1">
                            {comment.message}
                          </p>
                        </div>
                        <span className="text-xs text-gray-500">
                          {comment.time}
                        </span>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-gray-500 text-sm">No comments</p>
                )}

                {/* Add comment form */}
                <div className="mt-4 flex gap-2">
                  <input
                    type="text"
                    placeholder="Add comment..."
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  />
                  <button className="px-4 py-2 bg-orange-600 text-white rounded-lg text-sm hover:bg-orange-700 transition-colors">
                    Add
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // User Management Modal
  const UserManagementModal = () => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl w-full max-w-6xl max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-xl font-bold text-gray-900">User Management</h2>
          <button
            onClick={() => setShowUserManagement(false)}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-lg font-medium text-gray-900">Users List</h3>
            <button className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center gap-2">
              <Plus className="w-4 h-4" />
              Add User
            </button>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className=" py-3 px-4 font-medium text-gray-700">
                    Name
                  </th>
                  <th className=" py-3 px-4 font-medium text-gray-700">
                    Role
                  </th>
                  <th className=" py-3 px-4 font-medium text-gray-700">
                    Branch
                  </th>
                  <th className=" py-3 px-4 font-medium text-gray-700">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {users.map((user) => (
                  <tr key={user.id} className="hover:bg-gray-50">
                    <td className="py-3 px-4">{user.name}</td>
                    <td className="py-3 px-4">
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          user.role === "driver"
                            ? "bg-blue-100 text-blue-800"
                            : user.role === "factory"
                            ? "bg-green-100 text-green-800"
                            : "bg-purple-100 text-purple-800"
                        }`}
                      >
                        {user.role === "driver"
                          ? "Driver"
                          : user.role === "factory"
                          ? "Factory"
                          : "Branch"}
                      </span>
                    </td>
                    <td className="py-3 px-4">{user.branch || "-"}</td>
                    <td className="py-3 px-4">
                      <button className="text-blue-600 hover:text-blue-700 mr-2">
                        <Edit className="w-4 h-4" />
                      </button>
                      <button className="text-red-600 hover:text-red-700">
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );

  // Reports Page
  const ReportsPage = () => (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">Reports</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex items-center mb-4">
            <BarChart3 className="w-6 h-6 text-blue-600 mr-3" />
            <h3 className="font-medium text-gray-900">Shipments Report</h3>
          </div>
          <p className="text-gray-600 text-sm mb-4">
            Comprehensive report on all shipments and statuses
          </p>
          <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
            Generate Report
          </button>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex items-center mb-4">
            <Clock className="w-6 h-6 text-yellow-600 mr-3" />
            <h3 className="font-medium text-gray-900">Performance Report</h3>
          </div>
          <p className="text-gray-600 text-sm mb-4">
            Analysis of transit times and operational efficiency
          </p>
          <button className="w-full bg-yellow-600 text-white py-2 px-4 rounded-lg hover:bg-yellow-700 transition-colors">
            Generate Report
          </button>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex items-center mb-4">
            <User className="w-6 h-6 text-green-600 mr-3" />
            <h3 className="font-medium text-gray-900">Users Report</h3>
          </div>
          <p className="text-gray-600 text-sm mb-4">
            User activity and performance in the system
          </p>
          <button className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
            Generate Report
          </button>
        </div>
      </div>

      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900">Quick Statistics</h2>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center">
              <p className="text-3xl font-bold text-blue-600">
                {shipments.length}
              </p>
              <p className="text-gray-600 mt-1">Total Shipments</p>
            </div>
            <div className="text-center">
              <p className="text-3xl font-bold text-green-600">
                {shipments.filter((s) => s.status === "Delivered").length}
              </p>
              <p className="text-gray-600 mt-1">Delivered</p>
            </div>
            <div className="text-center">
              <p className="text-3xl font-bold text-yellow-600">
                {shipments.filter((s) => s.status === "In Transit").length}
              </p>
              <p className="text-gray-600 mt-1">In Transit</p>
            </div>
            <div className="text-center">
              <p className="text-3xl font-bold text-purple-600">
                {branches.length}
              </p>
              <p className="text-gray-600 mt-1">Number of Branches</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // Sidebar Navigation
  const Sidebar = () => {
    const menuItems = {
      super_admin: [
        { id: "dashboard", label: "Dashboard", icon: BarChart3 },
        { id: "shipments", label: "Shipments", icon: Package },
        { id: "users", label: "Users", icon: User },
        { id: "branches", label: "Branches", icon: Store },
        { id: "reports", label: "Reports", icon: BarChart3 },
      ],
      factory: [
        { id: "dashboard", label: "Dashboard", icon: BarChart3 },
        { id: "shipments", label: "Shipments", icon: Package },
      ],
      driver: [
        { id: "dashboard", label: "Dashboard", icon: BarChart3 },
        { id: "myShipments", label: "My Shipments", icon: Truck },
      ],
      branch: [
        { id: "dashboard", label: "Dashboard", icon: BarChart3 },
        { id: "incoming", label: "Incoming Shipments", icon: Package },
      ],
    };

    return (
      <div className="w-64 bg-white shadow-sm border-l border-gray-200 h-full">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center">
            <div className="bg-orange-100 p-2 rounded-lg mr-3">
              <Package className="w-6 h-6 text-orange-600" />
            </div>
            <div>
              <h1 className="font-bold text-gray-900">Bakery Tracking</h1>
              <p className="text-sm text-gray-600">{currentUser?.user?.name}</p>
            </div>
          </div>
        </div>

        <nav className="p-4">
          <ul className="space-y-2">
            {menuItems[currentUser?.user?.role]?.map((item) => {
              const Icon = item.icon;
              return (
                <li key={item.id}>
                  <button
                    onClick={() => setCurrentPage(item.id)}
                    className={`w-full flex items-center px-3 py-2 rounded-lg  transition-colors ${
                      currentPage === item.id
                        ? "bg-orange-100 text-orange-700"
                        : "text-gray-700 hover:bg-gray-100"
                    }`}
                  >
                    <Icon className="w-5 h-5 ml-3" />
                    {item.label}
                  </button>
                </li>
              );
            })}
          </ul>
        </nav>

        <div className="absolute bottom-4 left-4 right-4">
          <button
            onClick={() => setCurrentPage("login")}
            className="w-full flex items-center px-3 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
          >
            <LogOut className="w-5 h-5 ml-3" />
            Logout
          </button>
        </div>
      </div>
    );
  };

  // Main App Layout
  const MainLayout = () => (
    <div className="h-screen flex bg-gray-50" >
      <Sidebar />

      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
          <div className="flex justify-between items-center">
            <h1 className="text-xl font-semibold text-gray-900">
              {currentPage === "dashboard" && "Dashboard"}
              {currentPage === "shipments" && "Shipment Management"}
              {currentPage === "users" && "User Management"}
              {currentPage === "reports" && "Reports"}
            </h1>

            <div className="flex items-center gap-4">
              <button className="relative p-2 text-gray-400 hover:text-gray-600">
                <Bell className="w-5 h-5" />
                <span className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                  3
                </span>
              </button>

              <div className="flex items-center gap-3">
                <div className="">
                  <p className="text-sm font-medium text-gray-900">
                    {currentUser?.user?.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {currentUser?.user?.role === "super_admin" && "General Manager"}
                    {currentUser?.user?.role === "factory" && "Factory User"}
                    {currentUser?.user?.role === "driver" && "Driver"}
                    {currentUser?.user?.role === "branch" && "Branch Manager"}
                  </p>
                </div>
                <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-orange-600" />
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto p-6">
          {currentPage === "dashboard" && (
            <>
              {currentUser?.user?.role === "super_admin" && <SuperAdminDashboard />}
              {currentUser?.user?.role === "factory" && <FactoryDashboard />}
              {currentUser?.user?.role === "driver" && <DriverDashboard />}
              {currentUser?.user?.role === "branch" && <BranchDashboard />}
            </>
          )}
          {currentPage === "shipments" && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h1 className="text-2xl font-bold text-gray-900">
                  Shipment Management
                </h1>
                {(currentUser?.user?.role === "super_admin" ||
                  currentUser?.user?.role === "factory") && (
                  <button
                    onClick={() => setShowAddShipment(true)}
                    className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center gap-2"
                  >
                    <Plus className="w-4 h-4" />
                    Add Shipment
                  </button>
                )}
              </div>
              <div className="bg-white rounded-xl shadow-sm border border-gray-200">
                <div className="p-6">
                  <ShipmentsList />
                </div>
              </div>
            </div>
          )}
          {currentPage === "users" && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h1 className="text-2xl font-bold text-gray-900">
                  User Management
                </h1>
                <button
                  onClick={() => setShowUserManagement(true)}
                  className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center gap-2"
                >
                  <Settings className="w-4 h-4" />
                  Manage Users
                </button>
              </div>
              <div className="bg-white rounded-xl shadow-sm border border-gray-200">
                <div className="p-6">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b border-gray-200">
                          <th className=" py-3 px-4 font-medium text-gray-700">
                            Name
                          </th>
                          <th className=" py-3 px-4 font-medium text-gray-700">
                            Role
                          </th>
                          <th className=" py-3 px-4 font-medium text-gray-700">
                            Branch
                          </th>
                          <th className=" py-3 px-4 font-medium text-gray-700">
                            Last Activity
                          </th>
                          <th className=" py-3 px-4 font-medium text-gray-700">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200">
                        {users.map((user) => (
                          <tr key={user.id} className="hover:bg-gray-50">
                            <td className="py-3 px-4 font-medium">
                              {user.name}
                            </td>
                            <td className="py-3 px-4">
                              <span
                                className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                  user.role === "driver"
                                    ? "bg-blue-100 text-blue-800"
                                    : user.role === "factory"
                                    ? "bg-green-100 text-green-800"
                                    : "bg-purple-100 text-purple-800"
                                }`}
                              >
                                {user.role === "driver"
                                  ? "Driver"
                                  : user.role === "factory"
                                  ? "Factory"
                                  : "Branch"}
                              </span>
                            </td>
                            <td className="py-3 px-4">{user.branch || "-"}</td>
                            <td className="py-3 px-4 text-gray-500">
                              2 hours ago
                            </td>
                            <td className="py-3 px-4">
                              <button className="text-blue-600 hover:text-blue-700 mr-2">
                                <Edit className="w-4 h-4" />
                              </button>
                              <button className="text-red-600 hover:text-red-700">
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          )}
          {currentPage === "reports" && <ReportsPage />}
        </main>
      </div>

      {/* Modals */}
      {showAddShipment && <AddShipmentModal />}
      {selectedShipment && <ShipmentDetailsModal />}
      {showUserManagement && <UserManagementModal />}
    </div>
  );

  // Main App Component
  return (
    <div className="min-h-screen bg-gray-50">
      {currentPage === "login" ? <LoginPage /> : <MainLayout />}
    </div>
  );
};

export default BakeryTrackingSystem;
