import { Bell, User, UserCircle, LogOut, Menu } from "lucide-react";
import React from "react";
import { Dropdown, Button } from "antd";
import { useNavigate } from "react-router-dom";
import { useUser } from "../context/UserContext";

export default function TopBar({ currentPage, currentUser, onToggleSidebar }) {
  const navigate = useNavigate();
  const { clearUser } = useUser();

  const handleLogout = () => {
    clearUser();
    navigate("/");
  };

  const handleProfileClick = () => {
    navigate("/profile");
  };

  const getRoleDisplayName = (role) => {
    const roleMap = {
      super_admin: "General Manager",
      factory: "Factory User",
      driver: "Driver",
      branch: "Branch Manager",
    };
    return roleMap[role] || role;
  };

  const dropdownItems = [
    {
      key: "profile",
      label: (
        <div className="flex items-center gap-2 px-2 py-1">
          <UserCircle className="w-4 h-4 text-gray-600" />
          <span>Profile</span>
        </div>
      ),
      onClick: handleProfileClick,
    },
    {
      type: "divider",
    },
    {
      key: "logout",
      label: (
        <div className="flex items-center gap-2 px-2 py-1 text-red-600">
          <LogOut className="w-4 h-4" />
          <span>Logout</span>
        </div>
      ),
      onClick: handleLogout,
    },
  ];

  return (
    <header className="bg-white border-b-2 border-orange-500 border-dashed shadow-2xl px-4 lg:px-6 py-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          {/* Mobile menu button */}
          <button
            onClick={onToggleSidebar}
            className="lg:hidden p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <Menu className="w-5 h-5 text-gray-600" />
          </button>

          <h1 className="text-lg lg:text-xl font-semibold text-gray-900 truncate">
            {currentPage === "dashboard" && "Dashboard"}
            {currentPage === "shipments" && "Shipment Management"}
            {currentPage === "users" && "User Management"}
            {currentPage === "reports" && "Reports"}
            {currentPage === "driverShipments" && "My Shipments"}
            {currentPage === "incoming" && "Incoming Shipments"}
            {currentPage === "profile" && "Profile"}
          </h1>
        </div>

        <div className="flex items-center gap-2 lg:gap-4">
          <button className="relative p-2 text-gray-400 hover:text-gray-600">
            <Bell className="w-5 h-5" />
            <span className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
              3
            </span>
          </button>

          <Dropdown
            menu={{ items: dropdownItems }}
            placement="bottomRight"
            trigger={["click"]}
            arrow
          >
            <Button
              type="text"
              className="flex items-center gap-2 lg:gap-3 h-auto p-2 hover:bg-gray-50 rounded-lg transition-colors"
            >
              <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                <User className="w-4 h-4 text-orange-600" />
              </div>
              <div className="text-right hidden sm:block">
                <p className="text-sm font-medium text-gray-900 truncate max-w-32">
                  {currentUser?.user?.name}
                </p>
                <p className="text-xs text-gray-500">
                  {getRoleDisplayName(currentUser?.user?.role)}
                </p>
              </div>
            </Button>
          </Dropdown>
        </div>
      </div>
    </header>
  );
}
