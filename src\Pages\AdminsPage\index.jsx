import React, { useMemo, useState } from "react";
import { <PERSON><PERSON>, <PERSON>, Col, DatePicker, Popconfirm, Popover, Row, Space, Typography, Select } from "antd";
import { Calendar, CalendarClock, Edit, Eye, Hash, Lock, LucideTag, Mail, Plus, RefreshCcw, Settings, Tag, User } from "lucide-react";
import DataTable, { getColumnFiltersProps, getColumnNumberRange, getColumnSearchProps } from "../../utils/DataTable";
import { Tag as AntdTag } from "antd";
import dayjs from "dayjs";
import { useUser } from "../../context/UserContext";
import adminsSeed from "../../data/admins";
import AddNewAdminModal from "./components/AddNewAdminModal";
import EditAdminModal from "./components/EditAdminModal";

const { RangePicker } = DatePicker;

const AdminsPage = () => {
  const { currentUser } = useUser();
  const [showAdd, setShowAdd] = useState(false);
  const [showEdit, setShowEdit] = useState(false);
  const [adminToEdit, setAdminToEdit] = useState(null);
  const [admins, setAdmins] = useState(adminsSeed);

  const [filters, setFilters] = useState({
    search: "",
    status: [],
    dateRange: null,
  });

  const updateFilter = (key, value) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  };

  const resetFilters = () => setFilters({ search: "", status: [], dateRange: null });

  const getStatusColor = (status) => {
    switch (status) {
      case "active":
        return "green";
      case "inactive":
        return "red";
      case "suspended":
        return "orange";
      default:
        return "blue";
    }
  };

  const filteredAdmins = useMemo(() => {
    return admins.filter((a) => {
      if (filters.search) {
        const s = filters.search.toLowerCase();
        const ok = a.name.toLowerCase().includes(s) || a.email.toLowerCase().includes(s) || String(a.id).includes(s);
        if (!ok) return false;
      }

      if (filters.status.length && !filters.status.includes(a.status)) return false;

      if (filters.dateRange && filters.dateRange.length === 2) {
        const created = dayjs(a.created_at);
        const [start, end] = filters.dateRange;
        if (start && created.isBefore(start, "day")) return false;
        if (end && created.isAfter(end, "day")) return false;
      }
      return true;
    });
  }, [admins, filters]);

  const dateFmt = (d) =>
    d
      ? new Date(d).toLocaleString("en-GB", {
          dateStyle: "medium",
          timeStyle: "short",
        })
      : "-";

  const handleAdd = (newAdmin) => {
    setAdmins((prev) => [...prev, newAdmin]);
  };

  const handleEdit = (updated) => {
    setAdmins((prev) => prev.map((a) => (a.id === updated.id ? updated : a)));
  };

  const handleEditClick = (record) => {
    setAdminToEdit(record);
    setShowEdit(true);
  };

  const columns = [
    {
      title: (
        <div className="flex items-center gap-2">
          <Hash className="w-4 h-4 text-gray-600" />
          <span>ID</span>
        </div>
      ),
      dataIndex: "id",
      key: "id",
      fixed: true,
      render: (id) => (
        <div className="flex items-center gap-2">
          <Hash className="w-4 h-4 text-gray-600" />
          <span>{id}</span>
        </div>
      ),
      sorter: (a, b) => a.id - b.id,
      ...getColumnNumberRange("id"),
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <User className="w-4 h-4 text-gray-600" />
          <span>Name</span>
        </div>
      ),
      dataIndex: "name",
      key: "name",
      render: (text) => <span className="font-medium">{text}</span>,
      ...getColumnSearchProps("name"),
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <Mail className="w-4 h-4 text-gray-600" />
          <span>Email</span>
        </div>
      ),
      dataIndex: "email",
      key: "email",
      render: (email) => <Typography.Text copyable>{email}</Typography.Text>,
      ...getColumnSearchProps("email"),
      sorter: (a, b) => a.email.localeCompare(b.email),
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <LucideTag className="w-4 h-4 text-gray-600" />
          <span>Status</span>
        </div>
      ),
      dataIndex: "status",
      key: "status",
      render: (status) => <AntdTag color={getStatusColor(status)}>{status}</AntdTag>,
      ...getColumnFiltersProps("status", [
        { text: "Active", value: "active" },
        { text: "Inactive", value: "inactive" },
        { text: "Suspended", value: "suspended" },
      ]),
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <CalendarClock className="w-4 h-4 text-gray-600" />
          <span>Created At</span>
        </div>
      ),
      dataIndex: "created_at",
      key: "created_at",
      render: (d) => <span className="text-gray-700">{dateFmt(d)}</span>,
      sorter: (a, b) => new Date(a.created_at) - new Date(b.created_at),
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <RefreshCcw className="w-4 h-4 text-gray-600" />
          <span>Updated At</span>
        </div>
      ),
      dataIndex: "updated_at",
      key: "updated_at",
      render: (d) => <span className="text-gray-700">{dateFmt(d)}</span>,
      sorter: (a, b) => new Date(a.updated_at) - new Date(b.updated_at),
      defaultSortOrder: "descend",
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <Lock className="w-4 h-4 text-gray-600" />
          <span>Password</span>
        </div>
      ),
      dataIndex: "password",
      key: "password",
      render: (pwd) => (
        <Popover placement="left" trigger="click" content={<code className="text-red-600">{pwd}</code>} title="Plaintext (dev only)">
          <Button type="link" className="!p-0">Reveal</Button>
        </Popover>
      ),
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <Settings className="w-4 h-4 text-gray-600" />
          <span>Actions</span>
        </div>
      ),
      key: "actions",
      render: (_, record) => (
        <Space size="middle">
          <Button onClick={() => handleEditClick(record)} className="text-blue-600 hover:text-blue-700" color="blue">
            <Eye className="w-4 h-4" />
          </Button>
          <Button color="blue" onClick={() => handleEditClick(record)} className="text-blue-600 hover:text-blue-700">
            <Edit className="w-4 h-4" />
          </Button>
          <Popconfirm
            title="Delete admin"
            description="Are you sure to delete this admin?"
            onConfirm={() => setAdmins((prev) => prev.filter((a) => a.id !== record.id))}
            okText="Delete"
            okButtonProps={{ danger: true }}
            cancelText="No"
          >
            <Button danger>Delete</Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200">
      <div className="p-6">
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-900">Admins</h1>
            <div className="flex gap-3">
              <button
                onClick={() => setShowAdd(true)}
                className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                Add Admin
              </button>
            </div>
          </div>

          <Card>
            <div className="">
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12}>
                  <div>
                    <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
                      <Tag className="w-4 h-4 text-gray-600" />
                      <span>Status</span>
                    </label>
                    <Select
                      mode="multiple"
                      size="large"
                      value={filters.status}
                      onChange={(values) => updateFilter("status", values)}
                      style={{ width: "100%" }}
                      placeholder="Select status"
                      options={[
                        { label: "Active", value: "active" },
                        { label: "Inactive", value: "inactive" },
                        { label: "Suspended", value: "suspended" },
                      ]}
                      allowClear
                    />
                  </div>
                </Col>
                <Col xs={24} sm={12}>
                  <div>
                    <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
                      <Calendar className="w-4 h-4 text-gray-600" />
                      <span>Date Range</span>
                    </label>
                    <RangePicker
                      size="large"
                      value={filters.dateRange}
                      onChange={(dates) => updateFilter("dateRange", dates)}
                      style={{ width: "100%" }}
                      placeholder={["Start Date", "End Date"]}
                    />
                  </div>
                </Col>
              </Row>
            </div>
          </Card>

          <div className="overflow-x-auto">
            <DataTable
              searchPlaceholder={"Search Admins"}
              onAddClick={() => setShowAdd(true)}
              table={{ header: columns, rows: filteredAdmins }}
            />
          </div>

          {showAdd && (
            <AddNewAdminModal isOpen={showAdd} onClose={() => setShowAdd(false)} onAddAdmin={handleAdd} />
          )}

          {showEdit && (
            <EditAdminModal
              isOpen={showEdit}
              onClose={() => {
                setShowEdit(false);
                setAdminToEdit(null);
              }}
              onEditAdmin={handleEdit}
              adminToEdit={adminToEdit}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminsPage;


