import React, { useEffect, useMemo, useState } from "react";
import { Modal, Tabs } from "antd";
import { useLocation, useParams } from "react-router-dom";
import data, {
  getDistributorsByFactory,
  getFactoryById,
  getQualityTestersByFactory,
  getShipmentsByFactory,
} from "../../data/allData";
import DistributorsTable from "../RejionRoutes/components/DistributorsTable";
import FactoryShipList from "../FactoryDashboard/components/FactoryShipList";
import QualityTestersTable from "../RejionRoutes/components/QualityTestersTable";
import users from "../../data/users";
import EditQualityTesterModal from "../RejionRoutes/components/EditQualityTesterModal";
import AddNewDistributorModal from "../RejionRoutes/components/AddNewDistributorModal";
import AddNewQualityTesterModal from "../RejionRoutes/components/AddNewQualityTesterModal";
import AddShipmentModal from "../../Components/Modals/AddNewShipment";

const FactoryDetails = () => {
  const { id } = useParams();
  const location = useLocation();
  const state = location?.state?.data;
  const details = getFactoryById(data.regions, id);
  const [testers, setTesters] = useState(
    getQualityTestersByFactory(data.regions, id, { redactPasswords: false })
  );
  const [distributors, setDistributors] = useState([]);

  useEffect(() => {
    setDistributors(getDistributorsByFactory(data.regions, id));
  }, [id]);

  const [shipments, setShipments] = useState(
    getShipmentsByFactory(data.regions, id, data.stores)
  );

  const factoryName = distributors?.[0]?.factory?.name || id;

  const [showAddDistributor, setShowAddDistributor] = useState(false);
  const [showAddTester, setShowAddTester] = useState(false);
  const [showAddShipment, setShowAddShipment] = useState(false);

  // const testers = useMemo(() => users.filter((u) => u.role === "quality"), []);

  return (
    <div className="space-y-4">
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
        <div className="text-xl font-semibold">{details?.name}</div>
        <div className="text-gray-500 text-sm">Factory ID: {id}</div>
        <div className="text-gray-500 text-sm">
          Region Name: {details?.region?.name ?? state?.region?.name}
        </div>
      </div>

      <Tabs
        defaultActiveKey="distributors"
        items={[
          {
            key: "distributors",
            label: "Distributors",
            children: (
              <div className="space-y-3">
                <div className="flex justify-end">
                  <button
                    onClick={() => setShowAddDistributor(true)}
                    className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700"
                  >
                    Add Distributor
                  </button>
                </div>
                <DistributorsTable
                  hiddenColumns={["factory", "region"]}
                  data={distributors}
                  setData={setDistributors}
                  onEdit={() => {}}
                  onDelete={() => {}}
                />
              </div>
            ),
          },
          {
            key: "shipments",
            label: "Shipments",
            children: (
              <div className="space-y-3">
                <div className="flex justify-end">
                  <button
                    onClick={() => setShowAddShipment(true)}
                    className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700"
                  >
                    Add Shipment
                  </button>
                </div>
                <FactoryShipList
                  hiddenColumns={["factory", "region"]}
                  withFilters={true}
                  filterByRegion={false}
                  filterByFactory={false}
                  filterByRoute={false}
                  setSelectedShipment={() => {}}
                  shipments={shipments}
                  setShipments={setShipments}
                />
              </div>
            ),
          },
          {
            key: "quality-testers",
            label: "Quality Testers",
            children: (
              <div className="space-y-3">
                <div className="flex justify-end">
                  <button
                    onClick={() => setShowAddTester(true)}
                    className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700"
                  >
                    Add Quality Tester
                  </button>
                </div>
                <QualityTestersTable
                  hiddenColumns={["factory", "region"]}
                  setData={setTesters}
                  data={testers}
                  onEdit={() => {}}
                  onDelete={() => {}}
                />
              </div>
            ),
          },
        ]}
      />
      {showAddDistributor && (
        <AddNewDistributorModal
          defaultFactoryId={id}
          defaultRegionId={details?.region?.id ?? state?.region?.id}
          onClose={() => setShowAddDistributor(false)}
          onCreate={(payload) => {
            const factoryInfo = distributors?.[0]?.factory || {
              id,
              name: details.name,
            };
            setDistributors((prev) => [
              { ...payload, factory: factoryInfo },
              ...prev,
            ]);
          }}
        />
      )}
      {showAddTester && (
        <AddNewQualityTesterModal
          onClose={() => setShowAddTester(false)}
          onCreate={(payload) => {
            const factoryInfo = distributors?.[0]?.factory || {
              id,
              name: details?.name,
            };
            setTesters((prev) => [
              { ...payload, factory: factoryInfo },
              ...prev,
            ]);
          }}
        />
      )}
      {showAddShipment && (
        <AddShipmentModal
          defaultFactoryId={id}
          defaultRegionId={details?.region?.id ?? state?.region?.id}
          onClose={() => setShowAddShipment(false)}
          showAddShipment={showAddShipment}
          setShowAddShipment={setShowAddShipment}
          shipments={shipments}
          setShipments={setShipments}
        />
      )}
    </div>
  );
};

export default FactoryDetails;
