import React, { useMemo, useState, useEffect } from "react";
import { X, Search, CheckCircle2, XCircle, AlertCircle, Loader } from "lucide-react";
import { getQualityAnswersData } from "../../../services/shipmentApi";
import { getCheckListQuestions, confirmQualityChecklist, rejectQualityChecklist } from "../../../api/apiService";

// Questions will be fetched from API






const answerOptions = [
  { value: "yes", label: "Yes", icon: CheckCircle2, color: "text-green-600" },
  { value: "no", label: "No", icon: XCircle, color: "text-red-600" },
];

const TestQualityModal = ({ open, onClose, onConfirm, onReject, selectedShipment }) => {
  const [query, setQuery] = useState("");
  const [questions, setQuestions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);
  
  const buildEmptyAnswers = (list) => {
    return (list || []).reduce((acc, q) => {
      acc[q.id] = { answer: "", comment: "" };
      return acc;
    }, {});
  };

  const [answers, setAnswers] = useState({});

  const clearAllAnswers = () => {
    setAnswers(buildEmptyAnswers(questions));
  };

  const [notes, setNotes] = useState("");

  // Fetch checklist questions (and existing answers) when modal opens
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // 1) Load checklist questions
        const response = await getCheckListQuestions();
        const apiList = Array.isArray(response?.data) ? response.data : [];
        const mappedQuestions = apiList
          .sort((a, b) => (a.category || '').localeCompare(b.category || '') || (a.order || 0) - (b.order || 0))
          .map((item) => ({
            id: `question_${item.id}`,
            text: item.question,
            category: item.category || "",
          }));
        setQuestions(mappedQuestions);
        setAnswers(buildEmptyAnswers(mappedQuestions));

       
      } catch (err) {
        console.error('Error loading checklist questions:', err);
        setError('Failed to load quality checklist questions');
      } finally {
        setLoading(false);
      }
    };

    if (open) {
      fetchData();
    } else {
      // Reset state when modal closes
      setQuestions([]);
      setAnswers({});
      setNotes("");
      setError(null);
    }
  }, [open, selectedShipment?.id]);

  const filteredQuestions = useMemo(() => {
    const q = query.trim().toLowerCase();
    if (!q) return questions;
    return questions.filter(
      (item) =>
        item.text.toLowerCase().includes(q) ||
        (item.category || "").toLowerCase().includes(q)
    );
  }, [query, questions]);

  const grouped = useMemo(() => {
    return filteredQuestions.reduce((acc, q) => {
      if (!acc[q.category]) acc[q.category] = [];
      acc[q.category].push(q);
      return acc;
    }, {});
  }, [filteredQuestions]);

  const handleAnswerChange = (questionId, value) => {
    setAnswers((prev) => ({
      ...prev,
      [questionId]: { ...prev[questionId], answer: value },
    }));
  };

  const stats = useMemo(() => {
    const total = questions.length;
    const answered = Object.values(answers).filter((a) => a.answer).length;
    const yesCount = Object.values(answers).filter(
      (a) => a.answer === "yes"
    ).length;
    const noCount = Object.values(answers).filter(
      (a) => a.answer === "no"
    ).length;
    const allAnswered = answered === total;
    return { total, answered, yesCount, noCount, allAnswered };
  }, [answers, questions]);

  const handleConfirm = async () => {
    try {
      setSubmitting(true);
      setError(null);

      // Transform answers to API format
      const apiAnswers = questions.map((question) => ({
        checklist_question_id: question.id.replace('question_', ''),
        answer: answers[question.id]?.answer === 'yes' ? 'Yes' : 'No',
        note: answers[question.id]?.comment || notes,
        quality_id: 3, // Default quality ID, you might want to make this dynamic
      }));

      const result = await confirmQualityChecklist({
        shipment_id: selectedShipment.id,
        production_id: selectedShipment.productionId || 1,
        answers: apiAnswers,
        quality_note: notes,

      });

      const ok = !!result?.data?.message;
      if (ok) {
        onConfirm(result.data?.data?.quality_status || 'confirmed');
        setAnswers(buildEmptyAnswers(questions));
        onClose();
      } else {
        setError(result?.data?.message || 'Failed to submit quality answers');
      }
    } catch (err) {
      console.error('Error submitting quality answers:', err);
      setError('Failed to submit quality answers');
    } finally {
      setSubmitting(false);
    }
  };

  const handleReject = async () => {
    try {
      setSubmitting(true);
      setError(null);

      // Transform answers to API format
      const apiAnswers = questions.map((question) => ({
        checklist_question_id: question.id.replace('question_', ''),
        answer: answers[question.id]?.answer === 'yes' ? 'Yes' : 'No',
        note: answers[question.id]?.comment || notes,
        quality_id: 3, // Default quality ID, you might want to make this dynamic
      }));

      const result = await rejectQualityChecklist({
        shipment_id: selectedShipment.id,
        production_id: selectedShipment.productionId || 1,
        quality_note: notes,
        answers: apiAnswers,
      });

      const ok = !!result?.data?.message;
      if (ok) {
        onReject(result.data?.data?.quality_status || 'rejected');
        setAnswers(buildEmptyAnswers(questions));
        onClose();
      } else {
        setError(result?.data?.message || 'Failed to submit quality answers');
      }
    } catch (err) {
      console.error('Error submitting quality answers:', err);
      setError('Failed to submit quality answers');
    } finally {
      setSubmitting(false);
    }
  };

  if (!open) return null;

  return (
    <div className="fixed overflow-auto !m-0 inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-start justify-center z-50 p-3">
      <div className="bg-white rounded-2xl shadow-2xl max-w-5xl w-full  overflow-hidden flex flex-col text-[0.95rem]">
        {/* Header */}
        <div className="px-5 sticky top-0  py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white">
          <div className="flex items-center justify-between mb-3">
            <div>
              <h1 className="text-xl md:text-2xl font-bold">
                Quality Check List {selectedShipment?.bakery_type}
              </h1>
              <p className="text-blue-100 text-xs mt-0.5">
                Ensure compliance and quality standards
              </p>

            </div>
            <button
              onClick={onClose}
              className="text-white hover:bg-white hover:bg-opacity-20 rounded-full p-1.5 transition-all"
            >
              <X size={20} />
            </button>
          </div>

          {/* Stats Bar */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2.5">
            <div className="bg-white bg-opacity-20 rounded-lg px-3.5 py-2.5 backdrop-blur-sm">
              <div className="text-xl font-bold">{stats.total}</div>
              <div className="text-[11px] text-blue-100">Total Questions</div>
            </div>
            <div className="bg-white bg-opacity-20 rounded-lg px-3.5 py-2.5 backdrop-blur-sm">
              <div className="text-xl font-bold">{stats.answered}</div>
              <div className="text-[11px] text-blue-100">Answered</div>
            </div>
            <div className="bg-white bg-opacity-20 rounded-lg px-3.5 py-2.5 backdrop-blur-sm">
              <div className="text-xl font-bold text-green-200">
                {stats.yesCount}
              </div>
              <div className="text-[11px] text-blue-100">Passed</div>
            </div>
            <div className="bg-white bg-opacity-20 rounded-lg px-3.5 py-2.5 backdrop-blur-sm">
              <div className="text-xl font-bold text-red-200">
                {stats.noCount}
              </div>
              <div className="text-[11px] text-blue-100">Failed</div>
            </div>
          </div>
        </div>

        {/* Search Bar */}
        <div className="px-5 py-3.5 bg-gray-50 border-b">
          <div className="relative">
            <Search
              className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"
              size={18}
            />
            <input
              type="text"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="Search questions or categories..."
              className="w-full pl-9 pr-3.5 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Content */}
        <div className="overflow-y-auto flex-1 p-5 bg-gray-50">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <Loader className="w-8 h-8 text-blue-600 mx-auto mb-4 animate-spin" />
                <p className="text-gray-600">Loading quality questions...</p>
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <XCircle className="w-8 h-8 text-red-500 mx-auto mb-4" />
                <p className="text-red-600 mb-4">{error}</p>
                <button
                  onClick={() => window.location.reload()}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Retry
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-3.5">
            {Object.entries(grouped).map(([category, categoryQuestions]) => {
              const categoryAnswered = categoryQuestions.filter(
                (q) => (answers[q.id] && answers[q.id].answer)
              ).length;
              const categoryTotal = categoryQuestions.length;
              const progress = (categoryAnswered / categoryTotal) * 100;

              return (
                <div
                  key={category}
                  className="bg-white  border border-gray-200 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow"
                >
                  {/* Category Header */}
                  <div className="px-5 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
                    <div className="flex items-center justify-between mb-1.5">
                      <h2 className="text-base font-bold text-blue-700">
                        {category}
                      </h2>
                      <span className="text-xs font-medium text-gray-600 bg-white px-2.5 py-0.5 rounded-full">
                        {categoryAnswered}/{categoryTotal}
                      </span>
                    </div>
                    {/* Progress Bar */}
                    <div className="w-full bg-gray-200 rounded-full h-[6px]">
                      <div
                        className="bg-blue-600 h-[6px] rounded-full transition-all duration-300"
                        style={{ width: `${progress}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* Questions */}
                  <div className="divide-y divide-gray-100">
                    {categoryQuestions.map((q, idx) => {
                      const current = answers[q.id] || { answer: "", comment: "" };
                      const isAnswered = current.answer;
                      const answer = current.answer;

                      return (
                        <div
                          key={q.id}
                          className={`px-4 py-3 transition-colors ${
                            isAnswered
                              ? "bg-gray-50"
                              : "bg-white hover:bg-blue-50"
                          }`}
                        >
                          <div className="flex items-start gap-3">
                            {/* Question Number */}
                            <div className="flex-shrink-0">
                              <div
                                className={`w-7 h-7 rounded-full flex items-center justify-center font-semibold text-[13px] ${
                                  isAnswered
                                    ? answer === "yes"
                                      ? "bg-green-100 text-green-700"
                                      : "bg-red-100 text-red-700"
                                    : "bg-gray-200 text-gray-600"
                                }`}
                              >
                                {idx + 1}
                              </div>
                            </div>

                            {/* Question Text & Options */}
                            <div className="flex-1 min-w-0">
                              <label className="block font-medium text-gray-800 mb-2">
                                {q.text}
                              </label>

                              <div className="flex flex-wrap gap-2.5">
                                {answerOptions.map((opt) => {
                                  const Icon = opt.icon;
                                  const isSelected =
                                    (answers[q.id]?.answer) === opt.value;

                                  return (
                                    <label
                                      key={opt.value}
                                      className={`flex items-center gap-1.5 px-3 py-1.5 rounded-lg border-2 cursor-pointer transition-all ${
                                        isSelected
                                          ? opt.value === "yes"
                                            ? "border-green-500 bg-green-50"
                                            : "border-red-500 bg-red-50"
                                          : "border-gray-200 hover:border-gray-300 bg-white"
                                      }`}
                                    >
                                      <input
                                        type="radio"
                                        name={q.id}
                                        value={opt.value}
                                        checked={isSelected}
                                        onChange={(e) =>
                                          handleAnswerChange(
                                            q.id,
                                            e.target.value
                                          )
                                        }
                                        className="sr-only"
                                      />
                                      <Icon
                                        size={18}
                                        className={
                                          isSelected
                                            ? opt.color
                                            : "text-gray-400"
                                        }
                                      />
                                      <span
                                        className={`font-medium ${
                                          isSelected
                                            ? opt.color
                                            : "text-gray-700"
                                        } text-[13px]`}
                                      >
                                        {opt.label}
                                      </span>
                                    </label>
                                  );
                                })}
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="px-5 py-3.5 bg-white border-t space-y-3">
          {/* Notes Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1.5">
              Additional Notes
            </label>
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Add any additional observations or comments..."
              className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              rows="3"
            />
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1.5 text-sm">
              {stats.allAnswered ? (
                <div className="flex items-center gap-1.5 text-green-600 font-medium">
                  <CheckCircle2 size={18} />
                  All questions answered
                </div>
              ) : (
                <div className="flex items-center gap-1.5 text-amber-600">
                  <AlertCircle size={18} />
                  <span className="text-[13px]">
                    {stats.total - stats.answered} questions remaining
                  </span>
                </div>
              )}
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={()=>onClose()}
                className="px-5 py-2 font-medium rounded-lg transition-all flex items-center gap-2 bg-gray-200 text-gray-600 hover:bg-gray-300"
               
              >
                {submitting && <Loader className="w-4 h-4 animate-spin" />}
                Cancel
              </button>
              <button
                onClick={handleReject}
                disabled={!stats.allAnswered || submitting}
                className={`px-5 py-2 font-medium rounded-lg transition-all flex items-center gap-2 ${
                  stats.allAnswered && !submitting
                    ? "bg-red-600 hover:bg-red-700 text-white"
                    : "bg-gray-200 text-gray-400 cursor-not-allowed"
                }`}
              >
                {submitting && <Loader className="w-4 h-4 animate-spin" />}
                Reject
              </button>
              <button
                onClick={handleConfirm}
                disabled={!stats.allAnswered || submitting}
                className={`px-5 py-2 font-medium rounded-lg transition-all flex items-center gap-2 ${
                  stats.allAnswered && !submitting
                    ? "bg-green-600 hover:bg-green-700 text-white"
                    : "bg-gray-200 text-gray-400 cursor-not-allowed"
                }`}
              >
                {submitting && <Loader className="w-4 h-4 animate-spin" />}
                Confirm
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestQualityModal;
