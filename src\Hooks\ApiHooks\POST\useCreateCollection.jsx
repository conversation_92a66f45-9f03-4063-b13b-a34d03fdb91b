import { useState } from 'react';
import { createCollection } from '../../../api/apiService';

export const useCreateCollection = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [data, setData] = useState(null);

  const createNewCollection = async (collectionData) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await createCollection(collectionData);
      setData(response.data);
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.message || err.message || 'حدث خطأ أثناء إنشاء المجموعة';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return {
    createCollection: createNewCollection,
    loading,
    error,
    data,
    reset: () => {
      setError(null);
      setData(null);
    }
  };
};
