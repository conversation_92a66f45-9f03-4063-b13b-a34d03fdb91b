// src/pages/StoreDashboard.jsx
import {
  Package,
  CheckCircle,
  Store as StoreIcon,
  Eye,
  Search,
  Grid3X3,
  List,
  Table,
  SortAsc,
  SortDesc,
  X,
} from "lucide-react";
import { useState, useMemo, useEffect } from "react";
import { DatePicker } from "antd";
import dayjs from "dayjs";
import { useUser } from "../../context/UserContext";
import { getStoreShipments, AddStoreIssue } from "../../api/apiService";
import ShipmentDetailsModal from "../../Components/Modals/ShipmentDetailsModal";
import ConfirmationSuccessModal from "./components/StoreConfirmationSuccessModal";
import StoreRejectModal from "./components/StoreRejectModal";
import RejectionDetailsModal from "./components/RejectionDetailsModal";

const StoreDashboard = () => {
  const { currentUser } = useUser();
  const [shipments, setShipments] = useState([]);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedShipment, setSelectedShipment] = useState(null);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successType, setSuccessType] = useState("confirm"); // confirm | reject
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [showRejectionDetails, setShowRejectionDetails] = useState(false);
  const [addLoading, setAddLoading] = useState(false);
  const [loadingShipments , setLoadingShipments] = useState(false);
  // Load shipments from API
  const loadShipments = async () => {
    try {
      setLoadingShipments(true);
      const res = await getStoreShipments().finally(()=>{
        setLoadingShipments(false);
      });
      const items = Array.isArray(res?.data) ? res.data : [];

      // map API payload to UI model
      const mapped = items.map((it) => {
        const quality = (it.quality_status || "pending").toLowerCase();
        const qualityStatus =
          quality === "confirmed"
            ? "Confirmed"
            : quality === "rejected"
            ? "Rejected"
            : "Pending";

        const hasIssues = !!it.has_issues;
        const issues = Array.isArray(it.issues) ? it.issues : [];

        return {
          id: it.id,
          bakeryType: it.bakery_type,
          quantity: it.quantity,
          region: it.region,
          route: it.route,
          vehicleId: it.vehicle_id,
          vehicleNumber: it.vehicle_number,
          qualityStatus,
          qualityNotes: it.quality_note || undefined,
          timestamps: {
            created: it.created_at || null,
            delivery: it.exit_time || null,
          },
          // Derive store decision from issues if API doesn't provide it
          storeDecision: hasIssues ? "Rejected" : undefined,
          hasIssues,
          issues,
        };
      });

      setShipments(mapped);
      console.log("mapped", mapped);
    } catch (e) {
      console.error("Failed to load store shipments", e);
      setShipments([]);
    }
  };
  useEffect(() => {

    loadShipments();
  }, []);

  // View & filters
  const [viewMode, setViewMode] = useState("cards"); // cards | list | table
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all"); // all | Received | On Display | Sold
  const [dateFilter, setDateFilter] = useState("all"); // Deprecated by antd RangePicker (kept for backward compatibility)
  const [dateRange, setDateRange] = useState(null); // [dayjs, dayjs]
  const [sortBy, setSortBy] = useState("created"); // created | delivery | bakeryType | quantity
  const [sortOrder, setSortOrder] = useState("desc"); // asc | desc
  const [storeDecisionFilter, setStoreDecisionFilter] = useState("all"); // all | Pending | Confirmed | Rejected

  const openDetailsModal = (shipment) => {
    setSelectedShipment(shipment);
    setShowDetailsModal(true);
  };

  const markShipmentDecision = (shipmentId, decision, extra = {}) => {
    setShipments((prev) =>
      prev.map((s) =>
        s.id === shipmentId
          ? {
              ...s,
              storeDecision: decision, // "Confirmed" | "Rejected"
              storeDecisionAt: new Date().toISOString(),
              rejectionDetails: decision === "Rejected" ? extra : undefined,
            }
          : s
      )
    );
  };

  const handleConfirm = (shipment) => {
    markShipmentDecision(shipment.id, "Confirmed");
    setSelectedShipment({ ...shipment, storeDecision: "Confirmed" });
    setSuccessType("confirm");
    setShowSuccessModal(true);
  };

  const handleReject = (shipment) => {
    setSelectedShipment(shipment);
    setShowRejectModal(true);
  };

  const openRejectionDetails = (shipment) => {
    setSelectedShipment(shipment);
    setShowRejectionDetails(true);
  };

  // === Filtering & Sorting (Store view: only quality-confirmed) ===
  const filteredAndSortedShipments = useMemo(() => {
    let filtered = shipments.filter((shipment) => {
      // (1) فلتر حالة الشحنة (اختياري)
      // if (
      //   statusFilter !== "all" &&
      //   (shipment.status || "").toLowerCase() !== statusFilter.toLowerCase()
      // ) {
      //   return false;
      // }

      // (1.1) فلتر قرار المتجر (Pending/Confirmed/Rejected)
      if (storeDecisionFilter !== "all") {
        const decision = shipment.storeDecision; // may be undefined when pending
        if (storeDecisionFilter === "Pending" && decision) return false;
        if (storeDecisionFilter === "Confirmed" && decision !== "Confirmed")
          return false;
        if (storeDecisionFilter === "Rejected" && decision !== "Rejected")
          return false;
      }

      // (2) البحث
      if (searchTerm) {
        const s = searchTerm.toLowerCase();
        const matchesSearch =
          (shipment.bakeryType || "").toLowerCase().includes(s) ||
          (shipment.driver || "").toLowerCase().includes(s) ||
          (shipment.branch || "").toLowerCase().includes(s) ||
          String(shipment.id).includes(s);
        if (!matchesSearch) return false;
      }

      // (3) فلتر الوقت عبر antd RangePicker على created_at
      if (Array.isArray(dateRange) && dateRange[0] && dateRange[1]) {
        const createdAt = shipment.timestamps.created
          ? new Date(shipment.timestamps.created)
          : null;
        if (!createdAt) return false;
        const start = dateRange[0].startOf("day").toDate();
        const end = dateRange[1].endOf("day").toDate();
        if (createdAt < start || createdAt > end) return false;
      }

      return true;
    });

    // (4) الفرز
    return filtered.sort((a, b) => {
      let aValue, bValue;

      switch (sortBy) {
        case "created":
          aValue = new Date(a.timestamps.created);
          bValue = new Date(b.timestamps.created);
          break;
        case "delivery":
          aValue = new Date(a.timestamps.delivery || 0);
          bValue = new Date(b.timestamps.delivery || 0);
          break;
        case "bakeryType":
          aValue = (a.bakeryType || "").toLowerCase();
          bValue = (b.bakeryType || "").toLowerCase();
          break;
        case "quantity":
          aValue = a.quantity || 0;
          bValue = b.quantity || 0;
          break;
        default:
          aValue = new Date(a.timestamps.created);
          bValue = new Date(b.timestamps.created);
      }

      if (sortOrder === "asc") return aValue > bValue ? 1 : -1;
      return aValue < bValue ? 1 : -1;
    });
  }, [
    shipments,
    searchTerm,
    storeDecisionFilter,
    dateRange,
    sortBy,
    sortOrder,
  ]);

  const storeShipments = filteredAndSortedShipments;

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">Store Dashboard</h1>

      {/* Store Decision KPIs */}
      <div className="grid grid-cols-1 md:grid-cols-1 gap-6">
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex items-center gap-3">
            <div className="bg-yellow-100 p-3 rounded-full">
              <Package className="w-6 h-6 text-yellow-700" />
            </div>
            <div className="mr-4">
              <p className="text-2xl font-bold text-gray-900">
                {
                  shipments.filter(
                    (s) => s.qualityStatus === "Confirmed" && !s.storeDecision
                  ).length
                }
              </p>
              <p className="text-gray-600">Store Pending (QC Confirmed)</p>
            </div>
          </div>
        </div>

      </div>

      {/* Wrapper Card */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <h2 className="text-xl font-bold text-gray-900">Shipments</h2>
              <p className="text-sm text-gray-600 mt-1">
                {storeShipments.length} shipments — QC Confirmed
              </p>
            </div>

            {/* View Toggle */}
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">View:</span>
              <div className="flex bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setViewMode("cards")}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === "cards"
                      ? "bg-white text-gray-900 shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                >
                  <Grid3X3 className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode("list")}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === "list"
                      ? "bg-white text-gray-900 shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                >
                  <List className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode("table")}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === "table"
                      ? "bg-white text-gray-900 shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                >
                  <Table className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Filter Controls */}
        <div className="p-6 border-b border-gray-200 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
            {/* Search */}
            <div className="relative col-span-[auto]">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search shipments..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              />
            </div>

            {/* Store Decision Filter */}
            <select
              value={storeDecisionFilter}
              onChange={(e) => setStoreDecisionFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            >
              <option value="all">All Store Decisions</option>
              <option value="Rejected">Rejected</option>
            </select>

            {/* Date Filter - AntD RangePicker */}
            <div className="w-full">
              <DatePicker.RangePicker
                className="w-full h-[40px]"
                value={dateRange}
                onChange={(val) => setDateRange(val)}
                allowClear
                presets={[
                  { label: "Today", value: [dayjs(), dayjs()] },
                  { label: "This Week", value: [dayjs().startOf("week"), dayjs().endOf("week")] },
                  { label: "This Month", value: [dayjs().startOf("month"), dayjs().endOf("month")] },
                ]}
              />
            </div>

            {/* Sort */}
            <div className="flex gap-2">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              >
                <option value="created">Created Date</option>
                <option value="delivery">Delivery Date</option>
                <option value="bakeryType">Bakery Type</option>
                <option value="quantity">Quantity</option>
              </select>
              <button
                onClick={() =>
                  setSortOrder(sortOrder === "asc" ? "desc" : "asc")
                }
                className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                {sortOrder === "asc" ? (
                  <SortAsc className="w-4 h-4" />
                ) : (
                  <SortDesc className="w-4 h-4" />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        {storeShipments.length === 0 ? (
          <div className="p-12 text-center">
            <Package className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No shipments
            </h3>
            <p className="text-gray-600">
              QC-confirmed shipments will appear here.
            </p>
          </div>
        ) : (
          <div className="p-6 overflow-x-auto">
            {/* CARDS VIEW */}
            {viewMode === "cards" && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {storeShipments.map((shipment) => (
                  <div
                    key={shipment.id}
                    className={`bg-white border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow cursor-pointer ${
                      shipment.status === "Sold"
                        ? "!bg-green-50 border-green-600 border-dashed shadow-inner"
                        : ""
                    }`}
                  >
                    {/* Header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900 text-lg mb-1">
                          {shipment.bakeryType}
                        </h3>
                        <p className="text-sm text-gray-600">
                          ID: #{shipment.id}
                        </p>
                      </div>
                      {shipment?.issues?.length > 0 && (
                        <span
                          className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-semibold rounded-full border ${"bg-red-50 text-red-700 border-red-200"}`}
                        >
                          Ticketed
                        </span>
                      )}
                    </div>

                    {/* Details */}
                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Quantity:</span>
                        <span className="font-medium text-gray-900">
                          {shipment.quantity}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Region:</span>
                        <span className="font-medium text-gray-900">
                          {shipment.region}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Route:</span>
                        <span className="font-medium text-gray-900">
                          {shipment.route}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Created at:</span>
                        <span className="font-medium text-sm font-sans text-gray-500">
                          {shipment.timestamps.created
                            ? new Date(
                                shipment.timestamps.created
                              ).toLocaleString("en-US", {
                                year: "numeric",
                                month: "short",
                                day: "numeric",
                                hour: "2-digit",
                                minute: "2-digit",
                                hour12: true,
                              })
                            : "N/A"}
                        </span>
                      </div>
                   
                      {shipment.storeDecision && (
                        <div className="flex justify-between text-xs">
                          <span className="text-gray-500">Store:</span>
                          <span
                            className={`font-semibold ${
                              shipment.storeDecision === "Confirmed"
                                ? "text-emerald-700"
                                : "text-red-700"
                            }`}
                          >
                            {shipment.storeDecision}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Actions */}
                    <div
                      className="space-y-3"
                      onClick={(e) => e.stopPropagation()}
                    >
                      {shipment.qualityNotes && (
                        <p className="text-sm text-gray-600 bg-orange-100 p-3 rounded-xl border border-orange-300">
                          <span className="text-orange-600 font-bold font-mono">
                            QC Note:
                          </span>{" "}
                          {shipment.qualityNotes}
                        </p>
                      )}

                      <div className={`grid grid-cols-1  gap-y-2`}>
                        {!shipment.storeDecision && (
                          <button
                            onClick={() => handleReject(shipment)}
                            className=" bg-red-600 text-white px-4 py-2.5 rounded-lg text-sm font-medium hover:bg-red-700 transition-colors"
                          >
                            Complaint ticket
                          </button>
                        )}
                        {shipment.storeDecision === "Rejected" && (
                          <button
                            onClick={() => {
                              openRejectionDetails(shipment);
                              console.log("selectedShipment", selectedShipment);
                            }}
                            className="col-span-3 border border-red-600/90 text-red-600 px-4 py-2.5 rounded-lg text-sm font-medium hover:bg-red-700 hover:!text-white transition-colors flex items-center justify-center gap-2"
                          >
                            <X className="w-4 h-4" />
                            View Complaint ticket
                          </button>
                        )}
                        <button
                          onClick={() => openDetailsModal(shipment)}
                          className="col-span-3 bg-green-600 text-white px-4 py-2.5 rounded-lg text-sm font-medium hover:bg-orange-700 transition-colors flex items-center justify-center gap-2"
                        >
                          <Eye className="w-4 h-4" />
                          View
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* LIST VIEW */}
            {viewMode === "list" && (
              <div className="space-y-4 min-w-[999px] relative !overflow-auto">
                {storeShipments.map((shipment) => (
                  <div
                    key={shipment.id}
                    className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => openDetailsModal(shipment)}
                  >
                    <div className="whitespace-nowrap flex items-center justify-between">
                      <div className="flex-1 grid grid-cols-6 gap-4">
                        <div>
                          <h3 className="font-semibold text-gray-900">
                            {shipment.bakeryType}
                          </h3>
                          <p className="text-sm text-gray-600">ID: #{shipment.id}</p>
                          {shipment?.issues?.length > 0 && (
                            <span className={`inline-flex items-center gap-1 px-2 py-0.5 text-[11px] font-semibold rounded-full border ${"bg-red-50 text-red-700 border-red-200"}`}>
                              Ticketed
                            </span>
                          )}
                        </div>
                        <div className="w-fit">
                          <p className="text-sm text-gray-600">Quantity</p>
                          <p className="font-medium text-gray-900">{shipment.quantity}</p>
                        </div>
                        <div className="w-fit">
                          <p className="text-sm text-gray-600">Region</p>
                          <p className="font-medium text-gray-900">{shipment.region}</p>
                        </div>
                        <div className="w-fit">
                          <p className="text-sm text-gray-600">Route</p>
                          <p className="font-medium text-gray-900">{shipment.route}</p>
                        </div>
                        <div className="w-fit">
                          <p className="text-sm text-gray-600">Created at</p>
                          <p className="font-medium text-sm font-sans text-gray-500">
                            {shipment.timestamps.created
                              ? new Date(shipment.timestamps.created).toLocaleString("en-US", {
                                  year: "numeric",
                                  month: "short",
                                  day: "numeric",
                                  hour: "2-digit",
                                  minute: "2-digit",
                                  hour12: true,
                                })
                              : "N/A"}
                          </p>
                        </div>
                        {shipment.storeDecision && (
                          <div className="w-fit">
                            <p className="text-sm text-gray-600">Store</p>
                            <p className={`font-semibold ${shipment.storeDecision === "Confirmed" ? "text-emerald-700" : "text-red-700"}`}>
                              {shipment.storeDecision}
                            </p>
                          </div>
                        )}
                      </div>

                      <div
                        className="flex items-center gap-3 ml-4"
                        onClick={(e) => e.stopPropagation()}
                      >
                        {!shipment.storeDecision && (
                          <button
                            onClick={() => handleReject(shipment)}
                            className="bg-red-600 text-white px-3 py-1.5 rounded-lg text-sm font-medium hover:bg-red-700 transition-colors"
                          >
                            Complaint ticket
                          </button>
                        )}
                        <button
                          onClick={() => openDetailsModal(shipment)}
                          className="bg-gray-600 text-white px-3 py-1.5 rounded-lg text-sm font-medium hover:bg-gray-700 transition-colors flex items-center gap-1"
                        >
                          <Eye className="w-3 h-3" />
                          View
                        </button>
                        {shipment.storeDecision && (
                          <span
                            className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-semibold rounded-full border ${
                              shipment.storeDecision === "Confirmed"
                                ? "bg-emerald-50 text-emerald-700 border-emerald-200"
                                : "bg-red-50 text-red-700 border-red-200"
                            }`}
                          >
                            {shipment.storeDecision === "Confirmed" ? (
                              <CheckCircle className="w-3 h-3" />
                            ) : (
                              <X className="w-3 h-3" />
                            )}
                            {shipment.storeDecision === "Confirmed" ? "Confirmed" : "Ticketed"}
                          </span>
                        )}
                      </div>
                    </div>

                    {shipment.qualityNotes && (
                      <p className="mt-2 text-xs text-gray-600">
                        QC Note: {shipment.qualityNotes}
                      </p>
                    )}
                    {shipment.storeDecision === "Rejected" && (
                      <div className="mt-3">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            openRejectionDetails(shipment);
                          }}
                          className="px-3 py-1.5 rounded-lg bg-red-600/90 text-white hover:bg-red-700 text-xs"
                        >
                          View Complaint ticket
                        </button>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}

            {/* TABLE VIEW */}
            {viewMode === "table" && (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        ID
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Bakery Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Quantity
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Region
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Route
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Vehicle Number
                      </th>
                      
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Arrived
                      </th>
                     
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {storeShipments.map((shipment) => (
                      <tr
                        key={shipment.id}
                        className="hover:bg-gray-50 cursor-pointer"
                        onClick={() => openDetailsModal(shipment)}
                      >
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          #{shipment.id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {shipment.bakeryType}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {shipment.quantity}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {shipment.region}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {shipment.route}
                        </td>
                       
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {shipment.vehicleNumber}
                        </td>
                       
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {shipment.timestamps.delivery
                            ? new Date(
                                shipment.timestamps.delivery
                              ).toLocaleDateString()
                            : "N/A"}
                        </td>
                      
                        {/* Rejection details button (only when rejected) */}
                        <td className="px-6 py-4 whitespace-nowrap text-xs">
                          {shipment.storeDecision === "Rejected" ? (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                openRejectionDetails(shipment);
                              }}
                              className="px-3 py-1 rounded-lg bg-red-600/90 text-white hover:bg-red-700"
                            >
                              View Rejection
                            </button>
                          ) : (
                            <span className="text-gray-400">—</span>
                          )}
                        </td>
                        <td
                          className="px-6 py-4 whitespace-nowrap text-sm font-medium"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <div className="flex items-center gap-2">
                            {!shipment.storeDecision && (
                              <>
                                <button
                                  onClick={() => handleReject(shipment)}
                                  className="bg-red-600 text-white px-3 py-1 rounded-lg text-xs font-medium hover:bg-red-700 transition-colors"
                                >
                                  Reject
                                </button>
                              </>
                            )}
                            <button
                              onClick={() => openDetailsModal(shipment)}
                              className="bg-gray-600 text-white px-3 py-1 rounded-lg text-xs font-medium hover:bg-gray-700 transition-colors flex items-center gap-1"
                            >
                              <Eye className="w-3 h-3" />
                              View
                            </button>
                            {shipment.storeDecision && (
                              <span
                                className={`inline-flex items-center gap-1 px-2 py-1 text-[10px] font-semibold rounded-full border ${
                                  shipment.storeDecision === "Confirmed"
                                    ? "bg-emerald-50 text-emerald-700 border-emerald-200"
                                    : "bg-red-50 text-red-700 border-red-200"
                                }`}
                              >
                                {shipment.storeDecision === "Confirmed" ? (
                                  <CheckCircle className="w-3 h-3" />
                                ) : (
                                  <X className="w-3 h-3" />
                                )}
                                {shipment.storeDecision === "Confirmed"
                                  ? "Confirmed"
                                  : "Ticketed"}
                              </span>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}
      </div>

      {showDetailsModal && (
        <ShipmentDetailsModal
          setShowDetailsModal={setShowDetailsModal}
          selectedShipment={selectedShipment}
          setSelectedShipment={setSelectedShipment}
        />
      )}

      <ConfirmationSuccessModal
        show={showSuccessModal}
        onClose={() => {
          setShowSuccessModal(false);
          setSelectedShipment(null);
        }}
        shipment={selectedShipment}
        type={successType}
      />
      <StoreRejectModal
      loading ={addLoading}
        show={showRejectModal}
        shipment={selectedShipment}
        onCancel={() => {
          setShowRejectModal(false);
          setSelectedShipment(null);
        }}
        onSubmit={async (data) => {
          try {
            // Build multipart form data as required by the API
            const formData = new FormData();
            if (selectedShipment?.id) {
              formData.append("shipment_id", String(selectedShipment.id));
            }
            // store_id required by API
            const storeId =
              currentUser?.user?.store_id || currentUser?.user?.storeId;
            if (storeId) {
              formData.append("store_id", String(storeId));
            }
            if (data?.reason) {
              formData.append("note", data.reason);
            }
            if (data?.store_id) {
              formData.append("store_id", data.store_id);
            }
            // Prefer numeric vehicle_id if available, otherwise pass typed vehicle number
            // if (selectedShipment?.vehicleId) {
              // formData.append("vehicle_id", String(selectedShipment.vehicleId));
            // } else if (data?.vehicleNumber) {
              formData.append("vehicle_id", String(data.vehicle_id));
            // }
            // delivery_datetime must be in format Y-m-d H:i:s
            const fromModal = data?.delivery_datetime;
            let deliveryFormatted = null;
            if (fromModal) {
              // Expecting input like YYYY-MM-DDTHH:MM or with seconds; normalize
              deliveryFormatted = fromModal.replace("T", " ");
              if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$/.test(deliveryFormatted)) {
                deliveryFormatted = `${deliveryFormatted}:00`;
              }
            } else if (selectedShipment?.timestamps?.delivery) {
              const d = new Date(selectedShipment.timestamps.delivery);
              const pad = (n) => String(n).padStart(2, "0");
              const y = d.getFullYear();
              const m = pad(d.getMonth() + 1);
              const day = pad(d.getDate());
              const hh = pad(d.getHours());
              const mm = pad(d.getMinutes());
              const ss = pad(d.getSeconds());
              deliveryFormatted = `${y}-${m}-${day} ${hh}:${mm}:${ss}`;
            }
            if (deliveryFormatted) {
              formData.append("delivery_datetime", deliveryFormatted);
            }
            // Append images as photos[0], photos[1], ...
            if (Array.isArray(data?.images)) {
              data.images.forEach((img, idx) => {
                const file =
                  img?.file instanceof Blob
                    ? img.file
                    : img instanceof Blob
                    ? img
                    : null;
                if (
                  file &&
                  (file.type?.startsWith("image/") ||
                    typeof file.type === "undefined")
                ) {
                  formData.append(`photos[${idx}]`, file);
                }
              });
            }

            // Call API
            setAddLoading(true);
            await AddStoreIssue(formData).then(()=>{
              loadShipments()
            }).finally(()=>{
              setAddLoading(false)
            });


            // Optimistically update UI
            if (selectedShipment?.id) {
              markShipmentDecision(selectedShipment.id, "Rejected", data);
            }
            const enriched = selectedShipment
              ? {
                  ...selectedShipment,
                  rejectionDetails: data,
                  storeDecision: "Rejected",
                }
              : null;
            setSelectedShipment(enriched);
            setShowRejectModal(false);
            setSuccessType("reject");
            setShowSuccessModal(true);
          } catch (err) {
            console.error("Failed to submit store issue", err);
          }
        }}
      />

      <RejectionDetailsModal
        show={showRejectionDetails}
        shipmentId={selectedShipment?.id}
        details={selectedShipment?.issues[0]}
        onClose={() => {
          setShowRejectionDetails(false);
          setSelectedShipment(null);
        }}
      />

      {/* 
      

      */}
    </div>
  );
};

export default StoreDashboard;
