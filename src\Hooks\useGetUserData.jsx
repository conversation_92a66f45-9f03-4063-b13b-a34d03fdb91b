"use client";

import { useState, useEffect } from "react";
import { loadSultan, clearSultan } from "../lib/sultanStorage";
import { useNavigate } from "react-router-dom";

export default function useGetUserData() {
  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setLoading(true);
        const data = await loadSultan();
        if (data) {
          setUserData(data);
        } else {
          setError("No user data found");
        }
      } catch (err) {
        console.error("Error loading user data:", err);
        setError("Failed to load user data");
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, []);

  const refreshUserData = async () => {
    try {
      setLoading(true);
      const data = await loadSultan();
      if (data) {
        setUserData(data);
        setError(null);
      } else {
        setError("No user data found");
      }
    } catch (err) {
      console.error("Error refreshing user data:", err);
      setError("Failed to refresh user data");
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    clearSultan();
    setUserData(null);
    setError(null);
    navigate("/");
    window.location.reload();
  };

  const clearUserData = () => {
    setUserData(null);
    setError(null);
  };

  return {
    userData,
    loading,
    error,
    refreshUserData,
    clearUserData,
    logout,
    isAuthenticated: !!userData,
    userRole: userData?.role,
    userId: userData?.user_id,
    userName: userData?.name
  };
}
