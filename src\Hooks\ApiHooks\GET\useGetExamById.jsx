import { useState, useEffect, useCallback } from 'react';
import { getExamById } from '../../../api/apiService';

export const useGetExamById = (examId) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [exam, setExam] = useState(null);

  const fetchExam = useCallback(async (id) => {
    if (!id) return;

    setLoading(true);
    setError(null);

    try {
      const response = await getExamById({ id });
      if (response.data.status === 'success') {
        setExam(response.data.data);
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'فشل في جلب بيانات الامتحان');
      }
    } catch (err) {
      const errorMessage = err.response?.data?.message || err.message || 'حدث خطأ أثناء جلب بيانات الامتحان';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (examId && typeof examId === 'number') {
      fetchExam(examId);
    }
  }, [examId, fetchExam]);

  return {
    exam,
    loading,
    error,
    fetchExam,
    reset: () => {
      setError(null);
      setExam(null);
    }
  };
};
