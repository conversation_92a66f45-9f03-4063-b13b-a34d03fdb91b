import React, { useState, useRef, useEffect } from "react";
import { X, Upload, Camera, AlertCircle, Loader2 } from "lucide-react";
import UploadImage from "../../utils/UploadImage";

const MarkInTransitModal = ({ 
  isOpen, 
  onClose, 
  shipment, 
  onSubmit, 
  isLoading = false 
}) => {
  const [pickupImages, setPickupImages] = useState([]);
  const [note, setNote] = useState("");
  const [errors, setErrors] = useState({});
  const modalRef = useRef(null);
  const firstInputRef = useRef(null);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setPickupImages([]);
      setNote("");
      setErrors({});
      // Focus first input when modal opens
      setTimeout(() => {
        if (firstInputRef.current) {
          firstInputRef.current.focus();
        }
      }, 100);
    }
  }, [isOpen]);

  // Handle ESC key
  useEffect(() => {
    const handleEsc = (e) => {
      if (e.key === "Escape" && isOpen) {
        onClose();
      }
    };
    document.addEventListener("keydown", handleEsc);
    return () => document.removeEventListener("keydown", handleEsc);
  }, [isOpen, onClose]);

  // Focus trap
  useEffect(() => {
    if (!isOpen) return;

    const modal = modalRef.current;
    if (!modal) return;

    const focusableElements = modal.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    const handleTabKey = (e) => {
      if (e.key === "Tab") {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            lastElement.focus();
            e.preventDefault();
          }
        } else {
          if (document.activeElement === lastElement) {
            firstElement.focus();
            e.preventDefault();
          }
        }
      }
    };

    modal.addEventListener("keydown", handleTabKey);
    return () => modal.removeEventListener("keydown", handleTabKey);
  }, [isOpen]);

  const validateForm = () => {
    const newErrors = {};

    if (pickupImages.length === 0) {
      newErrors.pickupImages = "At least one pickup image is required";
    }

    if (pickupImages.length > 5) {
      newErrors.pickupImages = "Maximum 5 images allowed";
    }

    if (note.length > 300) {
      newErrors.note = "Note must be 300 characters or less";
    }

    // Check file sizes
    const oversizedFiles = pickupImages.filter(file => file.size > 5 * 1024 * 1024);
    if (oversizedFiles.length > 0) {
      newErrors.pickupImages = "Each image must be 5MB or less";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const comment = {
      user: "Driver",
      message: note || "Pickup completed",
      time: new Date().toLocaleTimeString(),
      timestamp: new Date()
    };

    onSubmit({
      shipmentId: shipment.id,
      pickupImages,
      note: note.trim(),
      comment,
      timestamp: new Date()
    });
  };

  const handleImageUpload = (images) => {
    setPickupImages(images);
    // Clear image errors when new images are uploaded
    if (errors.pickupImages) {
      setErrors(prev => ({ ...prev, pickupImages: "" }));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed overflow-auto inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 p-4">
      <div 
        ref={modalRef}
        className="bg-white rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto"
        role="dialog"
        aria-modal="true"
        aria-labelledby="modal-title"
      >
        {/* Header */}
        <div className="p-6 border-b border-gray-200 flex justify-between items-center">
          <div className="text-right">
            <h2 id="modal-title" className="text-xl font-bold text-gray-900">
              Mark as In Transit
            </h2>
            <p className="text-gray-600 text-sm mt-1">
              Shipment #{shipment.id} • {shipment.bakeryType} • {shipment.branch}
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-2xl"
            aria-label="Close modal"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Pickup Images Section */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Camera className="w-5 h-5 text-blue-600" />
              <h3 className="font-medium text-gray-900">
                Driver Pickup Images <span className="text-red-500">*</span>
              </h3>
            </div>
            <p className="text-sm text-gray-600">
              Upload 1-5 clear photos of the items when you pick them up from the bakery
            </p>
            
            <UploadImage
              onImagesUpload={handleImageUpload}
              label=""
              placeholder="Click to upload pickup photos"
              multiple={true}
              maxImages={5}
            />
            
            {errors.pickupImages && (
              <div className="flex items-center gap-2 text-red-600 text-sm">
                <AlertCircle className="w-4 h-4" />
                {errors.pickupImages}
              </div>
            )}
            
            {pickupImages.length > 0 && (
              <div className="text-sm text-green-600">
                ✓ {pickupImages.length} image(s) selected
              </div>
            )}
          </div>

          {/* Optional Note Section */}
          <div className="space-y-3">
            <label htmlFor="note" className="block font-medium text-gray-900">
              Optional Note
            </label>
            <textarea
              ref={firstInputRef}
              id="note"
              value={note}
              onChange={(e) => {
                setNote(e.target.value);
                if (errors.note) {
                  setErrors(prev => ({ ...prev, note: "" }));
                }
              }}
              placeholder="Add any notes about the pickup (optional, max 300 characters)"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              rows={3}
              maxLength={300}
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>Optional information about the pickup</span>
              <span>{note.length}/300</span>
            </div>
            {errors.note && (
              <div className="flex items-center gap-2 text-red-600 text-sm">
                <AlertCircle className="w-4 h-4" />
                {errors.note}
              </div>
            )}
          </div>

          {/* Requirements Notice */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex gap-3">
              <AlertCircle className="w-5 h-5 text-blue-500 flex-shrink-0 mt-0.5" />
              <div className="text-sm text-blue-800">
                <p className="font-medium mb-2">📋 Requirements:</p>
                <ul className="space-y-1 text-xs">
                  <li>• At least 1 pickup image is required</li>
                  <li>• Maximum 5 images allowed</li>
                  <li>• Each image must be 5MB or less</li>
                  <li>• Images should clearly show the items being picked up</li>
                  <li>• Note is optional but helpful for tracking</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-gray-100 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-200 transition-colors font-medium"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading || pickupImages.length === 0}
              className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <Upload className="w-4 h-4" />
                  Mark as In Transit
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default MarkInTransitModal;
