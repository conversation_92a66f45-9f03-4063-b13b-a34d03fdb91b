import React, { useState, useMemo, useEffect } from "react";
import { Package, Truck, Clock, CheckCircle } from "lucide-react";
import { Button, Space } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import FactoryShipList from "../FactoryDashboard/components/FactoryShipList";
import ShipmentDetailsModal from "../../Components/Modals/ShipmentDetailsModal";
import AddShipmentModal from "../../Components/Modals/AddNewShipment";
import { getShipmentsData } from "../../services/shipmentApi";
import { getDistributerShipments } from "../../api/apiService";
import { useLocation } from "react-router-dom";

export default function DistributorDashboard() {
  const [shipments, setShipments] = useState([]);
  const [selectedShipment, setSelectedShipment] = useState(null);
  const [showAddShipment, setShowAddShipment] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const getDistributerShipmentsData = async () => {
    setLoading(true);
    await getDistributerShipments()
      .then((e) => setShipments(e))
      .catch((err) => setError(err.message))
      .finally(() => {
        setLoading(false);
      });
  };
  const loaiton = useLocation()
  // Fetch shipments from API
  useEffect(() => {
    getDistributerShipmentsData();
  }, []);

  return (
    <div className="space-y-6">
      {/* Loading State */}
      {loading && (
        <div className="flex justify-center items-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading shipments...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && !loading && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-center">
            <div className="text-red-600 mr-3">
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <div>
              <h3 className="text-red-800 font-medium">
                Error Loading Shipments
              </h3>
              <p className="text-red-600 mt-1">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="mt-3 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      {!loading && !error && (
        <>
          {/* Stats */}
          {/* <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
              <div className="flex items-center">
                <div className="bg-blue-100 p-3 rounded-full">
                  <Package className="w-6 h-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-2xl font-bold text-gray-900">
                    {shipments?.length}
                  </p>
                  <p className="text-gray-600">Total Shipments</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
              <div className="flex items-center">
                <div className="bg-green-100 p-3 rounded-full">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-2xl font-bold text-gray-900">
                    {
                      shipments?.data?.filter((s) => s.status === "Delivered")
                        .length
                    }
                  </p>
                  <p className="text-gray-600">Delivered</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
              <div className="flex items-center">
                <div className="bg-yellow-100 p-3 rounded-full">
                  <Truck className="w-6 h-6 text-yellow-600" />
                </div>
                <div className="ml-4">
                  <p className="text-2xl font-bold text-gray-900">
                    {
                      shipments?.data?.filter((s) => s.status === "In Transit")
                        .length
                    }
                  </p>
                  <p className="text-gray-600">In Transit</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
              <div className="flex items-center">
                <div className="bg-purple-100 p-3 rounded-full">
                  <Clock className="w-6 h-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-2xl font-bold text-gray-900">2.5 hours</p>
                  <p className="text-gray-600">Average Transit Time</p>
                </div>
              </div>
            </div>
          </div> */}

          {/* Shipments Section */}
          <div className="space-y-5">
            <div className="p-6 border-b border-gray-200 bg-white rounded-xl shadow-sm border">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-bold text-gray-900">
                  Current Shipments
                </h2>
                <Space>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => setShowAddShipment(true)}
                  >
                    Add Shipment
                  </Button>
                </Space>
              </div>
            </div>

            <FactoryShipList
            filterByRegion={false}
            filterByFactory={false}
            filterByDistributor={false}
              shipments={shipments?.data}
              setShipments={setShipments}
              setSelectedShipment={setSelectedShipment}
            />
          </div>
        </>
      )}

      {showAddShipment && (
        <AddShipmentModal
          showAddShipment={showAddShipment}
          setShowAddShipment={setShowAddShipment}
          setShipments={setShipments}
          shipments={shipments}
        />
      )}
    </div>
  );
}
