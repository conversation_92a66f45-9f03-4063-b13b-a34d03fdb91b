import {
  Table,
  Tag as AntdTag,
  Button,
  Space,
  Popconfirm,
  Input,
  Select,
  DatePicker,
  Row,
  Col,
  Card,
  Modal,
} from "antd";
import {
  Eye,
  Edit,
  Trash2,
  Search,
  Filter,
  X,
  Hash,
  Image,
  Package,
  Hash as NumberIcon,
  Building,
  Tag,
  Calendar,
  Settings,
  Search as SearchIcon,
  Filter as FilterIcon,
  Calendar as CalendarIcon,
  Clock,
  AlertTriangle,
  UserCircle,
  LocationEditIcon,
  LocateFixedIcon,
} from "lucide-react";
import {
  SearchOutlined,
  FilterOutlined,
  ClearOutlined,
} from "@ant-design/icons";
import { useState, useMemo } from "react";
import { useUser } from "../../../context/UserContext";
import DataTable, {
  getColumnDateProps,
  getColumnFiltersProps,
  getColumnNumberRange,
  getColumnSearchProps,
} from "../../../utils/DataTable";
import TableImage from "../../../utils/TableImage";
import dayjs from "dayjs";
import { formatDate } from "../../../lib/formateDate";
import users from "../../../data/users";
import FactoryShipList from "../../FactoryDashboard/components/FactoryShipList";
import { getShipmentsByDistributor } from "../../../data/allData";
import allData from "./../../../data/allData";
import EditDistributorModal from "./EditDistributorModal";

const { RangePicker } = DatePicker;

const DistributorsTable = ({
  data,
  setData,
  onEdit,
  onDelete,
  hiddenColumns = [],
}) => {
  const { currentUser, setUser, clearUser, isLoggedIn, hasRole } = useUser();
  const [editDistributor, setEditDistributor] = useState(null);
  // Filter states
  const [filters, setFilters] = useState({
    search: "",
    dateRange: null,
  });

  const [selectedDist, setSelectedDist] = useState(null);

  // Build distributors rows (drivers)
  const rows = useMemo(() => {
    const source =
      Array.isArray(data) && data.length > 0
        ? data
        : users.filter((u) => u.role === "driver");
    return source.map((u) => ({
      id: u.id,
      name: u.name,
      email: u.email,
      status: u.status,
      factory: u.factory,
      region: u.region,
      createdAt: u.created_at || new Date(),
    }));
  }, [data]);

  // Filter shipments based on current filters
  const filteredRows = useMemo(() => {
    return rows.filter((row) => {
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        const matchesSearch =
          row.name.toLowerCase().includes(searchTerm) ||
          row.email?.toLowerCase().includes(searchTerm) ||
          row.id.toString().includes(searchTerm);
        if (!matchesSearch) return false;
      }

      if (filters.dateRange && filters.dateRange.length === 2) {
        const createdDate = dayjs(row.createdAt);
        const startDate = filters.dateRange[0];
        const endDate = filters.dateRange[1];
        if (startDate && createdDate.isBefore(startDate, "day")) return false;
        if (endDate && createdDate.isAfter(endDate, "day")) return false;
      }

      return true;
    });
  }, [rows, filters]);

  // Reset filters
  const resetFilters = () => {
    setFilters({
      search: "",
      dateRange: null,
    });
  };

  // Update filter
  const updateFilter = (key, value) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const columns = [
    {
      title: (
        <div className="flex items-center gap-2">
          <Hash className="w-4 h-4 text-gray-600" />
          <span>ID</span>
        </div>
      ),
      dataIndex: "id",
      key: "id",
      fixed: true,
      render: (id) => (
        <div className="flex items-center gap-2">
          <Hash className="w-4 h-4 text-gray-600" />
          <span>{id}</span>
        </div>
      ),
      sorter: (a, b) => a.id - b.id,
      ...getColumnNumberRange("id"),
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <Package className="w-4 h-4 text-gray-600" />
          <span className="font-bold">Name</span>
        </div>
      ),
      dataIndex: "name",
      key: "name",
      sorter: (a, b) => a.name.localeCompare(b.name),
      ...getColumnSearchProps("name"),
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <LocateFixedIcon className="w-4 h-4 text-gray-600" />
          <span className="">Region</span>
        </div>
      ),
      dataIndex: "region",
      key: "region",
      render: (region, record) => {
        return <div onClick={() => console.log(record)}>{region.name}</div>;
      },
    },

    {
      title: (
        <div className="flex items-center gap-2">
          <Package className="w-4 h-4 text-gray-600" />
          <span className="font-bold">Factory</span>
        </div>
      ),
      dataIndex: "factory",
      key: "factory",
      render: (f, r) => {
        return <div>{f.name}</div>;
      },
    },

    {
      title: (
        <div className="flex items-center gap-2">
          <Tag className="w-4 h-4 text-gray-600" />
          <span>Email</span>
        </div>
      ),
      dataIndex: "email",
      key: "email",
      ...getColumnSearchProps("email"),
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <Tag className="w-4 h-4 text-gray-600" />
          <span>Status</span>
        </div>
      ),
      dataIndex: "status",
      key: "status",
      render: (status) => (
        <AntdTag color={status === "active" ? "green" : "red"}>
          {status}
        </AntdTag>
      ),
      filters: [
        { text: "Active", value: "active" },
        { text: "Inactive", value: "inactive" },
      ],
      onFilter: (value, record) => record.status === value,
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-gray-600" />
          <span>Created At</span>
        </div>
      ),
      dataIndex: "createdAt",
      key: "createdAt",
      render: (text, record) => {
        return (
          <div className="flex flex-col gap-2">
            <div className="">
              {formatDate(record.createdAt).split("at")[0]}

            </div>
            <div className="text-xs text-gray-500 ">
              {formatDate(record.createdAt).split("at")[1]}
            </div>
          </div>
        );
      },
      ...getColumnDateProps("createdAt"),
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <Settings className="w-4 h-4 text-gray-600" />
          <span>Actions</span>
        </div>
      ),
      key: "actions",
      render: (_, record) => (
        <Space size="middle">
          <button
            className="text-orange-600 border transition-all border-orange-600 px-2 py-1 rounded-md hover:bg-orange-100"
            onClick={() => setSelectedDist(record)}
          >
            Show Shipments
          </button>
          <Button
            onClick={() => {
              onEdit && onEdit(record);
              setEditDistributor(record);
            }}
            className="text-blue-600 hover:text-blue-700"
          >
            <Edit className="w-4 h-4" />
          </Button>
          {["super_admin"].includes(currentUser?.user?.role) && (
            <Popconfirm
              title="Delete distributor"
              description="Are you sure you want to delete this distributor?"
              onConfirm={() => {
                onDelete && onDelete(record);
                setData(data.filter((d) => d.id !== record.id));
              }}
              okText="Delete"
              okButtonProps={{ danger: true }}
              cancelText="Cancel"
            >
              <Button danger>
                <Trash2 className="w-4 h-4" />
              </Button>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ].filter((c) => !hiddenColumns.includes(c.dataIndex));
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200">
      <div className="p-6">
        <div className="space-y-4">
          {/* Data Table */}
          <div className="overflow-x-auto">
            <DataTable
              searchPlaceholder={"Search for distributors"}
              onAddClick={() => console.log("add new distributors")}
              table={{
                header: columns,
                rows: filteredRows,
              }}
            />
          </div>

          {/* Edit modal removed for regions dataset */}
        </div>
      </div>

      <Modal
        className="!w-[90vw] max-w-6xl"
        open={Boolean(selectedDist)}
        onCancel={() => setSelectedDist(null)}
        footer={null}
        title={
          <>
            <h2 className=" flex items-center gap-2 text-blue-950 font-bold">
              <UserCircle />
              {selectedDist?.name}'s Shipments
            </h2>
          </>
        }
      >
        <FactoryShipList
          hiddenColumns={["distributor"]}
          withFilters={true}
          filterByRegion={false}
          filterByFactory={false}
          filterByRoute={true}
          filterByDistributor={false}
          setSelectedShipment={() => {}}
          shipments={getShipmentsByDistributor(
            allData.regions,
            { id: selectedDist?.id},
            allData.stores
          )}
          setShipments={[]}
        />
      </Modal>

      {editDistributor && (
        <EditDistributorModal
          distributor={editDistributor}
          onClose={() => setEditDistributor(null)}
          onSave={(updated) => {
            setData((prev) =>
              prev.map((d) => (d.id === updated.id ? updated : d))
            );
            setEditDistributor(null);
          }}
        />
      )}
    </div>
  );
};

export default DistributorsTable;
