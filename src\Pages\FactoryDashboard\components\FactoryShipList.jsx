import {
  Table,
  Tag as AntdTag,
  Button,
  Space,
  Popconfirm,
  Input,
  Select,
  DatePicker,
  Row,
  Col,
  Card,
  message,
} from "antd";
import {
  Eye,
  Edit,
  Trash2,
  Search,
  Filter,
  X,
  Hash,
  Image,
  Package,
  Hash as NumberIcon,
  User,
  Building,
  Tag,
  Calendar,
  Settings,
  Search as SearchIcon,
  Filter as FilterIcon,
  Calendar as CalendarIcon,
  Clock,
  AlertTriangle,
  Locate,
  LocateFixedIcon,
  Car,
  Factory,
  User2,
  DoorOpen,
} from "lucide-react";
import {
  SearchOutlined,
  FilterOutlined,
  ClearOutlined,
} from "@ant-design/icons";
import { useState, useMemo, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import AddShipmentModal from "../../../Components/Modals/AddNewShipment";
import EditShipmentModal from "../../../Components/Modals/EditShipment";
import { useUser } from "../../../context/UserContext";
import DataTable, {
  getColumnDateProps,
  getColumnFiltersProps,
  getColumnNumberRange,
  getColumnSearchProps,
} from "./../../../utils/DataTable";
import TableImage from "../../../utils/TableImage";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import { formatDate } from "../../../lib/formateDate";
import StoreDetailsModal from "./../../Stores/components/StoreDetailsModal";
import data, {
  getAllFactories,
  getRoutesByRegions,
} from "../../../data/allData";
import ShipmentDetailsModal from "../../../Components/Modals/ShipmentDetailsModal";
import { getRegionsData, getStoresData } from "../../../services/shipmentApi";
import {
  getVihiclesByRegionId,
  getRegionRoutes,
  deleteShipment,
} from "../../../api/apiService";

const { RangePicker } = DatePicker;

const FactoryShipList = ({
  setSelectedShipment,
  shipments,
  setShipments,
  withFilters = true,
  filterByRegion = true,
  filterByFactory = true,
  filterByRoute = true,
  filterByDistributor = true,
  hiddenColumns = [],
  serverPagination = null,
  onPaginate = () => {},
}) => {
  console.log(shipments, "shipments");
  const { currentUser, setUser, clearUser, isLoggedIn, hasRole } = useUser();
  const [showEditShipment, setShowEditShipment] = useState(false);
  const [selectedShipmentForEdit, setSelectedShipmentForEdit] = useState(null);
  const location = useLocation();
  const navigate = useNavigate();
  const [rowData, setRowData] = useState(null);
  // Filter states
  const [filters, setFilters] = useState({
    search: "",
    bakeryType: "",
    vehicleNumber: "",
    region: [],
    route: [],
    factory: [],
    distributor: [],
    dateRange: null,
  });
  const [showFilters, setShowFilters] = useState(false);

  // Enable parsing non-standard date formats returned by backend
  dayjs.extend(customParseFormat);

  // API data state
  const [apiRegions, setApiRegions] = useState([]);
  const [apiStores, setApiStores] = useState([]);
  const [loadingApiData, setLoadingApiData] = useState(false);
  const [vehiclesOptions, setVehiclesOptions] = useState([]);
  const [loadingVehicles, setLoadingVehicles] = useState(false);
  const [apiRoutes, setApiRoutes] = useState([]);
  const [loadingRoutes, setLoadingRoutes] = useState(false);

  // Fetch regions and stores from API
  useEffect(() => {
    const fetchApiData = async () => {
      try {
        setLoadingApiData(true);
        const [regionsResult, storesResult] = await Promise.all([
          getRegionsData(),
          getStoresData(),
        ]);

        if (regionsResult.error) {
          console.error("Error fetching regions:", regionsResult.error);
        } else {
          console.log( "regionsResult" , regionsResult)
          setApiRegions(regionsResult.regions);
        }

        if (storesResult.error) {
          console.error("Error fetching stores:", storesResult.error);
        } else {
          setApiStores(storesResult.stores);
        }
      } catch (err) {
        console.error("Error fetching API data:", err);
      } finally {
        setLoadingApiData(false);
      }
    };

    fetchApiData();
  }, []);

  // Load vehicles based on selected regions
  useEffect(() => {
    const fetchVehicles = async () => {
      try {
        if (!filters?.region || filters.region.length === 0) {
          console.log("render no region selected");
          const userRegVehicles = await getVihiclesByRegionId(
            currentUser?.user?.region_id
          );
          console.log("userRegVehicles", userRegVehicles);
          setVehiclesOptions(
            userRegVehicles.data.map((item) => ({
              label: item.vehicle_number,
              value: item.vehicle_number,
            }))
          );

          return;
        }
        setLoadingVehicles(true);
        // Fetch for all selected regions and merge uniquely
        const results = await Promise.all(
          filters.region.map((regId) => getVihiclesByRegionId(regId))
        ).then((res) => res.map((r) => r.data));
        const merged = [];
        const seen = new Set();
        results
          .filter(Boolean)
          .flat()
          .forEach((veh) => {
            const num = veh?.vehicle_number || veh?.number || veh?.plate || "";
            const key = String(num).trim();
            if (key && !seen.has(key)) {
              seen.add(key);
              merged.push({ label: key, value: key });
            }
          });
        setVehiclesOptions(
          merged.sort((a, b) => a.label.localeCompare(b.label))
        );
      } catch (e) {
        console.error("Error loading vehicles:", e);
        setVehiclesOptions([]);
      } finally {
        setLoadingVehicles(false);
      }
    };

    fetchVehicles();
  }, [filters?.region]);

  // Load routes using API based on selected regions (or user's region if none)
  useEffect(() => {
    const fetchRoutes = async () => {
      try {
        const selectedRegionIds =
          filterByRegion && filters?.region && filters.region.length > 0
            ? filters.region
            : currentUser?.user?.region_id
            ? [currentUser.user.region_id]
            : [];

        if (selectedRegionIds.length === 0) {
          setApiRoutes([]);
          return;
        }

        setLoadingRoutes(true);
        const results = await Promise.all(
          selectedRegionIds.map((regId) => getRegionRoutes(regId))
        );
        const merged = [];
        const seen = new Set();
        results
          .map((r) =>
            Array.isArray(r?.data) ? r.data : Array.isArray(r) ? r : []
          )
          .flat()
          .forEach((route) => {
            const id = route?.id;
            if (id != null && !seen.has(id)) {
              seen.add(id);
              merged.push(route);
            }
          });
        setApiRoutes(
          merged.sort((a, b) => (a?.name || "").localeCompare(b?.name || ""))
        );
      } catch (e) {
        console.error("Error loading routes:", e);
        setApiRoutes([]);
      } finally {
        setLoadingRoutes(false);
      }
    };

    fetchRoutes();
  }, [filters?.region, currentUser]);

  // Get unique values for filter options
  const uniqueBakeryTypes = useMemo(() => {
    const values = [
      ...new Set(
        shipments?.map((s) => s?.bakeryType || s?.bakery_type).filter(Boolean)
      ),
    ];
    return values.sort();
  }, [shipments]);

  const uniqueVehicleNumbers = useMemo(() => {
    const values = [
      ...new Set(
        shipments
          ?.map((s) => s?.vehicleNumber || s?.vehicle?.vehicle_number)
          .filter(Boolean)
      ),
    ];
    return values.sort();
  }, [shipments]);

  const uniqueRegions = useMemo(() => {
    // Use API regions if available, fallback to local data
    const regions = apiRegions.length > 0 ? apiRegions : data.regions;
    return regions.sort((a, b) =>
      (a.name || a.id).localeCompare(b.name || b.id)
    );
  }, [apiRegions]);

  const uniqueRoutes = useMemo(() => {
    if (apiRoutes.length > 0) return apiRoutes;
    // Fallback to local data utility
    const filtersOption = filters?.region.length > 0 ? filters?.region : null;
    const values = getRoutesByRegions(data.regions, filtersOption);
    return values.sort();
  }, [apiRoutes, filters?.region, location.pathname]);

  // Selected route and its stores (when a single route is selected)
  const selectedRoute = useMemo(() => {
    if (!filters?.route || filters.route.length === 0) return null;
    const selectedId = filters.route[0];
    return (
      apiRoutes.find((r) => r?.id === selectedId) ||
      uniqueRoutes.find((r) => r?.id === selectedId) ||
      null
    );
  }, [filters?.route, apiRoutes, uniqueRoutes]);

  const selectedRouteStores = useMemo(() => {
    const route = selectedRoute;
    if (!route) return [];
    // Prefer route.route_stores[].store if present
    if (Array.isArray(route?.route_stores) && route.route_stores.length > 0) {
      return route.route_stores
        .map((rs) => rs?.store)
        .filter(Boolean)
        .map((s) => ({ id: s.id, name: s.name, code: s.code }));
    }
    // Some APIs might return route.stores directly
    if (Array.isArray(route?.stores) && route.stores.length > 0) {
      return route.stores.map((s) => ({
        id: s.id,
        name: s.name,
        code: s.code,
      }));
    }
    return [];
  }, [selectedRoute]);

  const uniqueFactories = useMemo(() => {
    console.log("getAllFactories(data.regions)", getAllFactories(data.regions));
    return getAllFactories(data.regions);
  }, [shipments]);

  const uniqueDistributors = useMemo(() => {
    const map = new Map();
    shipments?.forEach((s) => {
      if (s?.distributor?.id && !map.has(s.distributor.id)) {
        map.set(s.distributor.id, s.distributor);
      }
    });
    return Array.from(map.values());
  }, [shipments]);

  // Filter shipments based on current filters
  const filteredShipments = useMemo(() => {
    console.log("filters", filters);
    return shipments?.filter((shipment) => {
      // Global search (safe across shapes)
      if (filters.search) {
        const searchTerm = String(filters.search).toLowerCase();
        const haystack = [
          shipment?.bakeryType || shipment?.bakery_type,
          shipment?.driver,
          shipment?.branch,
          shipment?.region?.name || shipment?.region,
          shipment?.route?.name || shipment?.route,
          shipment?.vehicleNumber || shipment?.vehicle?.vehicle_number,
          shipment?.id,
        ]
          .filter((v) => v != null)
          .map((v) => String(v).toLowerCase());
        const matchesSearch = haystack.some((v) => v.includes(searchTerm));
        if (!matchesSearch) return false;
      }

      // Bakery Type filter (text contains)
      if (filters.bakeryType && filters.bakeryType.trim().length > 0) {
        const needle = filters.bakeryType.toLowerCase();
        const hay = String(
          shipment?.bakeryType || shipment?.bakery_type || ""
        ).toLowerCase();
        if (!hay.includes(needle)) return false;
      }

      // Vehicle Number filter (text contains)
      if (filters.vehicleNumber && filters.vehicleNumber.trim().length > 0) {
        const needle = filters.vehicleNumber.toLowerCase();
        const hay = String(
          shipment?.vehicleNumber || shipment?.vehicle?.vehicle_number || ""
        ).toLowerCase();
        if (!hay.includes(needle)) return false;
      }

      // Region filter (prefer numeric id, fallback to object.id or name)
      if (filters.region.length > 0) {
        const shipmentRegionCandidate =
          shipment?.region_id ??
          (shipment?.region && typeof shipment.region === "object"
            ? shipment.region.id
            : shipment?.region);
        // Normalize to number when possible
        const regionValue =
          typeof shipmentRegionCandidate === "string"
            ? Number(shipmentRegionCandidate)
            : shipmentRegionCandidate;
        if (!filters.region.includes(regionValue)) {
          return false;
        }
      }

      // Route filter (prefer numeric id, fallback to object.id or name)
      if (filters.route.length > 0) {
        const shipmentRouteCandidate =
          shipment?.route_id ??
          (shipment?.route && typeof shipment.route === "object"
            ? shipment.route.id
            : shipment?.route);
        const routeValue =
          typeof shipmentRouteCandidate === "string"
            ? Number(shipmentRouteCandidate)
            : shipmentRouteCandidate;
        if (!filters.route.includes(routeValue)) {
          return false;
        }
      }

      // Factory filter (compare by id)
      if (
        filters.factory.length > 0 &&
        !filters.factory.includes(shipment?.factory?.id)
      ) {
        return false;
      }

      // Distributor filter (prefer numeric id, fallback to object.id)
      if (filters.distributor.length > 0) {
        const shipmentDistributorCandidate =
          shipment?.distributor_id ?? shipment?.distributor?.id;
        if (!filters.distributor.includes(shipmentDistributorCandidate)) {
          return false;
        }
      }

      // Date range filter (robust parsing for multiple backend formats)
      if (filters.dateRange && filters.dateRange.length === 2) {
        const created = shipment?.timestamps?.created || shipment?.created_at;
        if (!created) return false;
        const formats = [
          "DD/MM/YYYY hh:mm A",
          "MM/DD/YYYY hh:mm A",
          "YYYY-MM-DD HH:mm",
          "YYYY-MM-DD HH:mm:ss",
        ];
        let shipmentDate = dayjs(created);
        if (!shipmentDate.isValid()) {
          for (const fmt of formats) {
            const parsed = dayjs(created, fmt, true);
            if (parsed.isValid()) {
              shipmentDate = parsed;
              break;
            }
          }
        }
        if (!shipmentDate.isValid()) return false;
        const startDate = filters.dateRange[0];
        const endDate = filters.dateRange[1];
        if (startDate && shipmentDate.isBefore(startDate, "day")) return false;
        if (endDate && shipmentDate.isAfter(endDate, "day")) return false;
      }

      return true;
    });
  }, [shipments, filters]);

  // Flatten/derive fields required by table columns/filters
  const tableRows = useMemo(() => {
    return filteredShipments?.map((s) => ({
      ...s,
      // normalized fields for table columns/search
      bakeryType: s?.bakeryType || s?.bakery_type || "",
      vehicleNumber: s?.vehicleNumber || s?.vehicle?.vehicle_number || "",
      regionName: s?.route?.region?.name || s?.region?.name || s?.region || "",
      routeName: s?.route?.name || s?.route || "",
      distributorName: s?.distributor?.name || s?.driver || "",
      factoryName:
        s?.factory?.name || s?.production?.name || s?.productionName || "",
      qualityStatus: s?.qualityStatus || s?.quality_status || "",
      storesCount:
        s?.storesCount ??
        s?.route?.route_stores?.length ??
        s?.route?.stores?.length ??
        0,
      storeIssuesCount: Array.isArray(s?.issues) ? s.issues.length : 0,
      driverPickupAt: s?.timestamps?.driverPickup || s?.exit_time || null,
      createdAt:
        s?.created_at || s?.timestamps?.created || s?.rawCreatedAt || null,
    }));
  }, [filteredShipments]);

  // Unique route count based on current filters (including selected region)
  const filteredUniqueRouteCount = useMemo(() => {
    const set = new Set(filteredShipments?.map((s) => s.route));
    return set.size;
  }, [filteredShipments]);

  // Helpers to compute derived statuses
  const computeQualityStatus = (record) => {
    if (record?.status === "Rejected") return "Rejected";
    if (["Confirmed"].includes(record?.status)) return "Confirmed";
    return "Pending";
  };

  const computeStoreStatus = (record) => {
    if (record?.status === "Cancelled") return "Rejected";
    if (record?.timestamps?.display || record?.timestamps?.branchReceive)
      return "Confirmed";
    return "Pending";
  };

  const statusSortPriority = { Confirmed: 2, Pending: 1, Rejected: 0 };

  // Reset filters
  const resetFilters = () => {
    setFilters({
      search: "",
      bakeryType: "",
      vehicleNumber: "",
      region: [],
      route: [],
      factory: [],
      distributor: [],
      dateRange: null,
    });
  };

  // Update filter
  const updateFilter = (key, value) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  // Sync filters with URL search params
  useEffect(() => {
    const params = new URLSearchParams(location.search);

    const parseCsv = (str) => (str ? str.split(",").filter(Boolean) : []);
    const toNumIfNumeric = (v) => {
      const n = Number(v);
      return Number.isNaN(n) ? v : n;
    };

    const start = params.get("start");
    const end = params.get("end");

    setFilters((prev) => ({
      ...prev,
      bakeryType: params.get("bakeryType") || "",
      vehicleNumber: params.get("vehicleNumber") || "",
      // region ids are numeric in local data; coerce when possible
      region: parseCsv(params.get("region")).map(toNumIfNumeric),
      // route ids can be strings like RY-1
      route: parseCsv(params.get("route")),
      factory: parseCsv(params.get("factory")),
      distributor: parseCsv(params.get("distributor")),
      dateRange:
        start || end
          ? [start ? dayjs(start) : null, end ? dayjs(end) : null]
          : null,
    }));

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location.pathname]);

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const setCsv = (key, arr) => {
      if (arr && arr.length > 0) params.set(key, arr.join(","));
      // else params.delete(key);
    };

    // Strings
    if (filters.bakeryType && filters.bakeryType.trim())
      params.set("bakeryType", filters.bakeryType.trim());
    else params.delete("bakeryType");

    if (filters.vehicleNumber && filters.vehicleNumber.trim())
      params.set("vehicleNumber", filters.vehicleNumber.trim());
    else params.delete("vehicleNumber");

    // Multi-selects (ids)
    setCsv("region", filters.region);
    setCsv("route", filters.route);
    setCsv("factory", filters.factory);
    setCsv("distributor", filters.distributor);

    // Date range
    if (filters.dateRange && filters.dateRange.length === 2) {
      const [s, e] = filters.dateRange;
      if (s) params.set("start", dayjs(s).format("YYYY-MM-DD"));
      else params.delete("start");
      if (e) params.set("end", dayjs(e).format("YYYY-MM-DD"));
      else params.delete("end");
    } else {
      params.delete("start");
      params.delete("end");
    }

    navigate(
      { pathname: location.pathname, search: params.toString() },
      { replace: true }
    );
  }, [filters, location.pathname, location.search, navigate]);

  const columns = [
    {
      title: (
        <div className="flex items-center gap-2">
          <Hash className="w-4 h-4 text-gray-600" />
          <span>ID</span>
        </div>
      ),
      dataIndex: "id",
      key: "id",
      fixed: true,
      render: (id, rcord) => (
        <div
          onClick={() => console.log(rcord)}
          className="flex items-center gap-2"
        >
          <Hash className="w-4 h-4 text-gray-600" />
          <span>{id}</span>
        </div>
      ),
      sorter: (a, b) => a.id - b.id,
      ...getColumnNumberRange("id"),
    },

    {
      title: (
        <div className="flex items-center gap-2">
          <Package className="w-4 h-4 text-gray-600" />
          <span>Bakery Type</span>
        </div>
      ),
      dataIndex: "bakeryType",
      key: "bakeryType",
      sorter: (a, b) => (a.bakeryType || "").localeCompare(b.bakeryType || ""),
      ...getColumnSearchProps("bakeryType"),
    },

    {
      title: (
        <div className="flex items-center gap-2">
          <LocateFixedIcon className="w-4 h-4 text-gray-600" />
          <span>Region</span>
        </div>
      ),
      dataIndex: "regionName",
      key: "regionName",
      sorter: (a, b) => (a.regionName || "").localeCompare(b.regionName || ""),
      ...getColumnSearchProps("regionName"),
      render: (_, record) => {
        return (
          <div className="" onClick={() => console.log(record)}>
            {record?.route?.region?.name ||
              record?.region?.name ||
              record?.regionName}
          </div>
        );
      },
    },

    {
      title: (
        <div className="flex items-center gap-2">
          <Locate className="w-4 h-4 text-gray-600" />
          <span>Route</span>
        </div>
      ),
      dataIndex: "routeName",
      key: "routeName",
      sorter: (a, b) => (a.routeName || "").localeCompare(b.routeName || ""),
      ...getColumnSearchProps("routeName"),
      render: (_, record) => {
        return (
          <div className="" onClick={() => console.log(record)}>
            {record?.route?.name || record?.routeName}
          </div>
        );
      },
    },

    {
      title: (
        <div className="flex items-center gap-2">
          <User2 className="w-4 h-4 text-gray-600" />
          <span>Distributor</span>
        </div>
      ),
      dataIndex: "distributor_name",
      key: "distributor_name",
      render: (_, record) => {
        return (
          <div className="" onClick={() => console.log(record)}>
            {record?.distributor?.name ||
              record?.driver ||
              record?.distributor_name}
          </div>
        );
      },
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <Factory className="w-4 h-4 text-gray-600" />
          <span>Production</span>
        </div>
      ),
      dataIndex: "production_name",
      key: "production_name",
      sorter: (a, b) =>
        (a.production_name || "").localeCompare(b.production_name || ""),
      ...getColumnSearchProps("production_name"),
      render: (_, record) => {
        return (
          <div className="" onClick={() => console.log(record)}>
            {record?.production_name}
          </div>
        );
      },
    },

    {
      title: (
        <div className="flex items-center gap-2">
          <Tag className="w-4 h-4 text-gray-600" />
          <span>Quality Status</span>
        </div>
      ),
      dataIndex: "qualityStatus",
      key: "qualityStatus",
      render: (status) => {
        const color =
          status === "confirmed"
            ? "green"
            : status === "rejected"
            ? "red"
            : "orange";
        return (
          <AntdTag color={color} className="capitalize">
            {status}
          </AntdTag>
        );
      },
      sorter: (a, b) => {
        const qa = computeQualityStatus(a);
        const qb = computeQualityStatus(b);
        return statusSortPriority[qa] - statusSortPriority[qb];
      },
    },

    {
      title: (
        <div className="flex items-center gap-2">
          <NumberIcon className="w-4 h-4 text-gray-600" />
          <span>Quantity</span>
        </div>
      ),
      dataIndex: "quantity",
      key: "quantity",
      ...getColumnNumberRange("quantity"),
      sorter: (a, b) => a.quantity - b.quantity,
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <Car className="w-4 h-4 text-gray-600" />
          <span>Vehicle Number</span>
        </div>
      ),
      dataIndex: "vehicle_number",
      key: "vehicle_number",
      sorter: (a, b) =>
        (a.vehicle_number || "").localeCompare(b.vehicle_number || ""),
      render: (_, record) => {
        return (
          <div className="" onClick={() => console.log(record)}>
            {record?.vehicle_number || record?.vehicle?.vehicle_number}
          </div>
        );
      },
      ...getColumnSearchProps("vehicleNumber"),
    },

    {
      title: (
        <div className="flex items-center gap-2">
          <Building className="w-4 h-4 text-gray-600" />
          <span>Stores Count</span>
        </div>
      ),
      dataIndex: "storesCount",
      key: "storesCount",
      render: (_, record) => {
        return (
          <div
            className=""
            onClick={() =>
              console.log(
                "shipments",
                record?.stores_count ||
                  record?.route?.route_stores?.length ||
                  record?.route?.stores?.length ||
                  0
              )
            }
          >
            {record?.storesCount ||
              record?.route?.route_stores?.length ||
              record?.route?.stores?.length ||
              0}
          </div>
        );
      },
      // sorter: (a, b) => (a.storesCount || 0) - (b.storesCount || 0),
    },

    {
      title: (
        <div className="flex items-center gap-2">
          <AlertTriangle className="w-4 h-4 text-gray-600" />
          <span>Store Issues</span>
        </div>
      ),
      dataIndex: "issues",
      key: "issues",
      render: (_, record) => {
        return (
          <span
            className={
              record.issues?.length
                ? "text-red-500 font-bold "
                : "text-green-500 font-bold"
            }
            onClick={() => console.log(record)}
          >
            {record?.issues ? record?.issues?.length : 0}
          </span>
        );
      },
      sorter: (a, b) => (a.issues.length || 0) - (b.issues.length || 0),
    },

    {
      title: (
        <div className="flex items-center gap-2">
          <DoorOpen className="w-4 h-4 text-gray-600" />
          <span>Exit Time</span>
        </div>
      ),
      dataIndex: "driverPickupAt",
      key: "driverPickupAt",
      render: (date, record) => {
        return (
          <div className="" onClick={() => console.log(record)}>
            {date}
            {/* {formatDate(date)} */}
          </div>
        );
      },
    },

    {
      title: (
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-gray-600" />
          <span>Created At</span>
        </div>
      ),
      dataIndex: "createdAt",
      key: "createdAt",
      render: (date, record) => {
        return date;
        // try {
        //   return (
        //     <div className="" onClick={() => console.log(record)}>
        //       {formatDate(date)}
        //     </div>
        //   );
        // } catch (e) {
        //   return (
        //     <div className="" onClick={() => console.log(record)}>
        //       Invalid Date
        //     </div>
        //   );
        // }
      },
      ...getColumnDateProps("createdAt"),
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <Settings className="w-4 h-4 text-gray-600" />
          <span>Actions</span>
        </div>
      ),
      key: "actions",
      render: (_, record) => (
        <Space size="middle" className="not-target">
          {["super_admin", "factory"].includes(currentUser?.user?.role) &&
            computeQualityStatus(record) === "Pending" && (
              <>
                <Button
                  color="blue"
                  onClick={() => {
                    setSelectedShipmentForEdit(record);
                    setShowEditShipment(true);
                  }}
                  className=" border text-blue-600 border-blue-600 bg-white hover:text-blue-700"
                >
                  <Edit className="w-4 h-4" />
                </Button>

                <Popconfirm
                  className="not-target"
                  rootClassName="not-target"
                  title="Delete the shipment"
                  description="Are you sure to delete this shipment?"
                  onConfirm={() => {
                    handleDeleteShipement(record);
                  }}
                  onCancel={() => {}}
                  okText="Delete"
                  okButtonProps={{
                    danger: true,
                  }}
                  cancelText="No"
                >
                  <Button danger className="bg-white">
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </Popconfirm>
              </>
            )}
        </Space>
      ),
    },
  ].filter((item) => !hiddenColumns?.includes(item.dataIndex));


  const handleDeleteShipement = async (record) => {
    try {
      await deleteShipment(record.id);
      setShipments((prev) => (Array.isArray(prev) ? prev.filter((s) => s.id !== record.id) : prev));
      message.success("Shipment deleted successfully");
    } catch (e) {
      console.error("Failed to delete shipment", e);
      message.error("Failed to delete shipment");
    }
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200">
      <div className="p-6">
        <div className="space-y-4">
          {/* Filter Section */}
          {withFilters && (
            <div className="">
              <div className="flex items-center justify-end mb-4">
                <Button
                  icon={<ClearOutlined />}
                  onClick={resetFilters}
                  className="not-target"
                >
                  Reset Filters
                </Button>
              </div>
              <Row gutter={[16, 16]}>
                {/* Bakery Type Filter */}
                <Col xs={24} sm={12} md={8} lg={6}>
                  <div>
                    <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
                      <Package className="w-4 h-4 text-gray-600" />
                      <span>Bakery Type</span>
                    </label>
                    <Input
                      size="large"
                      placeholder="Type bakery"
                      value={filters.bakeryType}
                      onChange={(e) =>
                        updateFilter("bakeryType", e.target.value)
                      }
                    />
                  </div>
                </Col>

                {/* Vehicle Number Filter */}
                <Col xs={24} sm={12} md={8} lg={6}>
                  <div>
                    <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
                      <Car className="w-4 h-4 text-gray-600" />
                      <span>Vehicle Number</span>
                    </label>
                    <Select
                      size="large"
                      showSearch
                      allowClear
                      placeholder={
                        filters?.region?.length > 0
                          ? loadingVehicles
                            ? "Loading vehicles..."
                            : "Select vehicle number"
                          : "Select region(s) first"
                      }
                      value={filters.vehicleNumber || undefined}
                      onChange={(value) =>
                        updateFilter("vehicleNumber", value || "")
                      }
                      style={{ width: "100%" }}
                      loading={loadingVehicles}
                      // disabled={loadingVehicles || (filters?.region?.length || 0) === 0 || filterByRegion}
                      options={vehiclesOptions}
                      filterOption={(input, option) =>
                        (option?.label ?? "")
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      }
                    />
                  </div>
                </Col>

                {/* Region Filter */}
                {filterByRegion && (
                  <Col xs={24} sm={12} md={8} lg={6}>
                    <div>
                      <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
                        <LocateFixedIcon className="w-4 h-4 text-gray-600" />
                        <span>Region</span>
                      </label>
                      <Select
                        size="large"
                        mode="multiple"
                        placeholder={
                          loadingApiData
                            ? "Loading regions..."
                            : "Select regions"
                        }
                        value={filters.region}
                        onChange={(value) => {
                          console.log(value);
                          updateFilter("region", value);
                        }}
                        style={{ width: "100%" }}
                        allowClear
                        loading={loadingApiData}
                        disabled={loadingApiData}
                        options={uniqueRegions.map((v) => ({
                          label: v.name,
                          value: v.id,
                        }))}
                      />
                    </div>
                  </Col>
                )}
                {/* Route Filter */}
                {filterByRoute && (
                  <Col xs={24} sm={12} md={8} lg={6}>
                    <div>
                      <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
                        <AntdTag className="w-4 h-4 text-gray-600" />
                        <span>Route</span>
                        <span className="text-xs text-gray-500">
                          ({uniqueRoutes.length})
                        </span>
                      </label>
                      <Select
                        size="large"
                        placeholder={
                          loadingRoutes ? "Loading routes..." : "Select route"
                        }
                        value={
                          filters.route && filters.route.length > 0
                            ? filters.route[0]
                            : undefined
                        }
                        onChange={(value) =>
                          updateFilter("route", value ? [value] : [])
                        }
                        style={{ width: "100%" }}
                        allowClear
                        loading={loadingRoutes}
                        disabled={loadingRoutes}
                        options={uniqueRoutes.map((v) => ({
                          label: v.name,
                          value: v.id,
                        }))}
                      />
                      {filters.route && filters.route.length > 0 && (
                        <div className="mt-2">
                          <div className="text-xs text-gray-500 mb-1">
                            Stores in route ({selectedRouteStores.length})
                          </div>
                          {selectedRouteStores.length > 0 ? (
                            <div className="flex flex-wrap gap-2">
                              {selectedRouteStores.map((s) => (
                                <span
                                  key={s.id}
                                  className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs border"
                                >
                                  {s.name}
                                  {s.code ? ` (${s.code})` : ""}
                                </span>
                              ))}
                            </div>
                          ) : (
                            <div className="text-xs text-gray-400">
                              No stores found for this route
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </Col>
                )}

                {/* Factory Filter */}
                {filterByFactory && (
                  <Col xs={24} sm={12} md={8} lg={6}>
                    <div>
                      <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
                        <Building className="w-4 h-4 text-gray-600" />
                        <span>Factory</span>
                      </label>
                      <Select
                        size="large"
                        mode="multiple"
                        placeholder="Select factories"
                        value={filters.factory}
                        onChange={(value) => updateFilter("factory", value)}
                        style={{ width: "100%" }}
                        allowClear
                        options={uniqueFactories.map((v) => ({
                          label: v?.name,
                          value: v.id,
                        }))}
                      />
                    </div>
                  </Col>
                )}

                {/* Distributor Filter */}
                {filterByDistributor && (
                  <Col xs={24} sm={12} md={8} lg={6}>
                    <div>
                      <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
                        <User className="w-4 h-4 text-gray-600" />
                        <span>Distributor</span>
                      </label>
                      <Select
                        size="large"
                        mode="multiple"
                        placeholder="Select distributors"
                        value={filters.distributor}
                        onChange={(value) => updateFilter("distributor", value)}
                        style={{ width: "100%" }}
                        allowClear
                        options={uniqueDistributors.map((v) => ({
                          label: v?.name,
                          value: v?.id,
                        }))}
                      />
                    </div>
                  </Col>
                )}

                {/* Date Range Filter */}
                <Col xs={24} sm={12} md={8} lg={6}>
                  <div>
                    <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
                      <CalendarIcon className="w-4 h-4 text-gray-600" />
                      <span>Date Range</span>
                    </label>
                    <RangePicker
                      size="large"
                      value={filters.dateRange}
                      onChange={(dates) => updateFilter("dateRange", dates)}
                      style={{ width: "100%" }}
                      placeholder={["Start Date", "End Date"]}
                    />
                  </div>
                </Col>
              </Row>
            </div>
          )}

          {/* Data Table */}
          <div className="overflow-x-auto">
            <DataTable
              searchPlaceholder={"Search Shipment"}
              onAddClick={() => console.log("add shipment")}
              table={{
                header: columns,
                rows: tableRows,
              }}
              pagination={{
                current: serverPagination?.current_page || undefined,
                pageSize: serverPagination?.per_page || undefined,
                total: serverPagination?.total ?? undefined,
                onChange: (page, pageSize) => onPaginate(page, pageSize),
                onShowSizeChange: (current, size) => onPaginate(1, size),
              }}
              rowClassName={(record) =>
                "hover:bg-orange-100 hover:scale-[1.01] transition-all  cursor-pointer"
              }
              onRow={(record, rowIndex) => {
                return {
                  onClick: (e) => {
                    if (e.target.closest(".not-target")) {
                      return;
                    }
                    setSelectedShipment(record);
                    setRowData(record);
                  },
                };
              }}
            />
          </div>

          {showEditShipment && (
            <EditShipmentModal
              showEditShipment={showEditShipment}
              setShowEditShipment={setShowEditShipment}
              setShipments={setShipments}
              shipments={shipments}
              selectedShipment={selectedShipmentForEdit}
            />
          )}
          {rowData && (
            <ShipmentDetailsModal
              selectedShipment={rowData}
              setSelectedShipment={setRowData}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default FactoryShipList;
