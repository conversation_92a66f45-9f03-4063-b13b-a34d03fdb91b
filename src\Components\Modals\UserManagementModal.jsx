

import React from 'react'
import users from '../../data/users'
import { Plus, Edit, Trash2 , User } from 'lucide-react'




const UserManagementModal = ({ setShowUserManagement}) => {


  



  return (
    <div className=" !m-0 fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl w-full max-w-6xl max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-xl font-bold text-gray-900">User Management</h2>
          <button
            onClick={() => setShowUserManagement(false)}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-lg font-medium text-gray-900">Users List</h3>
            <button className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center gap-2">
              <Plus className="w-4 h-4" />
              Add User
            </button>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className=" py-3 px-4 font-medium text-gray-700">
                    Name
                  </th>
                  <th className=" py-3 px-4 font-medium text-gray-700">
                    Role
                  </th>
                  <th className=" py-3 px-4 font-medium text-gray-700">
                    Branch
                  </th>
                  <th className=" py-3 px-4 font-medium text-gray-700">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {users.map((user) => (
                  <tr key={user.id} className="hover:bg-gray-50">
                    <td className="py-3 px-4">{user.name}</td>
                    <td className="py-3 px-4">
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          user.role === "driver"
                            ? "bg-blue-100 text-blue-800"
                            : user.role === "factory"
                            ? "bg-green-100 text-green-800"
                            : "bg-purple-100 text-purple-800"
                        }`}
                      >
                        {user.role === "driver"
                          ? "Driver"
                          : user.role === "factory"
                          ? "Factory"
                          : "Branch"}
                      </span>
                    </td>
                    <td className="py-3 px-4">{user.branch || "-"}</td>
                    <td className="py-3 px-4">
                      <button className="text-blue-600 hover:text-blue-700 mr-2">
                        <Edit className="w-4 h-4" />
                      </button>
                      <button className="text-red-600 hover:text-red-700">
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );

}

export default UserManagementModal
