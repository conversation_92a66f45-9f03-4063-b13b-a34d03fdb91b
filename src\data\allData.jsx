// ---------- STORES (canonical list used everywhere) ----------
const stores = [
  {
    id: 1,
    name: "Prime Outlet",
    nameEn: "Prime Outlet",
    address: "15 Al-Nasr Street, Maadi, Cairo",
    phone: "+20 2 2358-1234",
    email: "<EMAIL>",
    manager: "<PERSON><PERSON>",
    managerPhone: "+20 10 1234-5678",
    storeCode: "12345",
    status: "active",
    openingHours: "07:00 - 22:00",
    establishedDate: "2020-01-15",
    totalShipments: 156,
    monthlyShipments: 24,
    averageDeliveryTime: "2.5 hours",
    lastShipment: "2024-01-15T14:30:00",
    coordinates: { lat: 29.9602, lng: 31.2569 },
    performance: {
      onTimeDelivery: 95,
      qualityScore: 4.8,
      customerSatisfaction: 4.7,
    },
    created_at: "2020-01-01T09:00:00Z",
    updated_at: "2024-01-10T10:00:00Z",
  },
  {
    id: 2,
    name: "Signature Outlet",
    nameEn: "Signature Outlet",
    address: "45 Mustafa Al-Nahhas Street, Nasr City, Cairo",
    phone: "+20 2 2274-5678",
    email: "<EMAIL>",
    manager: "<PERSON>",
    managerPhone: "+20 10 9876-5432",
    storeCode: "12345",
    status: "active",
    openingHours: "06:30 - 23:00",
    establishedDate: "2019-06-20",
    totalShipments: 203,
    monthlyShipments: 31,
    averageDeliveryTime: "1.8 hours",
    lastShipment: "2024-01-15T16:45:00",
    coordinates: { lat: 30.0626, lng: 31.3219 },
    performance: {
      onTimeDelivery: 92,
      qualityScore: 4.6,
      customerSatisfaction: 4.5,
    },
    created_at: "2019-06-01T08:30:00Z",
    updated_at: "2024-01-12T12:00:00Z",
  },
  {
    id: 3,
    name: "Concept Store",
    nameEn: "Concept Store",
    address: "12 26th July Street, Zamalek, Cairo",
    phone: "+20 2 2735-9012",
    email: "<EMAIL>",
    manager: "Nora Hassan",
    managerPhone: "+20 10 5555-1234",
    storeCode: "12345",
    status: "maintenance",
    openingHours: "08:00 - 21:00",
    establishedDate: "2018-03-10",
    totalShipments: 89,
    monthlyShipments: 0,
    averageDeliveryTime: "3.2 hours",
    lastShipment: "2024-01-10T12:15:00",
    coordinates: { lat: 30.0616, lng: 31.2194 },
    performance: {
      onTimeDelivery: 88,
      qualityScore: 4.4,
      customerSatisfaction: 4.3,
    },
    created_at: "2018-03-01T10:00:00Z",
    updated_at: "2024-01-09T15:00:00Z",
  },
  {
    id: 4,
    name: "Flagship Store",
    nameEn: "Flagship Store",
    address: "78 Arab League Street, Mohandessin, Giza",
    phone: "+20 2 3344-7890",
    email: "<EMAIL>",
    manager: "Mohamed Abdullah",
    managerPhone: "+20 10 7777-8888",
    storeCode: "12345",
    status: "active",
    openingHours: "07:30 - 22:30",
    establishedDate: "2021-09-05",
    totalShipments: 67,
    monthlyShipments: 18,
    averageDeliveryTime: "2.1 hours",
    lastShipment: "2024-01-15T11:20:00",
    coordinates: { lat: 30.0581, lng: 31.2067 },
    performance: {
      onTimeDelivery: 97,
      qualityScore: 4.9,
      customerSatisfaction: 4.8,
    },
    created_at: "2021-09-01T11:00:00Z",
    updated_at: "2024-01-13T09:30:00Z",
  },
];

// ---------- FINAL ARRAY OF OBJECTS (REGIONS ROOT) ----------
const regions = [
  {
    id: 2001,
    name: "Riyadh Region",
    createdAt: new Date("2025-08-20T08:30:00"),
    routes: [
      {
        id: "RY-1",
        name: "Olaya ⇄ King Fahd Rd",
        // relational link to stores via IDs:
        storeIds: [1, 2],
        stores: [1, 2].map((id) => stores.find((store) => store.id === id)),
        createdAt: new Date("2025-08-20T09:00:00"),
      },
      {
        id: "RY-2",
        name: "Diriyah ⇄ Riyadh Front (Qurtubah)",
        storeIds: [3, 4],
        stores: [3, 4].map((id) => stores.find((store) => store.id === id)),
        createdAt: new Date("2025-08-21T09:00:00"),
      },
    ],
    factories: [
      {
        id: "FA-1",
        name: "Central Baking Plant",
        createdAt: new Date("2025-08-20T10:00:00"),
        qualityTesters: [
          {
            id: "QT-1",
            name: "Huda Salem",
            email: "<EMAIL>",
            // dev only: keep out of prod or hash
            password: "Huda@123",
            status: "active",
            created_at: new Date("2024-12-20T10:00:00"),
            updated_at: new Date("2025-08-21T13:40:00"),
          },
          {
            id: "QT-2",
            name: "Tariq Nabil",
            email: "<EMAIL>",
            password: "Tariq@123",
            status: "inactive",
            created_at: new Date("2024-11-01T08:15:00"),
            updated_at: new Date("2025-07-30T09:20:00"),
          },
        ],
        distributors: [
          {
            id: "D-1",
            name: "Ahmed Mohamed",
            role: "distributor",
            branch: null,
            email: "<EMAIL>",
            // dev only: keep out of prod or hash
            password: "Ahmed@123",
            created_at: new Date("2024-12-15T09:15:00"),
            updated_at: new Date("2025-08-21T13:45:00"),
            status: "active",
            shipments: [
              {
                id: 1,
                bakeryType: "Donuts",
                quantity: 100,
                driver: "Ahmed Mohamed",
                vehicleNumber: "CAI-1023",
                routeId: "RY-1",
                status: "In Transit",
                qualityStatus: "Pending",
                storeStatus: "Pending",
                qualityNotes: "",
                storesTickets: null,
                timestamps: {
                  created: new Date("2024-01-15T08:00:00"),
                  driverPickup: new Date("2024-01-15T09:00:00"),
                  delivery: null,
                  branchReceive: null,
                  display: null,
                  qualityCheckedAt: null,
                  storeStatusUpdatedAt: null,
                },
              },
              {
                id: 2,
                bakeryType: "Cakes",
                quantity: 50,
                driver: "Mahmoud Ali",
                vehicleNumber: "CAI-2044",
                routeId: "RY-2",
                status: "Delivered",
                qualityStatus: "Confirmed",
                storeStatus: "Confirmed",
                qualityNotes:
                  "All checks passed. Packaging intact and counts verified.",
               
                storesTickets: [
                  {
                    storeId: 2, // Signature Outlet
                    status: "Confirmed",
                    updatedAt: new Date("2024-01-15T10:35:00"),
                    notes: "Goods received; minor box dent.",
                    vehicleNumber: "CAI-2044",
                    images: [
                      "https://res.cloudinary.com/dbzn1y8rt/image/upload/v1759838107/bf5hero39mrmoaua16yi.jpg",
                      "https://res.cloudinary.com/dbzn1y8rt/image/upload/v1759838090/gf9hcztqqdxlr2ivmuhe.jpg",
                      "https://res.cloudinary.com/dbzn1y8rt/image/upload/v1759838042/izg2x0bnucykdy69putx.jpg",
                    ],
                  },
                  {
                    storeId: 1, // Prime Outlet
                    status: "Rejected",
                    updatedAt: new Date("2024-01-15T10:40:00"),
                    notes: "Some items damaged.",
                    vehicleNumber: "CAI-2044",
                  },
                ],
                timestamps: {
                  created: new Date("2024-01-15T07:00:00"),
                  driverPickup: new Date("2024-01-15T08:00:00"),
                  delivery: new Date("2024-01-15T10:30:00"),
                  branchReceive: new Date("2024-01-15T10:35:00"),
                  display: new Date("2024-01-15T10:50:00"),
                  qualityCheckedAt: new Date("2024-01-15T10:30:00"),
                  storeStatusUpdatedAt: new Date("2024-01-15T10:35:00"),
                },
              },
              {
                id: 3,
                bakeryType: "Croissants",
                quantity: 200,
                driver: "Sara Ibrahim",
                vehicleNumber: "CAI-3310",
                routeId: "RY-1",
                status: "Pending Pickup",
                qualityStatus: "Rejected",
                storeStatus: "Pending",
                qualityNotes: "Conditions are not good",
               
                storesTickets: null,
                timestamps: {
                  created: new Date("2024-01-16T06:00:00"),
                  driverPickup: null,
                  delivery: null,
                  branchReceive: null,
                  display: null,
                  qualityCheckedAt: new Date("2024-01-16T09:00:00"),
                  storeStatusUpdatedAt: null,
                },
              },
              {
                id: 4,
                bakeryType: "Pita Bread",
                quantity: 300,
                driver: "Omar Khaled",
                vehicleNumber: "CAI-8891",
                routeId: "RY-2",
                status: "Received",
                qualityStatus: "Confirmed",
                storeStatus: "Confirmed",
                qualityNotes: "Minor delay; condition acceptable.",
               
                storesTickets: [
                  {
                    storeId: 2,
                    status: "Confirmed",
                    updatedAt: new Date("2024-01-16T11:00:00"),
                    notes: "Received with slight delay.",
                    vehicleNumber: "CAI-8891",
                  },
                  {
                    storeId: 4,
                    status: "Confirmed",
                    updatedAt: new Date("2024-01-16T11:10:00"),
                    notes: "Stored immediately",
                    vehicleNumber: "CAI-8891",
                  },
                ],
                timestamps: {
                  created: new Date("2024-01-16T07:30:00"),
                  driverPickup: new Date("2024-01-16T08:00:00"),
                  delivery: new Date("2024-01-16T10:45:00"),
                  branchReceive: new Date("2024-01-16T11:00:00"),
                  display: null,
                  qualityCheckedAt: new Date("2024-01-16T10:45:00"),
                  storeStatusUpdatedAt: new Date("2024-01-16T11:00:00"),
                },
              },
              {
                id: 5,
                bakeryType: "Donuts",
                quantity: 120,
                driver: "Youssef Hassan",
                vehicleNumber: "GIZ-4120",
                routeId: "RY-1",
                status: "In Transit",
                qualityStatus: "Pending",
                storeStatus: "Pending",
                qualityNotes: "",
                
                storesTickets: null,
                timestamps: {
                  created: new Date("2024-01-17T06:45:00"),
                  driverPickup: new Date("2024-01-17T07:15:00"),
                  delivery: null,
                  branchReceive: null,
                  display: null,
                  qualityCheckedAt: null,
                  storeStatusUpdatedAt: null,
                },
              },
              {
                id: 6,
                bakeryType: "Baguettes",
                quantity: 400,
                driver: "Fatma Adel",
                vehicleNumber: "ALX-7733",
                routeId: "RY-2",
                status: "Received",
                qualityStatus: "Confirmed",
                storeStatus: "Confirmed",
                qualityNotes: "Temperature maintained within acceptable range.",
                
                storesTickets: [
                  {
                    storeId: 2,
                    status: "Confirmed",
                    updatedAt: new Date("2024-01-17T12:10:00"),
                    notes: "Stored properly.",
                    vehicleNumber: "ALX-7733",
                  },
                  {
                    storeId: 3,
                    status: "Confirmed",
                    updatedAt: new Date("2024-01-17T12:25:00"),
                    notes: "Display prepared",
                    vehicleNumber: "ALX-7733",
                  },
                ],
                timestamps: {
                  created: new Date("2024-01-17T09:00:00"),
                  driverPickup: new Date("2024-01-17T09:30:00"),
                  delivery: new Date("2024-01-17T11:55:00"),
                  branchReceive: new Date("2024-01-17T12:10:00"),
                  display: new Date("2024-01-17T12:25:00"),
                  qualityCheckedAt: new Date("2024-01-17T11:55:00"),
                  storeStatusUpdatedAt: new Date("2024-01-17T12:10:00"),
                },
              },
              {
                id: 7,
                bakeryType: "Muffins",
                quantity: 75,
                driver: "Karim Nasser",
                vehicleNumber: "CAI-5582",
                routeId: "RY-1",
                status: "Pending Pickup",
                qualityStatus: "Pending",
                storeStatus: "Pending",
                qualityNotes: "",
                
                storesTickets: null,
                timestamps: {
                  created: new Date("2024-01-18T05:30:00"),
                  driverPickup: null,
                  delivery: null,
                  branchReceive: null,
                  display: null,
                  qualityCheckedAt: null,
                  storeStatusUpdatedAt: null,
                },
              },
              {
                id: 8,
                bakeryType: "Bagels",
                quantity: 180,
                driver: "Mona Hassan",
                vehicleNumber: "CAI-9921",
                routeId: "RY-2",
                status: "In Transit",
                qualityStatus: "Pending",
                storeStatus: "Pending",
                qualityNotes: "",
               
                storesTickets: null,
                timestamps: {
                  created: new Date("2024-01-18T07:00:00"),
                  driverPickup: new Date("2024-01-18T07:30:00"),
                  delivery: null,
                  branchReceive: null,
                  display: null,
                  qualityCheckedAt: null,
                  storeStatusUpdatedAt: null,
                },
              },
              {
                id: 9,
                bakeryType: "Focaccia",
                quantity: 60,
                driver: "Ali Said",
                vehicleNumber: "GIZ-6617",
                routeId: "RY-1",
                status: "Delivered",
                qualityStatus: "Confirmed",
                storeStatus: "Confirmed",
                qualityNotes: "Excellent freshness noted upon receipt.",
                
                storesTickets: [
                  {
                    storeId: 2,
                    status: "Confirmed",
                    updatedAt: new Date("2024-01-18T14:00:00"),
                    notes: "Excellent freshness.",
                    vehicleNumber: "GIZ-6617",
                  },
                  {
                    storeId: 4,
                    status: "Confirmed",
                    updatedAt: new Date("2024-01-18T14:15:00"),
                    notes: "Displayed on arrival",
                    vehicleNumber: "GIZ-6617",
                  },
                ],
                timestamps: {
                  created: new Date("2024-01-18T09:00:00"),
                  driverPickup: new Date("2024-01-18T09:20:00"),
                  delivery: new Date("2024-01-18T13:50:00"),
                  branchReceive: new Date("2024-01-18T14:00:00"),
                  display: null,
                  qualityCheckedAt: new Date("2024-01-18T13:50:00"),
                  storeStatusUpdatedAt: new Date("2024-01-18T14:00:00"),
                },
              },
              {
                id: 10,
                bakeryType: "Cookies",
                quantity: 90,
                driver: "Layla Mostafa",
                vehicleNumber: "CAI-1470",
                routeId: "RY-2",
                status: "In Transit",
                qualityStatus: "Pending",
                storeStatus: "Pending",
                qualityNotes: "",
                
                storesTickets: null,
                timestamps: {
                  created: new Date("2024-01-19T06:30:00"),
                  driverPickup: new Date("2024-01-19T07:00:00"),
                  delivery: null,
                  branchReceive: null,
                  display: null,
                  qualityCheckedAt: null,
                  storeStatusUpdatedAt: null,
                },
              },
            ],
          },
        ],
      },
    ],
  },

  {
    id: 2002,
    name: "Dammam Region",
    createdAt: new Date("2025-09-02T09:00:00"),
    routes: [
      {
        id: "DM-1",
        name: "Dammam Corniche ⇄ Al-Faisaliyah",
        storeIds: [2, 1], // reuse known stores
        stores: [2, 1].map((id) => stores.find((store) => store.id === id)),
        createdAt: new Date("2025-09-02T09:30:00"),
      },
      {
        id: "DM-2",
        name: "Khobar Corniche ⇄ Dhahran Mall/Tech Valley",
        storeIds: [4, 3],
        stores: [4,3].map((id) =>
          stores.find((store) => store.id === id)
        ),
        createdAt: new Date("2025-09-03T09:30:00"),
      },
    ],
    factories: [
      {
        id: "FA-2",
        name: "Eastern Dough Works",
        createdAt: new Date("2025-09-03T11:30:00"),
        qualityTesters: [], // ready for future quality testers
        distributors: [], // ready for future distributors
      },
    ],
  },

  {
    id: 2003,
    name: "Southern Region",
    createdAt: new Date("2025-09-15T07:45:00"),
    routes: [
      {
        id: "SO-1",
        name: "Abha City ⇄ Khamis Mushait (Industrial)",
        storeIds: [1, 3],
        stores: [1,3].map((id) =>
          stores.find((store) => store.id === id)
        ),
        createdAt: new Date("2025-09-15T08:00:00"),
      },
    ],
    factories: [
      {
        id: "FA-3",
        name: "Southern Oven Hub",
        createdAt: new Date("2025-09-15T12:15:00"),
        qualityTesters: [],
        distributors: [],
      },
    ],
  },

  {
    id: 2004,
    name: "Northern Region",
    createdAt: new Date("2025-09-18T07:45:00"),
    routes: [
      {
        id: "NO-1",
        name: "Tabuk City Center ⇄ Tabuk Airport",
        storeIds: [4, 2],
        stores: [4,2].map((id) =>
          stores.find((store) => store.id === id)
        ),
        createdAt: new Date("2025-09-18T08:00:00"),
      },
    ],
    factories: [],
  },
];

// ---------- EXPORT A SINGLE BUNDLE IF YOU LIKE ----------
const data = { stores, regions };
export default data;

/**
 * Get ALL shipments from your nested data.
 * @param {Array} regions - root regions array (with factories → distributors → shipments)
 * @param {Array} [stores] - optional canonical stores array to resolve store tickets
 * @returns {Array} flat array of shipments with context
 */

// --------------------------------------
// Helpers
// --------------------------------------
const byId = (arr = [], id) => arr.find((x) => x?.id === id) || null;

/**
 * يبني فهرس سريع للمتاجر بالمعرّف
 */
const buildStoreIndex = (stores = []) => new Map(stores.map((s) => [s.id, s]));

/**
 * يُرجع جميع الشحنات مُسطّحة ومُثرّاة بالسياق (الإقليم/المصنع/الموزّع/المسار/تذاكر المتجر)
 * @param {Array} regions
 * @param {Array} [stores=[]] - قائمة المتاجر لاستخدامها في ربط storesTickets ببيانات المتجر
 * @returns {Array} shipments[]
 */
export function getAllShipments(regions = [], stores = []) {
  const storeIdx = buildStoreIndex(stores);
  const out = [];

  for (const region of regions || []) {
    const routes = region?.routes || [];
    const factories = region?.factories || [];

    for (const factory of factories) {
      const distributors = factory?.distributors || [];

      for (const distributor of distributors) {
        const shipments = distributor?.shipments || [];

        for (const sh of shipments) {
          // اربط المسار من داخل الإقليم عبر routeId
          const route = sh?.routeId ? byId(routes, sh.routeId) : null;

          // اربط تذاكر المتجر ببيانات المتجر الكاملة (إن وُجدت)
          const storesTickets =
            (sh?.storesTickets || []).map((t) => ({
              ...t,
              store: storeIdx.get(t.storeId) || null,
            })) || null;

          out.push({
            ...sh,
            // سياق إضافي مفيد
            region: region
              ? {
                  id: region.id,
                  name: region.name,
                  createdAt: region.createdAt,
                }
              : null,
            factory: factory
              ? {
                  id: factory.id,
                  name: factory.name,
                  createdAt: factory.createdAt,
                }
              : null,
            distributor: distributor
              ? {
                  id: distributor.id,
                  name: distributor.name,
                  email: distributor.email,
                  branch: distributor.branch,
                  status: distributor.status,
                }
              : null,
            route, // الكائن الكامل للمسار داخل الإقليم (إن وُجد)
            storesTickets,
          });
        }
      }
    }
  }

  return out;
}

/**
 * يُرجع مسارات الإقليم
 * @param {Array} regions
 * @param {number} regionId
 * @returns {Array} routes[]
 */
export function getRoutesByRegion(regions = [], regionId) {
  return (regions.find((r) => r.id === regionId)?.routes || []).slice();
}

/**
 * يُرجع الشحنات حسب الإقليم
 * @param {Array} regions
 * @param {number} regionId
 * @param {Array} [stores=[]]
 * @returns {Array} shipments[]
 */
export function getShipmentsByRegion(regions = [], regionId, stores = []) {
  const all = getAllShipments(regions, stores);
  return all.filter((s) => s.region?.id === regionId);
}

/**
 * يُرجع الشحنات حسب المصنع
 * @param {Array} regions
 * @param {string} factoryId
 * @param {Array} [stores=[]]
 * @returns {Array} shipments[]
 */
export function getShipmentsByFactory(regions = [], factoryId, stores = []) {
  const all = getAllShipments(regions, stores);
  return all.filter((s) => s.factory?.id === factoryId);
}

export function getRoutesByRegions(regions = [], regionIds, opts = {}) {
  const { withContext = true, dedupe = true, sort = true } = opts;

  const wantAll = regionIds == null;
  const wanted = wantAll
    ? null
    : new Set(Array.isArray(regionIds) ? regionIds : [regionIds]);

  const seen = new Set();
  const out = [];

  for (const region of regions || []) {
    if (!wantAll && !wanted.has(region.id)) continue;

    for (const route of region?.routes || []) {
      if (dedupe) {
        if (seen.has(route.id)) continue;
        seen.add(route.id);
      }

      out.push(
        withContext
          ? {
              ...route,
              region: {
                id: region.id,
                name: region.name,
                createdAt: region.createdAt,
              },
            }
          : { ...route }
      );
    }
  }

  if (sort) {
    out.sort((a, b) => {
      const ra = a.region?.id ?? 0;
      const rb = b.region?.id ?? 0;
      if (ra !== rb) return ra - rb;

      const ta = +new Date(a.createdAt || 0);
      const tb = +new Date(b.createdAt || 0);
      return ta - tb;
    });
  }

  return out;
}

/**
 * يُرجع الشحنات حسب الموزّع
 * - يمكن التصفية بالمعرّف أو البريد أو الاسم
 * @param {Array} regions
 * @param {Object} selector - { id?: string, email?: string, name?: string }
 * @param {Array} [stores=[]]
 * @returns {Array} shipments[]
 */
export function getShipmentsByDistributor(
  regions = [],
  selector = {},
  stores = []
) {
  const { id, email, name } = selector || {};
  const all = getAllShipments(regions, stores);

  return all.filter((s) => {
    const d = s.distributor || {};
    if (id && d.id === id) return true;
    if (email && d.email === email) return true;
    if (name && d.name?.toLowerCase() === String(name).toLowerCase())
      return true;
    return false;
  });
}

export function filterShipments(shipments = [], criteria = {}) {
  const normArr = (v) => (Array.isArray(v) ? v : v != null ? [v] : null);

  const {
    status,
    qualityStatus,
    storeStatus,
    routeId,
    bakeryType,
    driver,
    storeId,
    regionId,
    factoryId,
    distributorId,
    createdFrom,
    createdTo,
  } = criteria;

  const statusSet = normArr(status)?.map(String.toLowerCase);
  const qStatusSet = normArr(qualityStatus)?.map(String.toLowerCase);
  const sStatusSet = normArr(storeStatus)?.map(String.toLowerCase);
  const routeSet = normArr(routeId);
  const bakerySet = normArr(bakeryType)?.map(String.toLowerCase);
  const driverSet = normArr(driver)?.map(String.toLowerCase);
  const storeSet = normArr(storeId);
  const regionSet = normArr(regionId);
  const factorySet = normArr(factoryId);
  const distributorSet = normArr(distributorId);

  const fromTs = createdFrom ? new Date(createdFrom).getTime() : null;
  const toTs = createdTo ? new Date(createdTo).getTime() : null;

  return shipments.filter((s) => {
    if (statusSet && !statusSet.includes(String(s.status).toLowerCase()))
      return false;
    if (
      qStatusSet &&
      !qStatusSet.includes(String(s.qualityStatus).toLowerCase())
    )
      return false;
    if (sStatusSet && !sStatusSet.includes(String(s.storeStatus).toLowerCase()))
      return false;
    if (routeSet && !routeSet.includes(s.route?.id || s.routeId)) return false;
    if (bakerySet && !bakerySet.includes(String(s.bakeryType).toLowerCase()))
      return false;
    if (driverSet && !driverSet.includes(String(s.driver).toLowerCase()))
      return false;

    if (regionSet && !regionSet.includes(s.region?.id)) return false;
    if (factorySet && !factorySet.includes(s.factory?.id)) return false;
    if (distributorSet && !distributorSet.includes(s.distributor?.id))
      return false;

    if (storeSet) {
      const tickets = s.storesTickets || [];
      const hasStore = tickets.some((t) => storeSet.includes(t.storeId));
      if (!hasStore) return false;
    }

    if (fromTs || toTs) {
      const created = s?.timestamps?.created
        ? new Date(s.timestamps.created).getTime()
        : null;
      if (fromTs && (created == null || created < fromTs)) return false;
      if (toTs && (created == null || created > toTs)) return false;
    }

    return true;
  });
}

/**
 * Get factories by a region
 * @param {Array} regions
 * @param {number} regionId
 * @returns {Array} factories[]
 */
export function getFactoriesByRegion(regions = [], regionId, opts = {}) {
  const region = regions.find((r) => r.id === regionId);
  if (!region) return [];
  const factories = region?.factories || [];
  const { withContext = true } = opts;
  if (!withContext) return factories.slice();
  return factories.map((f) => ({
    ...f,
    region: {
      id: region.id,
      name: region.name,
      createdAt: region.createdAt,
    },
  }));
}

/**
 * Get distributors by a factory
 * Searches all regions for the factoryId (no need to pass regionId).
 * @param {Array} regions
 * @param {string} factoryId
 * @param {Object} [opts]
 * @param {boolean} [opts.withContext=true] - include region/factory context in each distributor
 * @returns {Array} distributors[]
 */
export function getDistributorsByFactory(regions = [], factoryId, opts = {}) {
  const { withContext = true } = opts;

  for (const region of regions || []) {
    for (const factory of region?.factories || []) {
      if (factory.id === factoryId) {
        const list = factory.distributors || [];
        if (!withContext) return list.slice();
        return list.map((d) => ({
          ...d,
          region: {
            id: region.id,
            name: region.name,
            createdAt: region.createdAt,
          },
          factory: {
            id: factory.id,
            name: factory.name,
            createdAt: factory.createdAt,
          },
        }));
      }
    }
  }
  return [];
}

/**
 * Get distributors by a region
 * Flattens all distributors from all factories in the region.
 * @param {Array} regions
 * @param {number} regionId
 * @param {Object} [opts]
 * @param {boolean} [opts.withContext=true] - include region/factory context in each distributor
 * @returns {Array} distributors[]
 */
export function getDistributorsByRegion(regions = [], regionId, opts = {}) {
  const { withContext = true } = opts;
  const region = regions.find((r) => r.id === regionId);
  if (!region) return [];

  const out = [];
  for (const factory of region?.factories || []) {
    const dist = factory?.distributors || [];
    if (withContext) {
      out.push(
        ...dist.map((d) => ({
          ...d,
          region: {
            id: region.id,
            name: region.name,
            createdAt: region.createdAt,
          },
          factory: {
            id: factory.id,
            name: factory.name,
            createdAt: factory.createdAt,
          },
        }))
      );
    } else {
      out.push(...dist);
    }
  }
  return out;
}

// --------------------------------------
// أمثلة سريعة للاستخدام
// --------------------------------------
// const all = getAllShipments(regions, stores);
// const byRegion = getShipmentsByRegion(regions, 2001, stores);
// const routesRiyadh = getRoutesByRegion(regions, 2001);
// const byFactory = getShipmentsByFactory(regions, "FA-1", stores);
// const byDistributorId = getShipmentsByDistributor(regions, { id: "D-1" }, stores);
// const filtered = filterShipments(all, { status: ["Delivered", "Received"], regionId: 2001, storeId: [2,4] });

// Factories in Riyadh:
const factoriesRiyadh = getFactoriesByRegion(regions, 2001);

// Distributors for factory FA-1 (with context by default):
const dFA1 = getDistributorsByFactory(regions, "FA-1");

// Distributors in Dammam region (raw objects only):
const dDammamRaw = getDistributorsByRegion(regions, 2002, {
  withContext: false,
});

/**
 * Get factory details by id (searches across all regions)
 * @param {Array} regions
 * @param {string} factoryId
 * @param {Object} [opts]
 * @param {boolean} [opts.withContext=true]  - include region meta alongside the factory
 * @param {boolean} [opts.clone=true]        - return a shallow-cloned object (avoid mutating source)
 * @returns {Object|null} factory or null if not found
 */
export function getFactoryById(regions = [], factoryId, opts = {}) {
  const { withContext = true, clone = true } = opts;

  for (const region of regions || []) {
    for (const factory of region?.factories || []) {
      if (factory?.id === factoryId) {
        const base = clone ? { ...factory } : factory;
        if (!withContext) return base;

        return {
          ...base,
          region: {
            id: region.id,
            name: region.name,
            createdAt: region.createdAt,
          },
        };
      }
    }
  }
  return null;
}

/**
 * Get ALL factories data across all regions.
 * @param {Array} regions
 * @param {Object} [opts]
 * @param {boolean} [opts.withContext=true]       - include region meta per factory
 * @param {boolean} [opts.withDistributors=false] - include full distributors array
 * @param {boolean} [opts.withStats=true]         - include quick stats (shipments, routes, etc.)
 * @param {boolean} [opts.clone=true]             - shallow-clone returned objects
 * @returns {Array} factories[]
 */
export function getAllFactories(regions = [], opts = {}) {
  const {
    withContext = true,
    withDistributors = false,
    withStats = true,
    clone = true,
  } = opts;

  const out = [];

  for (const region of regions || []) {
    for (const factory of region?.factories || []) {
      const distributors = factory?.distributors || [];

      // Base factory object (clone-safe)
      const base = clone ? { ...factory } : factory;
      if (!withDistributors) delete base.distributors;

      const item = {
        ...base,
        ...(withContext
          ? {
              region: {
                id: region.id,
                name: region.name,
                createdAt: region.createdAt,
              },
            }
          : {}),
      };

      if (withDistributors) {
        item.distributors = clone
          ? distributors.map((d) => ({ ...d }))
          : distributors;
      }

      if (withStats) {
        // Aggregate stats from distributors -> shipments
        let shipmentsCount = 0;
        const shipmentsByStatus = {};
        const routesUsed = new Set();
        const drivers = new Set();
        const storesCovered = new Set();

        let firstShipmentAt = null;
        let lastShipmentAt = null;
        let lastDistributorUpdatedAt = null;

        for (const d of distributors) {
          // distributor update time
          if (d?.updated_at) {
            const upTs = new Date(d.updated_at).getTime();
            if (!Number.isNaN(upTs)) {
              lastDistributorUpdatedAt = Math.max(
                lastDistributorUpdatedAt ?? upTs,
                upTs
              );
            }
          }

          const shipArr = d?.shipments || [];
          shipmentsCount += shipArr.length;

          for (const sh of shipArr) {
            // status counts
            const st = String(sh?.status || "Unknown");
            shipmentsByStatus[st] = (shipmentsByStatus[st] || 0) + 1;

            // routes + drivers
            if (sh?.routeId) routesUsed.add(sh.routeId);
            if (sh?.driver) drivers.add(String(sh.driver));

            // stores coverage
            for (const t of sh?.storesTickets || []) {
              if (t?.storeId != null) storesCovered.add(t.storeId);
            }

            // timestamps windows
            const createdTs = sh?.timestamps?.created
              ? new Date(sh.timestamps.created).getTime()
              : null;

            // prefer the most concrete delivery/receive/display times for "last"
            const candidateTimes = [
              sh?.timestamps?.display,
              sh?.timestamps?.branchReceive,
              sh?.timestamps?.delivery,
              sh?.timestamps?.driverPickup,
              sh?.timestamps?.created,
            ]
              .filter(Boolean)
              .map((d) => new Date(d).getTime())
              .filter((n) => !Number.isNaN(n));

            const maxSh = candidateTimes.length
              ? Math.max(...candidateTimes)
              : null;

            if (createdTs != null) {
              firstShipmentAt =
                firstShipmentAt == null
                  ? createdTs
                  : Math.min(firstShipmentAt, createdTs);
            }
            if (maxSh != null) {
              lastShipmentAt =
                lastShipmentAt == null
                  ? maxSh
                  : Math.max(lastShipmentAt, maxSh);
            }
          }
        }

        item.stats = {
          distributorsCount: distributors.length,
          shipmentsCount,
          shipmentsByStatus,
          routesUsed: Array.from(routesUsed),
          drivers: Array.from(drivers),
          storesCovered: Array.from(storesCovered),
          firstShipmentAt: firstShipmentAt ? new Date(firstShipmentAt) : null,
          lastShipmentAt: lastShipmentAt ? new Date(lastShipmentAt) : null,
          lastDistributorUpdatedAt: lastDistributorUpdatedAt
            ? new Date(lastDistributorUpdatedAt)
            : null,
        };
      }

      out.push(item);
    }
  }

  return out;
}
/**
 * Get quality testers by a factory (searches across all regions).
 * @param {Array} regions
 * @param {string} factoryId
 * @param {Object} [opts]
 * @param {boolean} [opts.withContext=true]   - include region/factory meta per tester
 * @param {boolean} [opts.clone=true]         - shallow-clone tester objects
 * @param {boolean} [opts.activeOnly=false]   - return only testers with status === "active"
 * @param {boolean} [opts.redactPasswords=true] - remove `password` field from results
 * @param {string}  [opts.search]             - case-insensitive search in id/name/email
 * @returns {Array} qualityTesters[]
 *
 * يُرجع مختبري الجودة لمصنع معيّن مع خيارات للسياق/النسخ/الفلترة/إخفاء كلمات المرور.
 */
export function getQualityTestersByFactory(regions = [], factoryId, opts = {}) {
  const {
    withContext = true,
    clone = true,
    activeOnly = false,
    redactPasswords = true,
    search,
  } = opts;

  // Locate the factory across regions
  for (const region of regions || []) {
    for (const factory of region?.factories || []) {
      if (factory?.id === factoryId) {
        let list = factory?.qualityTesters || [];

        if (activeOnly) {
          list = list.filter((t) => String(t?.status).toLowerCase() === "active");
        }

        if (search && String(search).trim()) {
          const q = String(search).toLowerCase().trim();
          list = list.filter((t) => {
            const id = String(t?.id || "").toLowerCase();
            const name = String(t?.name || "").toLowerCase();
            const email = String(t?.email || "").toLowerCase();
            return id.includes(q) || name.includes(q) || email.includes(q);
          });
        }

        // Map with context / cloning / redaction
        return list.map((t) => {
          const base = clone ? { ...t } : t;
          if (redactPasswords && base?.password != null) delete base.password;

          if (!withContext) return base;

          return {
            ...base,
            region: {
              id: region.id,
              name: region.name,
              createdAt: region.createdAt,
            },
            factory: {
              id: factory.id,
              name: factory.name,
              createdAt: factory.createdAt,
            },
          };
        });
      }
    }
  }
  return [];
}

/**
 * Get a single quality tester by testerId (searches everywhere).
 * Same options as getQualityTestersByFactory.
 */
export function getQualityTesterById(regions = [], testerId, opts = {}) {
  const {
    withContext = true,
    clone = true,
    redactPasswords = true,
  } = opts;

  for (const region of regions || []) {
    for (const factory of region?.factories || []) {
      for (const t of factory?.qualityTesters || []) {
        if (t?.id === testerId) {
          const base = clone ? { ...t } : t;
          if (redactPasswords && base?.password != null) delete base.password;

          if (!withContext) return base;

          return {
            ...base,
            region: {
              id: region.id,
              name: region.name,
              createdAt: region.createdAt,
            },
            factory: {
              id: factory.id,
              name: factory.name,
              createdAt: factory.createdAt,
            },
          };
        }
      }
    }
  }
  return null;
}

/**
 * Get all quality testers in a region (flattens across its factories).
 * @param {Array} regions
 * @param {number} regionId
 * @param {Object} [opts] - same flags as above + {activeOnly, search}
 * @returns {Array} qualityTesters[]
 */
export function getQualityTestersByRegion(regions = [], regionId, opts = {}) {
  const {
    withContext = true,
    clone = true,
    activeOnly = false,
    redactPasswords = true,
    search,
  } = opts;

  const region = regions.find((r) => r.id === regionId);
  if (!region) return [];

  let out = [];
  for (const factory of region?.factories || []) {
    let list = factory?.qualityTesters || [];

    if (activeOnly) {
      list = list.filter((t) => String(t?.status).toLowerCase() === "active");
    }

    if (search && String(search).trim()) {
      const q = String(search).toLowerCase().trim();
      list = list.filter((t) => {
        const id = String(t?.id || "").toLowerCase();
        const name = String(t?.name || "").toLowerCase();
        const email = String(t?.email || "").toLowerCase();
        return id.includes(q) || name.includes(q) || email.includes(q);
      });
    }

    out.push(
      ...list.map((t) => {
        const base = clone ? { ...t } : t;
        if (redactPasswords && base?.password != null) delete base.password;

        if (!withContext) return base;

        return {
          ...base,
          region: {
            id: region.id,
            name: region.name,
            createdAt: region.createdAt,
          },
          factory: {
            id: factory.id,
            name: factory.name,
            createdAt: factory.createdAt,
          },
        };
      })
    );
  }
  return out;
}
// All testers for FA-1 (with context, hide passwords):
const qFA1 = getQualityTestersByFactory(regions, "FA-1");

// Only active testers, search “huda”:
const qFA1ActiveHuda = getQualityTestersByFactory(regions, "FA-1", {
  activeOnly: true,
  search: "huda",
});

// Single tester anywhere:
const tester = getQualityTesterById(regions, "QT-2");

// All testers in Riyadh region:
const qRiyadh = getQualityTestersByRegion(regions, 2001, { activeOnly: true });