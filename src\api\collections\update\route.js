import { NextResponse } from 'next/server';

export async function PUT(request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    const body = await request.json();
    const { user_id, name, generation_id } = body;

    // Validate collection ID
    if (!id || isNaN(Number(id))) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Valid collection ID is required' 
        },
        { status: 400 }
      );
    }

    // Validate required fields
    if (!user_id || !name || !generation_id) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Missing required fields: user_id, name, and generation_id are required' 
        },
        { status: 400 }
      );
    }

    // Validate data types
    if (typeof user_id !== 'number' || typeof generation_id !== 'number') {
      return NextResponse.json(
        { 
          success: false, 
          message: 'user_id and generation_id must be numbers' 
        },
        { status: 400 }
      );
    }

    if (typeof name !== 'string' || name.trim().length === 0) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'name must be a non-empty string' 
        },
        { status: 400 }
      );
    }

    // Here you would typically:
    // 1. Check if the collection exists
    // 2. Validate user_id exists in your database
    // 3. Validate generation_id exists in your database
    // 4. Update the collection in your database
    // 5. Return the updated collection

    // For now, we'll simulate a successful update
    const updatedCollection = {
      id: Number(id),
      user_id,
      name: name.trim(),
      generation_id,
      created_at: "2024-01-15T10:00:00Z", // This would come from the existing record
      updated_at: new Date().toISOString()
    };

    // TODO: Replace this with actual database update
    // Example with a hypothetical database:
    // const existingCollection = await db.collections.findById(id);
    // if (!existingCollection) {
    //   return NextResponse.json(
    //     { success: false, message: 'Collection not found' },
    //     { status: 404 }
    //   );
    // }
    // 
    // const result = await db.collections.update(id, {
    //   user_id,
    //   name: name.trim(),
    //   generation_id,
    //   updated_at: new Date()
    // });

    return NextResponse.json(
      {
        success: true,
        message: 'Collection updated successfully',
        data: updatedCollection
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error updating collection:', error);
    
    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
      },
      { status: 500 }
    );
  }
}

// Handle other HTTP methods
export async function GET() {
  return NextResponse.json(
    { message: 'Method not allowed. Use PUT to update a collection.' },
    { status: 405 }
  );
}

export async function POST() {
  return NextResponse.json(
    { message: 'Method not allowed. Use PUT to update a collection.' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { message: 'Method not allowed. Use PUT to update a collection.' },
    { status: 405 }
  );
}
