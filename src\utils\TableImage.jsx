import React, {useEffect, useState} from "react";
import cx from "classnames";
import { Image } from "antd";
const TableImage = ({src, onClick = () => null, className, ...rest}) => {
  const [showError, setShowError] = useState(false);
  const [prevOpen, setPreviewOpen] = useState(false);

  const onImageClick = (e) => {
    onClick(e);
    setPreviewOpen(true);
  };

  return (
    <>
      <img
        whileHover={{scale: 1.1}}
        onClick={(e) => (!showError ? onImageClick(e) : null)}
        loading='lazy'
        onError={(e) => {
          e.target.src =
            "https://res.cloudinary.com/dbzn1y8rt/image/upload/v1735643913/uspiaz37moatrwckryqp.png";
          setShowError(true);
        }}
        src={src}
        className={cx(
          "rounded  mx-auto w-[100px] cursor-pointer height-[100px] object-contain",
          className
        )}
        alt=''
        {...rest}
      />

      {src && (
        <Image
          wrapperStyle={{
            display: "none",
          }}
          preview={{
            visible: prevOpen,
            onVisibleChange: (visible) => setPreviewOpen(visible),
            afterOpenChange: (visible) => !visible && setPreviewOpen(false),
          }}
          src={src}
        />
      )}
    </>
  );
};

export default TableImage;
