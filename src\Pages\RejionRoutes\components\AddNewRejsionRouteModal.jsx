import { useState, useMemo, useEffect } from "react";
import stores from "../../../data/stores";
import users from "../../../data/users";
import regions from "../../../data/regions";
import AddNewStoreModal from '../../Stores/components/AddNewStoreModal';

const AddNewRejionRouteModal = ({ onClose, onCreate , withRegoinInput }) => {
  const drivers = useMemo(() => users.filter((u) => u.role === "driver"), []);

  const [name, setName] = useState("");
  const [driverId, setDriverId] = useState("");
  const [selectedBranches, setSelectedBranches] = useState([]);
  const [allBranches, setAllBranches] = useState(stores);
  const [storeToAdd, setStoreToAdd] = useState("");
  const [dragIndex, setDragIndex] = useState(null);
  const [showAddStore, setShowAddStore] = useState(false);
  const [newStoreName, setNewStoreName] = useState("");
  const [regionId, setRegionId] = useState("");

  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === "Escape") {
        onClose();
      }
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [onClose]);

  const toggleBranch = (branch) => {
    setSelectedBranches((prev) =>
      prev.includes(branch)
        ? prev.filter((b) => b !== branch)
        : [...prev, branch]
    );
  };

  const addStore = () => {
    if (!storeToAdd) return;
    setSelectedBranches((prev) =>
      prev.includes(storeToAdd) ? prev : [...prev, storeToAdd]
    );
    setStoreToAdd("");
  };

  const removeStore = (branch) => {
    setSelectedBranches((prev) => prev.filter((b) => b !== branch));
  };

  const handleSubmit = () => {
    if (!name.trim()) return;
    if (withRegoinInput && !regionId) return;
    const payload = {
      id: Date.now(),
      name: name.trim(),
      driverId: driverId || null,
      points: selectedBranches,
      regionName: withRegoinInput ? regionId : null,
      createdAt: new Date(),
    };
    console.log("submit")
    console.log("object")
    onCreate(payload);
    onClose();
  };

  const handleCreateStore = () => {
    const trimmed = newStoreName.trim();
    if (!trimmed) return;
    const exists = allBranches.some(
      (b) => b.toLowerCase() === trimmed.toLowerCase()
    );
    if (exists) {
      // If it already exists, optionally auto-select it and close
      setSelectedBranches((prev) =>
        prev.includes(trimmed) ? prev : [...prev, trimmed]
      );
      setShowAddStore(false);
      setNewStoreName("");
      return;
    }
    const updated = [...allBranches, trimmed];
    setAllBranches(updated);
    setSelectedBranches((prev) => [...prev, trimmed]);
    setShowAddStore(false);
    setNewStoreName("");
  };

  return (
    <div
      onClick={() => onClose()}
      className=" !m-0 fixed  !p-10 inset-0 top-0 bottom-0 left-0 right-0 bg-black bg-opacity-50 flex items-center justify-center z-50 "
    >
      <div
        onClick={(e) => {
          e.stopPropagation();
        }}
        className="bg-white rounded-xl shadow-xl w-full max-w-lg max-h-[95vh] overflow-y-auto custom-scrollbar"
      >
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900">
            Create New Route 
          </h2>
          <p className="text-sm text-gray-600 mt-1">
            Add a new route by entering its name.
          </p>
        </div>

        <div className="p-6 space-y-5">
        {withRegoinInput && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Region
              </label>
              <select
                value={regionId}
                onChange={(e) => setRegionId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white"
              >
                <option value="">Choose a region</option>
                {regions.map((r) => (
                  <option key={r.id} value={r.name}>
                    {r.name}
                  </option>
                ))}
              </select>
            </div>
          )}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Route Name
            </label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              placeholder="Enter route name"
            />
          </div>

       


          {showAddStore && (
            <AddNewStoreModal  setShowAddModal={setShowAddStore} />
            
          )}
        </div>

        <div className="p-6 border-t border-gray-200 flex gap-3">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            className="flex-1 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors disabled:opacity-50"
            disabled={!name.trim() || (withRegoinInput && !regionId)}
          >
            Create Route
          </button>
        </div>
      </div>
    </div>
  );
};

export default AddNewRejionRouteModal;
