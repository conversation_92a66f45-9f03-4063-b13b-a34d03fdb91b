import React from "react";
import {
  Settings,
  Edit,
  Trash2,
  Plus,
  Eye,
  Search,
  Filter,
  X,
  Hash,
  User,
  Building,
  Tag,
  Calendar,
  Clock,
  AlertTriangle,
  Mail,
  LucideTag,
  MapPin,
  CalendarClock,
  RefreshCcw,
  Lock,
} from "lucide-react";
import { useUser } from "../../context/UserContext";
import { useState, useMemo } from "react";
import users from "../../data/users";
import UserManagementModal from "../../Components/Modals/UserManagementModal";
import AddUserModal from "./components/AddNewUserModal";
import EditUserModal from "./components/EditUserModal";
import UserStats from "./components/UserStats";
import {
  Table,
  Tag as AntdTag,
  Button,
  Space,
  Popconfirm,
  Input,
  Select,
  DatePicker,
  Row,
  Col,
  Card,
  Popover,
  Typography,
} from "antd";

import DataTable, {
  getColumnFiltersProps,
  getColumnSearchProps,
  getColumnNumberRange,
} from "./../../utils/DataTable";
import dayjs from "dayjs";

const { RangePicker } = DatePicker;

const   UsersPage = () => {
  const { currentUser } = useUser();
  const [showAddUser, setShowAddUser] = useState(false);
  const [showEditUser, setShowEditUser] = useState(false);
  const [userToEdit, setUserToEdit] = useState(null);
  const [usersList, setUsersList] = useState(users);

  // Filter states
  const [filters, setFilters] = useState({
    search: "",
    role: [],
    dateRange: null,
  });
  const [showFilters, setShowFilters] = useState(false);

  // Get unique values for filter options
  const uniqueRoles = useMemo(() => {
    const roles = [...new Set(usersList.map((u) => u.role))];
    return roles.sort();
  }, [usersList]);

  // Filter users based on current filters
  const filteredUsers = useMemo(() => {
    return usersList.filter((user) => {
      // Search filter
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        const matchesSearch =
          user.name.toLowerCase().includes(searchTerm) ||
          user.role.toLowerCase().includes(searchTerm) ||
          user.id.toString().includes(searchTerm);
        if (!matchesSearch) return false;
      }

      // Role filter (multi-select)
      if (filters.role.length > 0 && !filters.role.includes(user.role)) {
        return false;
      }

      // Date range filter (if we add created date to users)
      if (filters.dateRange && filters.dateRange.length === 2) {
        const userDate = dayjs(user.createdAt || new Date());
        const startDate = filters.dateRange[0];
        const endDate = filters.dateRange[1];

        if (startDate && userDate.isBefore(startDate, "day")) return false;
        if (endDate && userDate.isAfter(endDate, "day")) return false;
      }

      return true;
    });
  }, [usersList, filters]);

  // Reset filters
  const resetFilters = () => {
    setFilters({
      search: "",
      role: [],
      dateRange: null,
    });
  };

  // Update filter
  const updateFilter = (key, value) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const dateFmt = (d) =>
    d
      ? new Date(d).toLocaleString("en-GB", {
          dateStyle: "medium",
          timeStyle: "short",
        })
      : "-";

  const getStatusColor = (status) => {
    switch (status) {
      case "active":
        return "green";
      case "inactive":
        return "red";
      case "suspended":
        return "orange";
      default:
        return "blue";
    }
  };

  const columns = [
    {
      title: (
        <div className="flex items-center gap-2">
          <Hash className="w-4 h-4 text-gray-600" />
          <span>ID</span>
        </div>
      ),
      dataIndex: "id",
      key: "id",
      fixed: true,
      render: (id) => (
        <div className="flex items-center gap-2">
          <Hash className="w-4 h-4 text-gray-600" />
          <span>{id}</span>
        </div>
      ),
      sorter: (a, b) => a.id - b.id,
      ...getColumnNumberRange("id"),
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <User className="w-4 h-4 text-gray-600" />
          <span>Name</span>
        </div>
      ),
      dataIndex: "name",
      key: "name",
      render: (text) => <span className="font-medium">{text}</span>,
      ...getColumnSearchProps("name"),
      sorter: (a, b) => a.name.localeCompare(b.name),
    },

    // NEW: Email
    {
      title: (
        <div className="flex items-center gap-2">
          <Mail className="w-4 h-4 text-gray-600" />
          <span>Email</span>
        </div>
      ),
      dataIndex: "email",
      key: "email",
      render: (email) => <Typography.Text copyable>{email}</Typography.Text>,
      ...getColumnSearchProps("email"),
      sorter: (a, b) => a.email.localeCompare(b.email),
    },

    {
      title: (
        <div className="flex items-center gap-2">
          <LucideTag className="w-4 h-4 text-gray-600" />
          <span>Role</span>
        </div>
      ),
      dataIndex: "role",
      key: "role",
      render: (role) => {
        let color = "purple";
        let label = "Branch";
        if (role === "driver") {
          color = "blue";
          label = "Driver";
        } else if (role === "factory") {
          color = "green";
          label = "Factory";
        } else if (role === "super_admin") {
          color = "red";
          label = "Super Admin";
        }
        return <AntdTag color={color}>{label}</AntdTag>;
      },
      ...getColumnFiltersProps("role", [
        { text: "Driver", value: "driver" },
        { text: "Factory", value: "factory" },
        { text: "Branch", value: "branch" },
        { text: "Super Admin", value: "super_admin" },
      ]),
    },

    // NEW: Branch (searchable)
    {
      title: (
        <div className="flex items-center gap-2">
          <MapPin className="w-4 h-4 text-gray-600" />
          <span>Branch</span>
        </div>
      ),
      dataIndex: "branch",
      key: "branch",
      render: (branch) => branch || "-",
      ...getColumnSearchProps("branch"),
      sorter: (a, b) => (a.branch || "").localeCompare(b.branch || ""),
    },

    // NEW: Status (tag + filters)
    {
      title: (
        <div className="flex items-center gap-2">
          <LucideTag className="w-4 h-4 text-gray-600" />
          <span>Status</span>
        </div>
      ),
      dataIndex: "status",
      key: "status",
      render: (status) => (
        <AntdTag color={getStatusColor(status)}>{status}</AntdTag>
      ),
      ...getColumnFiltersProps("status", [
        { text: "Active", value: "active" },
        { text: "Inactive", value: "inactive" },
        { text: "Suspended", value: "suspended" },
      ]),
    },

    // NEW: Created At
    {
      title: (
        <div className="flex items-center gap-2">
          <CalendarClock className="w-4 h-4 text-gray-600" />
          <span>Created At</span>
        </div>
      ),
      dataIndex: "created_at",
      key: "created_at",
      render: (d) => <span className="text-gray-700">{dateFmt(d)}</span>,
      sorter: (a, b) => new Date(a.created_at) - new Date(b.created_at),
    },

    // NEW: Updated At
    {
      title: (
        <div className="flex items-center gap-2">
          <RefreshCcw className="w-4 h-4 text-gray-600" />
          <span>Updated At</span>
        </div>
      ),
      dataIndex: "updated_at",
      key: "updated_at",
      render: (d) => <span className="text-gray-700">{dateFmt(d)}</span>,
      sorter: (a, b) => new Date(a.updated_at) - new Date(b.updated_at),
      defaultSortOrder: "descend",
    },

    // NEW: Password (masked; reveal for super_admin only)
    {
      title: (
        <div className="flex items-center gap-2">
          <Lock className="w-4 h-4 text-gray-600" />
          <span>Password</span>
        </div>
      ),
      dataIndex: "password",
      key: "password",
      render: (pwd, record) =>
        currentUser?.user?.role === "super_admin" ? (
          <Popover
            placement="left"
            trigger="click"
            content={<code className="text-red-600">{pwd}</code>}
            title="Plaintext (dev only)"
          >
            <Button type="link" className="!p-0">
              Reveal
            </Button>
          </Popover>
        ) : (
          <span className="tracking-widest select-none">••••••••</span>
        ),
    },

    {
      title: (
        <div className="flex items-center gap-2">
          <Clock className="w-4 h-4 text-gray-600" />
          <span>Last Activity</span>
        </div>
      ),
      dataIndex: "lastActivity",
      key: "lastActivity",
      render: () => <span className="text-gray-500">2 hours ago</span>,
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <Settings className="w-4 h-4 text-gray-600" />
          <span>Actions</span>
        </div>
      ),
      key: "actions",
      render: (_, record) => (
        <Space size="middle">
          <Button
            onClick={() => handleEditClick(record)}
            className="text-blue-600 hover:text-blue-700"
            color="blue"
          >
            <Eye className="w-4 h-4" />
          </Button>
          {["super_admin"].includes(currentUser?.user?.role) && (
            <>
              <Button
                color="blue"
                onClick={() => handleEditClick(record)}
                className="text-blue-600 hover:text-blue-700"
              >
                <Edit className="w-4 h-4" />
              </Button>

              <Popconfirm
                title="Delete the user"
                description="Are you sure to delete this user?"
                onConfirm={() => {
                  setUsersList(usersList.filter((u) => u.id !== record.id));
                }}
                onCancel={() => {}}
                okText="Delete"
                okButtonProps={{ danger: true }}
                cancelText="No"
              >
                <Button danger>
                  <Trash2 className="w-4 h-4" />
                </Button>
              </Popconfirm>
            </>
          )}
        </Space>
      ),
    },
  ];

  const handleAddUser = (newUser) => {
    setUsersList((prev) => [...prev, newUser]);
  };

  const handleEditUser = (updatedUser) => {
    setUsersList((prev) =>
      prev.map((user) => (user.id === updatedUser.id ? updatedUser : user))
    );
  };

  const handleEditClick = (user) => {
    setUserToEdit(user);
    setShowEditUser(true);
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200">
      <div className="p-6">
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-900">
              User Management
            </h1>
            <div className="flex gap-3">
              <button
                onClick={() => setShowAddUser(true)}
                className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                Add User
              </button>
            </div>
          </div>

          {/* Statistics Section */}
          <UserStats users={usersList} />

          {/* Filter Section */}
          <Card>
            {/* Filter Controls */}
            <div className="">
              <Row gutter={[16, 16]}>
                {/* Role Filter */}
                <Col xs={24} sm={12}>
                  <div>
                    <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
                      <Tag className="w-4 h-4 text-gray-600" />
                      <span>Role</span>
                    </label>
                    <Select
                      size="large"
                      mode="multiple"
                      placeholder="Select roles"
                      value={filters.role}
                      onChange={(value) => updateFilter("role", value)}
                      style={{ width: "100%" }}
                      allowClear
                      options={[
                        { label: "Driver", value: "driver" },
                        { label: "Factory", value: "factory" },
                        { label: "Branch", value: "branch" },
                        { label: "Super Admin", value: "super_admin" },
                      ]}
                    />
                  </div>
                </Col>

                {/* Date Range Filter */}
                <Col xs={24} sm={12}>
                  <div>
                    <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
                      <Calendar className="w-4 h-4 text-gray-600" />
                      <span>Date Range</span>
                    </label>
                    <RangePicker
                      size="large"
                      value={filters.dateRange}
                      onChange={(dates) => updateFilter("dateRange", dates)}
                      style={{ width: "100%" }}
                      placeholder={["Start Date", "End Date"]}
                    />
                  </div>
                </Col>
              </Row>
            </div>
          </Card>

          {/* Data Table */}
          <div className="overflow-x-auto">
            <DataTable
              searchPlaceholder={"Search Users"}
              onAddClick={() => setShowAddUser(true)}
              table={{
                header: columns,
                rows: filteredUsers,
              }}
            />
          </div>

          {/* {showUserManagement && (
        <UserManagementModal setShowUserManagement={setShowUserManagement} />
      )} */}

          {showAddUser && (
            <AddUserModal
              isOpen={showAddUser}
              onClose={() => setShowAddUser(false)}
              onAddUser={handleAddUser}
            />
          )}

          {showEditUser && (
            <EditUserModal
              isOpen={showEditUser}
              onClose={() => {
                setShowEditUser(false);
                setUserToEdit(null);
              }}
              onEditUser={handleEditUser}
              userToEdit={userToEdit}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default UsersPage;
