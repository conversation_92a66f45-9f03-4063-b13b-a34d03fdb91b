// src/pages/QualityDashboard.jsx (or wherever you keep it)
import {
  Package,
  CheckCircle,
  Store,
  Download,
  Eye,
  Search,
  Grid3X3,
  List,
  Table,
  SortAsc,
  SortDesc,
  XCircle,
  Loader,
} from "lucide-react";
import { useState, useMemo, useEffect } from "react";
import { DatePicker, Pagination } from "antd";
import dayjs from "dayjs";
import {
  getQualityShipments,
  getProductionsData,
  confirmQualityChecklist,
  rejectQualityChecklist,
} from "../../api/apiService";
import ShipmentDetailsModal from "../../Components/Modals/ShipmentDetailsModal";
import TestQualityModal from "./components/TestQualityModal";
import { useUser } from "../../context/UserContext";

const QualityDashboard = () => {
  const { currentUser } = useUser();
  const [shipments, setShipments] = useState([]);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedShipment, setSelectedShipment] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const [openTestModal, setOpenTestModal] = useState(false);

  // Filter and view states
  const [viewMode, setViewMode] = useState("cards"); // cards, list, table
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [qualityFilter, setQualityFilter] = useState("all"); // all | Pending | Confirmed | Rejected
  const [dateFilter, setDateFilter] = useState("all");
  const [createdFrom, setCreatedFrom] = useState("");
  const [createdTo, setCreatedTo] = useState("");

  const handleResetFilters = () => {
    setSearchTerm("");
    setStatusFilter("all");
    setQualityFilter("all");
    setDateFilter("all");
    setCreatedFrom("");
    setCreatedTo("");
    setSortBy("created");
    setSortOrder("desc");
    setCurrentPage(1);
  };
  const [sortBy, setSortBy] = useState("created");
  const [sortOrder, setSortOrder] = useState("desc");
  const [selectedProduction, setSelectedProduction] = useState(1); // Default to production ID 1
  const [productions, setProductions] = useState([]);
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const [total, setTotal] = useState(0);
  const [lastPage, setLastPage] = useState(1);

  // Fetch productions and shipments on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Helper to safely extract array data regardless of API shape
        const extractArray = (obj, keys) => {
          if (Array.isArray(obj)) return obj;
          for (const key of keys) {
            const value = obj && obj[key];
            if (Array.isArray(value)) return value;
          }
          return [];
        };

        // Fetch productions
        const productionsRes = await getProductionsData();
        const productionsList = extractArray(productionsRes, [
          "productions",
          "data",
          "items",
          "rows",
        ]);
        setProductions(productionsList);

        // Fetch quality shipments with server pagination
        const shipmentsRes = await getQualityShipments(
           currentPage,

        );
        console.log("shipmentsRes", shipmentsRes);
        const allShipmentsRaw = extractArray(shipmentsRes, [
          "shipments",
          "data",
          "items",
          "rows",
        ]);

        // Normalize keys to camelCase fields expected by UI
        const allShipments = allShipmentsRaw.map((s) => {
          const qualityStatusRaw = s?.quality_status ?? s?.qualityStatus;
          const qualityStatus = qualityStatusRaw
            ? qualityStatusRaw.charAt(0).toUpperCase() +
              qualityStatusRaw.slice(1)
            : s?.qualityStatus ?? "Pending";
          return {
            ...s,
            bakeryType: s?.bakeryType ?? s?.bakery_type ?? s?.bakery ?? "",
            createdAt: s?.createdAt ?? s?.created_at ?? null,
            updatedAt: s?.updatedAt ?? s?.updated_at ?? null,
            productionId:
              s?.productionId ?? s?.production_id ?? s?.production?.id ?? null,
            qualityStatus,
            timestamps: {
              ...(s?.timestamps || {}),
              created:
                s?.timestamps?.created ??
                s?.createdAt ??
                s?.created_at ??
                s?.exit_time ??
                null,
              delivery:
                s?.timestamps?.delivery ??
                s?.deliveryAt ??
                s?.delivery_at ??
                null,
            },
          };
        });

        // Optional pre-filter by selected production if present on shipment
        const filteredByProduction = allShipments.filter((s) => {
          const prodId =
            s?.productionId ?? s?.production_id ?? s?.production?.id ?? null;
          if (!selectedProduction) return true;
          if (prodId == null) return true; // keep if unknown to avoid hiding data
          return Number(prodId) === Number(selectedProduction);
        });

        setShipments(filteredByProduction);

        // Set pagination if present
        const pg = shipmentsRes?.pagination || shipmentsRes?.meta || null;
        if (pg) {
          setCurrentPage(pg.current_page ?? pg.currentPage ?? currentPage);
          setPerPage(pg.per_page ?? pg.perPage ?? perPage);
          setTotal(pg.total ?? total);
          setLastPage(pg.last_page ?? pg.lastPage ?? lastPage);
        }
      } catch (err) {
        console.error("Error fetching data:", err);
        const message =
          err?.response?.data?.message || err?.message || "Failed to load data";
        setError(message);
      } finally {
        setLoading(false);
      }
    };

    if (currentUser) {
      fetchData();
    }
  }, [selectedProduction, currentPage, currentUser, createdFrom, createdTo]);

  const openDetailsModal = (shipment) => {
    setSelectedShipment(shipment);
    setShowDetailsModal(true);
  };

  // === Handlers: QUALITY ===
  const handleSetQualityStatus = async (shipmentId, status, notes = "") => {
    try {
      const shipment = shipments.find((s) => s.id === shipmentId);
      if (!shipment) return;

      // Prepare answers for API (empty answers for quick confirm/reject)
      const answers = [
        {
          checklist_question_id: 1,
          answer: status === "Confirmed" ? "Yes" : "No",
          quality_id: 3,
        },
      ];

      let result;
      if (status === "Confirmed") {
        result = await confirmQualityChecklist({
          shipment_id: shipmentId,
          production_id: shipment.productionId || 1,
          answers: answers,
        });
      } else {
        result = await rejectQualityChecklist({
          shipment_id: shipmentId,
          production_id: shipment.productionId || 1,
          answers: answers,
        });
      }

      if (result?.data?.message) {
        // Update local state on success
        setShipments((prev) =>
          prev.map((s) =>
            s.id === shipmentId
              ? {
                  ...s,
                  qualityStatus: status,
                  qualityNotes: notes || s.qualityNotes,
                  timestamps: {
                    ...s.timestamps,
                    qualityCheckedAt: new Date(),
                  },
                }
              : s
          )
        );
        console.log(
          `Quality status updated to ${status} for shipment ${shipmentId}`
        );
      } else {
        throw new Error(
          result?.data?.message || "Failed to update quality status"
        );
      }
    } catch (error) {
      console.error("Error updating quality status:", error);
      setError(`Failed to update quality status: ${error.message}`);
    }
  };

  const rejectWithNote = (shipment) => {
    setSelectedShipment(shipment);
    setOpenTestModal(true);
  };

  // === Filtering & Sorting ===
  const filteredAndSortedShipments = useMemo(() => {
    let filtered = shipments.filter((shipment) => {
      // Status filter
      if (statusFilter !== "all" && shipment.status !== statusFilter)
        return false;

      // Production filter (in case backend returned mixed productions)
      const prodId =
        shipment?.productionId ??
        shipment?.production_id ??
        shipment?.production?.id ??
        null;
      if (
        selectedProduction &&
        prodId != null &&
        Number(prodId) !== Number(selectedProduction)
      )
        return false;

      // Search filter
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        const matchesSearch =
          (shipment.bakeryType || "").toLowerCase().includes(searchLower) ||
          (shipment.driver || "").toLowerCase().includes(searchLower) ||
          (shipment.branch || "").toLowerCase().includes(searchLower) ||
          shipment.id.toString().includes(searchLower);
        if (!matchesSearch) return false;
      }

      // Date filter
      if (dateFilter !== "all") {
        const createdAt =
          shipment?.timestamps?.created ||
          shipment?.createdAt ||
          shipment?.created_at;
        const shipmentDate = createdAt ? new Date(createdAt) : null;
        if (!shipmentDate || isNaN(shipmentDate)) return false;
        const now = new Date();
        const daysDiff = Math.floor(
          (now - shipmentDate) / (1000 * 60 * 60 * 24)
        );

        if (dateFilter === "today" && daysDiff !== 0) return false;
        if (dateFilter === "week" && daysDiff > 7) return false;
        if (dateFilter === "month" && daysDiff > 30) return false;
      }

      // Created range filter (by created_at)
      if (createdFrom || createdTo) {
        const createdAt =
          shipment?.timestamps?.created ||
          shipment?.createdAt ||
          shipment?.created_at;
        const shipmentDate = createdAt ? new Date(createdAt) : null;
        if (!shipmentDate || isNaN(shipmentDate)) return false;

        let fromDate = createdFrom ? new Date(createdFrom) : null;
        let toDate = createdTo ? new Date(createdTo) : null;
        if (fromDate && !isNaN(fromDate)) {
          fromDate.setHours(0, 0, 0, 0);
          if (shipmentDate < fromDate) return false;
        }
        if (toDate && !isNaN(toDate)) {
          toDate.setHours(23, 59, 59, 999);
          if (shipmentDate > toDate) return false;
        }
      }

      // Quality filter
      const q = shipment.qualityStatus || "Pending";
      if (qualityFilter !== "all" && q !== qualityFilter) return false;

      return true;
    });

    // Sort shipments
    return filtered.sort((a, b) => {
      let aValue, bValue;

      switch (sortBy) {
        case "created":
          aValue = new Date(
            a?.timestamps?.created ??
              a?.createdAt ??
              a?.created_at ??
              a?.exit_time ??
              0
          );
          bValue = new Date(
            b?.timestamps?.created ??
              b?.createdAt ??
              b?.created_at ??
              b?.exit_time ??
              0
          );
          break;
        case "delivery":
          aValue = new Date(a?.timestamps?.delivery ?? 0);
          bValue = new Date(b?.timestamps?.delivery ?? 0);
          break;
        case "bakeryType":
          aValue = (a.bakeryType || "").toLowerCase();
          bValue = (b.bakeryType || "").toLowerCase();
          break;
        case "quantity":
          aValue = a.quantity || 0;
          bValue = b.quantity || 0;
          break;
        case "qualityStatus": {
          // order: Confirmed < Pending < Rejected (customize if you want)
          const order = { Confirmed: 0, Pending: 1, Rejected: 2 };
          aValue = order[a.qualityStatus || "Pending"];
          bValue = order[b.qualityStatus || "Pending"];
          break;
        }
        default:
          aValue = new Date(
            a?.timestamps?.created ??
              a?.createdAt ??
              a?.created_at ??
              a?.exit_time ??
              0
          );
          bValue = new Date(
            b?.timestamps?.created ??
              b?.createdAt ??
              b?.created_at ??
              b?.exit_time ??
              0
          );
      }

      if (sortOrder === "asc") {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  }, [
    shipments,
    searchTerm,
    statusFilter,
    qualityFilter,
    dateFilter,
    sortBy,
    sortOrder,
  ]);

  // For the quality tester, show everything after filtering:
  const arrivedShipments = filteredAndSortedShipments;

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">Quality Dashboard</h1>

      {/* Quality KPIs */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex items-center gap-3">
            <div className="bg-yellow-100 p-3 rounded-full">
              <Package className="w-6 h-6 text-yellow-700" />
            </div>
            <div className="mr-4">
              <p className="text-2xl font-bold text-gray-900">
                {
                  shipments.filter(
                    (s) => (s.qualityStatus || "Pending") === "Pending"
                  ).length
                }
              </p>
              <p className="text-gray-600">Quality Pending</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex items-center gap-3">
            <div className="bg-green-100 p-3 rounded-full">
              <CheckCircle className="w-6 h-6 text-green-700" />
            </div>
            <div className="mr-4">
              <p className="text-2xl font-bold text-gray-900">
                {
                  shipments.filter((s) => s.qualityStatus === "Confirmed")
                    .length
                }
              </p>
              <p className="text-gray-600">Quality Confirmed</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex items-center gap-3">
            <div className="bg-red-100 p-3 rounded-full">
              <XCircle className="w-6 h-6 text-red-700" />
            </div>
            <div className="mr-4">
              <p className="text-2xl font-bold text-gray-900">
                {shipments.filter((s) => s.qualityStatus === "Rejected").length}
              </p>
              <p className="text-gray-600">Quality Rejected</p>
            </div>
          </div>
        </div>
      </div>

      {/* Wrapper Card , stats */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <h2 className="text-xl font-bold text-gray-900">Shipments</h2>
              <p className="text-sm text-gray-600 mt-1">
                {arrivedShipments.length} shipments — Quality view
              </p>
            </div>

            {/* View Toggle */}
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">View:</span>
              <div className="flex bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setViewMode("cards")}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === "cards"
                      ? "bg-white text-gray-900 shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                >
                  <Grid3X3 className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode("list")}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === "list"
                      ? "bg-white text-gray-900 shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                >
                  <List className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode("table")}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === "table"
                      ? "bg-white text-gray-900 shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                >
                  <Table className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Filter Controls */}
        <div className="p-6 border-b border-gray-200 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Production Filter */}
            {/* <select
              value={selectedProduction}
              onChange={(e) => setSelectedProduction(parseInt(e.target.value))}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            >
              {productions.map((production) => (
                <option key={production.id} value={production.id}>
                  {production.name || `Production ${production.id}`}
                </option>
              ))}
            </select> */}

            {/* Search */}
            <div className="relative col-span-2">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search shipments..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              />
            </div>

            {/* Quality Filter */}
            <select
              value={qualityFilter}
              onChange={(e) => setQualityFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            >
              <option value="all">All Quality Staus</option>
              <option value="Pending">Pending</option>
              <option value="Confirmed">Confirmed</option>
              <option value="Rejected">Rejected</option>
            </select>

            {/* Date Filter */}
            <select
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            >
              <option value="all">All Time</option>
              <option value="today">Today</option>
              <option value="week">This Week</option>
              <option value="month">This Month</option>
            </select>
            {/* Created At Range (AntD) */}
            <DatePicker.RangePicker
              value={
                createdFrom || createdTo
                  ? [
                      createdFrom ? dayjs(createdFrom, "YYYY-MM-DD") : null,
                      createdTo ? dayjs(createdTo, "YYYY-MM-DD") : null,
                    ]
                  : null
              }
              onChange={(dates) => {
                if (!dates || dates.length === 0) {
                  setCreatedFrom("");
                  setCreatedTo("");
                } else {
                  const [from, to] = dates;
                  setCreatedFrom(from ? from.format("YYYY-MM-DD") : "");
                  setCreatedTo(to ? to.format("YYYY-MM-DD") : "");
                }
              }}
              format="YYYY-MM-DD"
              className="px-0 py-0 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 h-[40px]"
              allowClear
              placeholder={["From date", "To date"]}
            />

            {/* Sort */}
            <div className="flex gap-2">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              >
                <option value="created">Created Date</option>
                <option value="delivery">Delivery Date</option>
                <option value="bakeryType">Bakery Type</option>
                <option value="quantity">Quantity</option>
                <option value="qualityStatus">Quality Status</option>
              </select>
              <button
                onClick={() =>
                  setSortOrder(sortOrder === "asc" ? "desc" : "asc")
                }
                className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                {sortOrder === "asc" ? (
                  <SortAsc className="w-4 h-4" />
                ) : (
                  <SortDesc className="w-4 h-4" />
                )}
              </button>
            </div>

            {/* Reset Filters */}
            <button
              onClick={handleResetFilters}
              className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-gray-700"
            >
              Reset
            </button>
          </div>
        </div>

        {loading ? (
          <div className="p-12 text-center">
            <Loader className="w-16 h-16 text-orange-500 mx-auto mb-4 animate-spin" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Loading shipments...
            </h3>
            <p className="text-gray-600">
              Please wait while we fetch the quality data.
            </p>
          </div>
        ) : error ? (
          <div className="p-12 text-center">
            <XCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Error loading shipments
            </h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors"
            >
              Retry
            </button>
          </div>
        ) : arrivedShipments.length === 0 ? (
          <div className="p-12 text-center">
            <Package className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No shipments
            </h3>
            <p className="text-gray-600">
              No shipments found for the selected production.
            </p>
          </div>
        ) : (
          <div className="p-6 overflow-x-auto">
            {/* CARDS VIEW */}
            {viewMode === "cards" && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {arrivedShipments.map((shipment) => {
                  const qStatus = shipment.qualityStatus || "Pending";
                  return (
                    <div
                      key={shipment.id}
                      className={`bg-white border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow cursor-pointer 
                        ${
                          shipment.status === "Sold"
                            ? "!bg-green-50 border-green-600 border-dashed shadow-inner"
                            : ""
                        }`}
                    >
                      {/* Header */}
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex-1">
                          <h3 className="font-semibold text-gray-900 text-lg mb-1">
                            {shipment.bakeryType}
                          </h3>
                          <p className="text-sm text-gray-600">
                            ID: #{shipment.id}
                          </p>
                        </div>
                        <div className="mb-3">
                          <span
                            className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full
                          ${
                            qStatus === "Confirmed"
                              ? "bg-green-100 text-green-800"
                              : qStatus === "Rejected"
                              ? "bg-red-100 text-red-800"
                              : "bg-yellow-100 text-yellow-800"
                          }`}
                          >
                            Quality: {qStatus}
                          </span>
                        </div>
                      </div>

                      {/* Details */}
                      <div className="space-y-2 mb-4">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">Quantity:</span>
                          <span className="font-medium text-gray-900">
                            {shipment.quantity}
                          </span>
                        </div>

                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">Created at:</span>
                          <span className="font-medium text-sm font-sans text-gray-500">
                            {shipment?.timestamps?.created
                              ? new Date(
                                  shipment?.timestamps?.created
                                ).toLocaleString("en-US", {
                                  year: "numeric",
                                  month: "short",
                                  day: "numeric",
                                  hour: "2-digit",
                                  minute: "2-digit",
                                  hour12: true,
                                })
                              : "N/A"}
                          </span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">
                            Quality Checked at:
                          </span>
                          <span className="font-medium text-sm font-sans text-blue-500">
                            {shipment?.timestamps?.qualityCheckedAt
                              ? new Date(
                                  shipment?.timestamps?.qualityCheckedAt
                                ).toLocaleString("en-US", {
                                  year: "numeric",
                                  month: "short",
                                  day: "numeric",
                                  hour: "2-digit",
                                  minute: "2-digit",
                                  hour12: true,
                                })
                              : "N/A"}
                          </span>
                        </div>
                      </div>

                      {/* Quality badge */}

                      {/* Actions */}
                      <div
                        className="space-y-3"
                        onClick={(e) => e.stopPropagation()}
                      >
                        {shipment.qualityNotes && (
                          <p className="text-sm text-gray-600 bg-orange-100 p-3 rounded-xl border border-orange-300 ">
                            <span className="text-orange-600 font-bold font-mono">
                              Note:
                            </span>{" "}
                            {shipment.qualityNotes}
                          </p>
                        )}

                        {/* Quality actions */}
                        <div className="flex gap-2">
                          {shipment.qualityStatus == "Pending" && (
                            <button
                              onClick={() => {
                                setOpenTestModal(true);
                                setSelectedShipment(shipment);
                              }}
                              className="flex-1 flex items-center justify-center gap-2 bg-green-600 text-white px-4 py-2.5 rounded-lg text-sm font-medium hover:bg-green-700 transition-colors"
                            >
                              <Loader className="w-4 h-4" />
                              Test Quality
                            </button>
                          )}
                          {/* <button
                            onClick={() =>
                              handleSetQualityStatus(shipment.id, "Confirmed")
                            }
                            className="flex-1 bg-green-600 text-white px-4 py-2.5 rounded-lg text-sm font-medium hover:bg-green-700 transition-colors"
                          >
                            Confirm Quality
                          </button> */}
                          {/* <button
                            onClick={() => rejectWithNote(shipment)}
                            className="flex-1 bg-red-600 text-white px-4 py-2.5 rounded-lg text-sm font-medium hover:bg-red-700 transition-colors"
                          >
                            Reject
                          </button> */}
                        </div>

                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            openDetailsModal(shipment);
                          }}
                          className="w-full bg-orange-600 text-white px-4 py-2.5 rounded-lg text-sm font-medium hover:bg-gray-700 transition-colors flex items-center justify-center gap-2"
                        >
                          <Eye className="w-4 h-4" />
                          View Details
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}

            {/* LIST VIEW */}
            {viewMode === "list" && (
              <div className="space-y-4 min-w-[999px] relative !overflow-auto">
                {arrivedShipments.map((shipment) => {
                  const qStatus = shipment.qualityStatus || "Pending";
                  return (
                    <div
                      key={shipment.id}
                      className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                      onClick={() => openDetailsModal(shipment)}
                    >
                      <div className="whitespace-nowrap flex items-center justify-between">
                        <div className="flex-1 grid grid-cols-4 gap-4">
                          <div>
                            <h3 className="font-semibold text-gray-900">
                              {shipment.bakeryType}
                            </h3>
                            <p className="text-sm text-gray-600">
                              ID: #{shipment.id}
                            </p>
                          </div>
                          <div className="w-fit flex-1">
                            <p className="text-sm text-gray-600">Quantity</p>
                            <p className="font-medium text-gray-900">
                              {shipment.quantity}
                            </p>
                          </div>

                          {/* Quality */}
                          <div className="w-fit flex-1">
                            <p className="text-sm text-gray-600">Quality</p>
                            <p
                              className={`font-medium ${
                                qStatus === "Confirmed"
                                  ? "text-green-700"
                                  : qStatus === "Rejected"
                                  ? "text-red-700"
                                  : "text-yellow-700"
                              }`}
                            >
                              {qStatus}
                            </p>
                          </div>
                          <div className="w-fit flex-1">
                            <p className="text-sm text-gray-600">Created At</p>
                            <p
                              className={`text-sm ${
                                qStatus === "Confirmed"
                                  ? "text-green-700"
                                  : qStatus === "Rejected"
                                  ? "text-red-700"
                                  : "text-yellow-700"
                              }`}
                            >
                              {shipment?.timestamps?.created
                                ? new Date(
                                    shipment?.timestamps?.created
                                  ).toLocaleString("en-US", {
                                    year: "numeric",
                                    month: "short",
                                    day: "numeric",
                                    hour: "2-digit",
                                    minute: "2-digit",
                                    hour12: true,
                                  })
                                : "N/A"}
                            </p>
                          </div>  
                        </div>

                        <div
                          className="flex items-center gap-3 ml-4"
                          onClick={(e) => e.stopPropagation()}
                        >
                          {/* Quality actions */}
                          {qStatus === "Pending" ? (
                            <button
                              onClick={() => {
                                setSelectedShipment(shipment);
                                setOpenTestModal(true);
                              }}
                              className="bg-blue-600 text-white px-3 py-1.5 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
                            >
                              Test Quality
                            </button>
                          ) : null}

                          {/* Logistics quick actions (optional) */}
                          {shipment.status === "Delivered" && (
                            <button
                              onClick={() => openReceiveModal(shipment)}
                              className="bg-green-600 text-white px-3 py-1.5 rounded-lg text-sm font-medium hover:bg-green-700 transition-colors flex items-center gap-1"
                            >
                              <Download className="w-3 h-3" />
                              Receive
                            </button>
                          )}
                          {shipment.status === "Received" && (
                            <button
                              onClick={() => openDisplayModal(shipment)}
                              className="bg-purple-600 text-white px-3 py-1.5 rounded-lg text-sm font-medium hover:bg-purple-700 transition-colors flex items-center gap-1"
                            >
                              <Store className="w-3 h-3" />
                              Display
                            </button>
                          )}
                          <button
                            onClick={() => openDetailsModal(shipment)}
                            className="bg-gray-600 text-white px-3 py-1.5 rounded-lg text-sm font-medium hover:bg-gray-700 transition-colors flex items-center gap-1"
                          >
                            <Eye className="w-3 h-3" />
                            Details
                          </button>
                        </div>
                      </div>

                      {shipment.qualityNotes && (
                        <p className="mt-2 text-xs text-gray-600">
                          Note: {shipment.qualityNotes}
                        </p>
                      )}
                    </div>
                  );
                })}
              </div>
            )}

            {/* TABLE VIEW */}
            {viewMode === "table" && (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        ID
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Bakery Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Quantity
                      </th>

                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Quality Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Created At
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {arrivedShipments.map((shipment) => {
                      const qStatus = shipment.qualityStatus || "Pending";
                      return (
                        <tr
                          key={shipment.id}
                          className="hover:bg-gray-50 cursor-pointer"
                          onClick={() => openDetailsModal(shipment)}
                        >
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            #{shipment.id}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {shipment.bakeryType}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {shipment.quantity}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span
                              className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                qStatus === "Confirmed"
                                  ? "text-green-700"
                                  : qStatus === "Rejected"
                                  ? "text-red-700"
                                  : "text-yellow-700"
                              }`}
                            >
                              {shipment.qualityStatus}
                            </span>
                          </td>

                          {/* Quality */}
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            <span
                              className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full
                              ${
                                qStatus === "Confirmed"
                                  ? "bg-green-100 text-green-800"
                                  : qStatus === "Rejected"
                                  ? "bg-red-100 text-red-800"
                                  : "bg-yellow-100 text-yellow-800"
                              }`}
                            >
                              {qStatus}
                            </span>
                          </td>
                          <td>
                          <p
                              className={`text-sm ${
                                qStatus === "Confirmed"
                                  ? "text-green-700"
                                  : qStatus === "Rejected"
                                  ? "text-red-700"
                                  : "text-yellow-700"
                              }`}
                            >
                              {shipment?.timestamps?.created
                                ? new Date(
                                    shipment?.timestamps?.created
                                  ).toLocaleString("en-US", {
                                    year: "numeric",
                                    month: "short",
                                    day: "numeric",
                                    hour: "2-digit",
                                    minute: "2-digit",
                                    hour12: true,
                                  })
                                : "N/A"}
                            </p>
                          </td>

                          {/* Actions */}
                          <td
                            className="px-6 py-4 whitespace-nowrap text-sm font-medium"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div className="flex gap-2">
                              {/* Quality actions */}
                              {qStatus === "Pending" ? (
                                <button
                                  onClick={() => {
                                    setSelectedShipment(shipment);
                                    setOpenTestModal(true);
                                  }}
                                  className="bg-blue-600 text-white px-3 py-1 rounded-lg text-xs font-medium hover:bg-blue-700 transition-colors"
                                >
                                  Test Quality
                                </button>
                              ) : (
                               null
                              )}

                              {/* Logistics quick actions (optional) */}
                              {shipment.status === "Delivered" && (
                                <button
                                  onClick={() => openReceiveModal(shipment)}
                                  className="bg-green-600 text-white px-3 py-1 rounded-lg text-xs font-medium hover:bg-green-700 transition-colors flex items-center gap-1"
                                >
                                  <Download className="w-3 h-3" />
                                  Receive
                                </button>
                              )}
                              {shipment.status === "Received" && (
                                <button
                                  onClick={() => openDisplayModal(shipment)}
                                  className="bg-purple-600 text-white px-3 py-1 rounded-lg text-xs font-medium hover:bg-purple-700 transition-colors flex items-center gap-1"
                                >
                                  <Store className="w-3 h-3" />
                                  Display
                                </button>
                              )}
                              <button
                                onClick={() => openDetailsModal(shipment)}
                                className="bg-gray-600 text-white px-3 py-1 rounded-lg text-xs font-medium hover:bg-gray-700 transition-colors flex items-center gap-1"
                              >
                                <Eye className="w-3 h-3" />
                                View
                              </button>
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Pagination Controls */}
      <div className="flex items-center justify-between mt-4">
        <div className="text-sm text-gray-600">
          Page {currentPage} of {lastPage} • Total {total}
        </div>
        <Pagination
          current={currentPage}
          total={total}
          pageSize={perPage}
          showSizeChanger={false}
          onChange={(page) => setCurrentPage(page)}
          showTotal={(t) => `${t} items`}
        />
      </div>

      {showDetailsModal && (
        <ShipmentDetailsModal
          setShowDetailsModal={setShowDetailsModal}
          selectedShipment={selectedShipment}
          setSelectedShipment={setSelectedShipment}
        />
      )}

      <TestQualityModal
        open={openTestModal}
        onClose={() => setOpenTestModal(false)}
        onConfirm={(qualityStatus) => {
          console.log("Quality confirmed:", qualityStatus);
          setOpenTestModal(false);
          // Refresh the shipments data to get updated quality status
          const fetchData = async () => {
            try {
              const shipmentsRes = await getQualityShipments(
              currentPage,
              );
              const allShipmentsRaw = Array.isArray(shipmentsRes)
                ? shipmentsRes
                : Array.isArray(shipmentsRes?.data)
                ? shipmentsRes.data
                : Array.isArray(shipmentsRes?.shipments)
                ? shipmentsRes.shipments
                : [];

              const allShipments = allShipmentsRaw.map((s) => {
                const qualityStatusRaw = s?.quality_status ?? s?.qualityStatus;
                const qualityStatus = qualityStatusRaw
                  ? qualityStatusRaw.charAt(0).toUpperCase() +
                    qualityStatusRaw.slice(1)
                  : s?.qualityStatus ?? "Pending";
                return {
                  ...s,
                  bakeryType:
                    s?.bakeryType ?? s?.bakery_type ?? s?.bakery ?? "",
                  createdAt: s?.createdAt ?? s?.created_at ?? null,
                  updatedAt: s?.updatedAt ?? s?.updated_at ?? null,
                  productionId:
                    s?.productionId ??
                    s?.production_id ??
                    s?.production?.id ??
                    null,
                  qualityStatus,
                  timestamps: {
                    ...(s?.timestamps || {}),
                    created:
                      s?.timestamps?.created ??
                      s?.createdAt ??
                      s?.created_at ??
                      s?.exit_time ??
                      null,
                    delivery:
                      s?.timestamps?.delivery ??
                      s?.deliveredAt ??
                      s?.delivered_at ??
                      null,
                    qualityChecked:
                      s?.timestamps?.qualityChecked ??
                      s?.qualityCheckedAt ??
                      s?.quality_checked_at ??
                      null,
                  },
                };
              });
              setShipments(allShipments);
            } catch (error) {
              console.error("Error refreshing shipments:", error);
            }
          };
          fetchData();
        }}
        onReject={(qualityStatus) => {
          console.log("Quality rejected:", qualityStatus);
          setOpenTestModal(false);
          // Refresh the shipments data to get updated quality status
          const fetchData = async () => {
            try {
              const shipmentsRes = await getQualityShipments(
               currentPage,
              );
              const allShipmentsRaw = Array.isArray(shipmentsRes)
                ? shipmentsRes
                : Array.isArray(shipmentsRes?.data)
                ? shipmentsRes.data
                : Array.isArray(shipmentsRes?.shipments)
                ? shipmentsRes.shipments
                : [];

              const allShipments = allShipmentsRaw.map((s) => {
                const qualityStatusRaw = s?.quality_status ?? s?.qualityStatus;
                const qualityStatus = qualityStatusRaw
                  ? qualityStatusRaw.charAt(0).toUpperCase() +
                    qualityStatusRaw.slice(1)
                  : s?.qualityStatus ?? "Pending";
                return {
                  ...s,
                  bakeryType:
                    s?.bakeryType ?? s?.bakery_type ?? s?.bakery ?? "",
                  createdAt: s?.createdAt ?? s?.created_at ?? null,
                  updatedAt: s?.updatedAt ?? s?.updated_at ?? null,
                  productionId:
                    s?.productionId ??
                    s?.production_id ??
                    s?.production?.id ??
                    null,
                  qualityStatus,
                  timestamps: {
                    ...(s?.timestamps || {}),
                    created:
                      s?.timestamps?.created ??
                      s?.createdAt ??
                      s?.created_at ??
                      s?.exit_time ??
                      null,
                    delivery:
                      s?.timestamps?.delivery ??
                      s?.deliveredAt ??
                      s?.delivered_at ??
                      null,
                    qualityChecked:
                      s?.timestamps?.qualityChecked ??
                      s?.qualityCheckedAt ??
                      s?.quality_checked_at ??
                      null,
                  },
                };
              });
              setShipments(allShipments);
            } catch (error) {
              console.error("Error refreshing shipments:", error);
            }
          };
          fetchData();
        }}
        selectedShipment={selectedShipment}
      />
    </div>
  );
};

export default QualityDashboard;
