import { X } from "lucide-react";
import { baseURL } from "../../../api";

const RejectionDetailsModal = ({ show, details, shipmentId, onClose }) => {
  if (!show) return null;

  const reason = details?.note || "-";
  const vehicleNumber = details?.vehicle_number || "-";
  const updatedAt = details?.updatedAt
    ? new Date(details.updatedAt).toLocaleString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      })
    : "-";
  const images = Array.isArray(details?.photos) ? details.photos : [];

  const deliveryDate = details?.delivery_datetime
    ? new Date(details.delivery_datetime).toLocaleString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      })
    : "-";

  console.log("images", images);

  return (
    <div
      className="fixed   overflow-auto !m-0 inset-0 bg-black/60 backdrop-blur-sm flex  items-start justify-center z-50 p-4"
      onClick={onClose}
    >
      <div
        className="bg-white overflow-auto rounded-2xl shadow-2xl w-full max-w-2xl "
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-5 border-b border-gray-200">
          <div>
            <h2 className="text-lg font-bold text-gray-900">Ticket Details</h2>
            {shipmentId ? (
              <p className="text-sm text-gray-600">Shipment #{shipmentId}</p>
            ) : null}
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Body */}
        <div className="p-5 space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            <div className="rounded-lg border border-red-200 bg-red-50 p-4">
              <p className="text-xs font-semibold text-red-700 mb-2">Notes</p>
              <p className="text-sm text-red-800">{reason}</p>
            </div>
            <div className="rounded-lg border border-gray-200 bg-gray-50 p-4">
              <p className="text-xs font-semibold text-gray-700 mb-2">
                Vehicle Number
              </p>
              <p className="text-sm text-gray-900">{vehicleNumber}</p>
            </div>
            <div className="rounded-lg border border-gray-200 bg-blue-50 p-4">
              <p className="text-xs font-semibold text-gray-700 mb-2">
                delivery Date
              </p>
              <p className="text-sm text-gray-900">{deliveryDate}</p>
            </div>
          </div>

          <div>
            <p className="text-sm font-semibold text-gray-900 mb-2">
              Photos ({images.filter(img => Boolean (img.photo_url)).length})
            </p>
            {images.filter(img => Boolean(img.photo_url)).length === 0 ? (
              <p className="text-sm text-gray-500">No photos attached.</p>
            ) : (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
                {images.map((img, idx) => {
                  return img.photo_url &&  <div
                  key={idx}
                  className="aspect-square w-full rounded-lg overflow-hidden border border-gray-200 bg-gray-100"
                >
                  <img
                    src={
                      typeof img.photo_url === "string"
                        ? "https://dd-ops.com/donuttracking" + img?.photo_url
                        : ""
                    }
                    alt={`rejection-${idx}`}
                    className="w-full h-full object-cover"
                  />
                </div>
                }
                 
                )}
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default RejectionDetailsModal;
