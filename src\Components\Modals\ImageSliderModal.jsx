import 'react-image-gallery/styles/css/image-gallery.css';
import React from 'react';
import ImageGallery from 'react-image-gallery';

const ImageSliderModal = ({ images, isOpen, onClose, startIndex = 0 }) => {
  if (!isOpen || !images || images.length === 0) return null;

  // Transform images to the format expected by react-image-gallery
  const galleryImages = images.map((image, index) => ({
    original: image,
    thumbnail: image,
    originalAlt: `Shipment Image ${index + 1}`,
    thumbnailAlt: `Thumbnail ${index + 1}`,
  }));

  const handleImageError = (e) => {
    e.target.onerror = null; // prevent infinite loop in case fallback fails
    e.target.src =
      "https://i.pinimg.com/736x/09/32/e1/0932e1730b457bd13e508897790ef081.jpg";
  };

  return (
    <div className="fixed overflow-auto inset-0 !m-0 bg-black/60 bg-opacity-90 flex items-start justify-center z-50 p-4">
      <div className="relative w-full max-w-6xl ">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 z-10 bg-black bg-opacity-50 text-white rounded-full w-10 h-10 flex items-center justify-center hover:bg-opacity-70 transition-all"
        >
          ✕
        </button>
        
        {/* Image Gallery */}
        <div className="  overflow-hidden bg-black rounded-3xl ">
          <ImageGallery
          onErrorImageURL='https://i.pinimg.com/736x/09/32/e1/0932e1730b457bd13e508897790ef081.jpg'
            items={galleryImages}
            startIndex={startIndex}
            showThumbnails={true}
            showFullscreenButton={true}
            showPlayButton={false}
            showBullets={true}
            autoPlay={false}
            slideInterval={3000}
            slideDuration={450}
            useBrowserFullscreen={false}
            renderCustomControls={() => null}
          />
        </div>
      </div>
    </div>
  );
};

export default ImageSliderModal;
