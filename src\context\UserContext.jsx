import { createContext, useContext, useState, useEffect } from 'react';

// Create the UserContext
const UserContext = createContext();

// Custom hook to use the UserContext
export const useUser = () => {
  const context = useContext(UserContext); 
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

// UserProvider component
export const UserProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load user from localStorage on component mount
  useEffect(() => {
    try {
      const savedUser = localStorage.getItem('currentUser'); // undefined if no user is logged in
      if (savedUser) { // undefined 
        setCurrentUser(JSON.parse(savedUser));
      }
    } catch (error) {
      console.error('Error loading user from localStorage:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Function to set current user and save to localStorage
  const setUser = (user) => {
    try {
      setCurrentUser(user);
      if (user) {
        localStorage.setItem('currentUser', JSON.stringify(user));
      } else {
        localStorage.removeItem('currentUser');
      }
    } catch (error) {
      console.error('Error saving user to localStorage:', error);
    }
  };


  // Function to clear current user
  const clearUser = () => {
    setUser(null);
  };

  // Function to update user data
  const updateUser = (updatedData) => {
    if (currentUser) {
      const updatedUser = { ...currentUser, ...updatedData };
      setUser(updatedUser);
    }
  };



  // Check if user is logged in
  const isLoggedIn = () => {
    return currentUser !== null;  
  };

  // Check user role
  const hasRole = (role) => {
    return currentUser?.user.role === role;
  };


  // Check if user belongs to a specific branch
  const belongsToBranch = (branchName) => {
    return currentUser?.branch === branchName;
  };

  const value = {
    currentUser,
    setUser,
    clearUser,
    updateUser,
    isLoggedIn,
    hasRole,
    belongsToBranch,
    isLoading
  };

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
};

export default UserContext;
