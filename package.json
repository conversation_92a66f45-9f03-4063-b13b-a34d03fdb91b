{"name": "bekary-tacker", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"antd": "^5.27.3", "autoprefixer": "^10.4.21", "axios": "^1.13.0", "classnames": "^2.5.1", "lucide-react": "^0.540.0", "postcss": "^8.5.6", "react": "^19.1.1", "react-dom": "^19.1.1", "react-image-gallery": "^1.4.0", "react-router-dom": "^7.8.2", "react-toastify": "^11.0.5", "sweetalert2": "^11.23.0", "tailwindcss": "^3.3.5"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.1.2"}}