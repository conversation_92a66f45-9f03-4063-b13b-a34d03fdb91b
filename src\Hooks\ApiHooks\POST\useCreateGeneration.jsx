import { useState } from 'react';
import { createGeneration } from '../../../api/apiService';

export const useCreateGeneration = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [data, setData] = useState(null);

  const createNewGeneration = async (generationData) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await createGeneration(generationData);
      setData(response.data);
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.message || err.message || 'حدث خطأ أثناء إنشاء الدفعة';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return {
    createGeneration: createNewGeneration,
    loading,
    error,
    data,
    reset: () => {
      setError(null);
      setData(null);
    }
  };
};
