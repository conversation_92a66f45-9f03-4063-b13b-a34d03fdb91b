import React, { useState } from "react";
import Sidebar from "./SideBar";
import TopBar from "./TopBar";
import LoginPage from "../Pages/LoginPage";
import { useUser } from "../context/UserContext";
import { ToastContainer } from "react-toastify";

const DefaultLayout = ({ children }) => {
  const [currentPage, setCurrentPage] = useState("dashboard");
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const { currentUser, isLoggedIn } = useUser();

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const closeSidebar = () => {
    setIsSidebarOpen(false);
  };

  const MainLayout = () => (
    <div className="h-screen flex bg-gray-50">
      {/* Mobile Overlay */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={closeSidebar}
        />
      )}

      {/* Sidebar */}
      <Sidebar
        currentPage={currentPage}
        currentUser={currentUser}
        setCurrentPage={setCurrentPage}
        isOpen={isSidebarOpen}
        onClose={closeSidebar}
      />

      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <TopBar
          currentPage={currentPage}
          currentUser={currentUser}
          onToggleSidebar={toggleSidebar}
        />

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto p-4 lg:p-6 bg-white custom-scrollbar">
          {children}
        </main>
      </div>
    </div>
  );

  // Main App Component
  return (
    <div className="min-h-screen bg-gray-50">
      {!isLoggedIn() ? (
        <LoginPage setCurrentPage={setCurrentPage} />
      ) : (
        <MainLayout />
      )}

      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick={false}
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
    </div>
  );
};

export default DefaultLayout;
