import { CheckCircle, X } from "lucide-react";

const ConfirmationSuccessModal = ({ show, onClose, shipment, type }) => {
  if (!show) return null;

  return (
    <div className="fixed overflow-auto !m-0 inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-md w-full">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-full ${
              type === "confirm" ? "bg-green-100" : "bg-red-100"
            }`}>
              <CheckCircle className={`w-5 h-5 ${
                type === "confirm" ? "text-green-600" : "text-red-600"
              }`} />
            </div>
            <h2 className="text-xl font-bold text-gray-900">
              {type === "confirm" ? "Shipment Confirmed" : "Shipment Rejected"}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-4">
          <div className={`border-l-4 p-4 rounded ${
            type === "confirm" 
              ? "bg-green-50 border-green-500" 
              : "bg-red-50 border-red-500"
          }`}>
            <p className="text-sm text-gray-700">
              {type === "confirm" 
                ? `Shipment #${shipment?.id} has been successfully confirmed and is now ready for display.`
                : `Shipment #${shipment?.id} has been rejected and marked accordingly in the system.`
              }
            </p>
          </div>

          {/* Shipment Details */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h3 className="text-sm font-semibold text-gray-700 mb-2">Shipment Details</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Bakery Type:</span>
                <span className="font-medium text-gray-900">{shipment?.bakeryType}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Quantity:</span>
                <span className="font-medium text-gray-900">{shipment?.quantity}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Region:</span>
                <span className="font-medium text-gray-900">{shipment?.region}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Route:</span>
                <span className="font-medium text-gray-900">{shipment?.route}</span>
              </div>
              {type === "reject" && shipment?.rejectionDetails && (
                <>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Vehicle:</span>
                    <span className="font-medium text-gray-900">
                      {shipment.vehicleNumber}
                    </span>
                  </div>
                  <div className="pt-2 border-t border-gray-300">
                    <span className="text-gray-600">Reason:</span>
                    <p className="font-medium text-gray-900 mt-1">
                      {shipment.rejectionDetails.reason}
                    </p>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className={`w-full px-4 py-2.5 rounded-lg font-medium transition-colors ${
              type === "confirm"
                ? "bg-green-600 text-white hover:bg-green-700"
                : "bg-red-600 text-white hover:bg-red-700"
            }`}
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationSuccessModal;