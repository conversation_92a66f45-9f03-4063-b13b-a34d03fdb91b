import React, { useMemo } from "react";
import DataTable, {
  getColumnSearchProps,
  getColumnFiltersProps,
  getColumnDateProps,
} from "../../../utils/DataTable";
import { Popconfirm } from "antd";

const FactoriesTable = ({
  factories = [],
  onEdit,
  onDelete,
  onShowDetails,
}) => {
  const columns = useMemo(
    () => [
      {
        title: "Production Name",
        dataIndex: "name",
        key: "name",
        ...getColumnSearchProps("name", () => {}),
      },
      {
        title: "Region Name",
        dataIndex: "region",
        key: "region",
        ...getColumnFiltersProps("regionName"),
        render: (value) => {
          return <div>{value?.name}</div>;
        },
      },
      {
        title: "Type",
        dataIndex: "type",
        key: "type",
        ...getColumnFiltersProps("type"),
        render: (value) => {
          return <div>{value}</div>;
        },
      },
      // {
      //   title: "Created At",
      //   dataIndex: "createdAt",
      //   key: "createdAt",
      //   ...getColumnDateProps("createdAt"),
      //   render: (value, record) => {
      //     try {
      //       const d = new Date(value);
      //       if (Number.isNaN(d.getTime())) return "";
      //       return <div onClick={() => {}}> {d.toLocaleString()}</div>;
      //     } catch {
      //       return "";
      //     }
      //   },
      // },
      {
        title: "Actions",
        key: "actions",
        render: (_, record) => (
          <div className="flex items-center gap-2 not-target">
            <button
              className="px-3 py-1 text-sm rounded border border-gray-300 hover:bg-gray-50"
              onClick={() => onEdit && onEdit(record)}
            >
              Edit
            </button>
            <Popconfirm
              rootClassName="not-target"
              title="Are you sure you want to delete this factory?"
              onConfirm={() => onDelete && onDelete(record)}
              okText="Yes"
              cancelText="No"
              okButtonProps={{ className: "!bg-red-600 text-white" }}
            >
              <button className="px-3 py-1 text-sm rounded border border-red-300 text-red-600 hover:bg-red-50">
                Delete
              </button>
            </Popconfirm>

            <button
              className="px-3 py-1 text-sm rounded border border-orange-300 text-orange-600  hover:bg-orange-400 hover:text-white transition-all"
              onClick={() => onShowDetails && onShowDetails(record)}
            >
              Show Details
            </button>
          </div>
        ),
      },
    ],
    [onEdit, onDelete, onShowDetails]
  );

  const table = useMemo(
    () => ({
      header: columns,
      rows: factories,
    }),
    [columns, factories]
  );

  return (
    <DataTable
      table={table}
      rowKey="id"
      rowClassName={() =>
        "hover:bg-orange-100 hover:scale-[1.01] transition-all  cursor-pointer"
      }
      onRow={(record) => {
        return {
          onClick: (e) => {
            if (e.target.closest(".not-target")) {
              return;
            }
            onShowDetails && onShowDetails(record);
          },
        };
      }}
    />
  );
};

export default FactoriesTable;
