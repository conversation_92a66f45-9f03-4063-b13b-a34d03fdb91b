import { Camera, X, Upload, CheckCircle, AlertCircle, Plus, Image as ImageIcon } from "lucide-react";
import React, { useState, useRef } from "react";

const MultipleImageUpload = ({ 
  onImagesUpload, 
  maxSize = 5 * 1024 * 1024, // 5MB default
  acceptedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  label = "Product Images",
  required = false,
  className = "",
  placeholder = "Click to upload images",
  maxImages = 10,
  showPreview = true
}) => {
  const [images, setImages] = useState([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState("");
  const fileInputRef = useRef(null);

  // Validate file
  const validateFile = (file) => {
    // Check file type
    if (!acceptedTypes.includes(file.type)) {
      return `Invalid file type. Please upload: ${acceptedTypes.join(", ")}`;
    }

    // Check file size
    if (file.size > maxSize) {
      return `File too large. Maximum size is ${(maxSize / (1024 * 1024)).toFixed(1)}MB`;
    }

    return null;
  };

  // Handle file selection
  const handleFileSelect = async (files) => {
    setError("");
    setIsUploading(true);

    const fileArray = Array.from(files);
    const validFiles = [];
    const errors = [];

    // Check if adding these files would exceed maxImages
    if (images.length + fileArray.length > maxImages) {
      setError(`Maximum ${maxImages} images allowed. You can add ${maxImages - images.length} more.`);
      setIsUploading(false);
      return;
    }

    // Validate each file
    for (const file of fileArray) {
      const validationError = validateFile(file);
      if (validationError) {
        errors.push(`${file.name}: ${validationError}`);
      } else {
        validFiles.push(file);
      }
    }

    if (errors.length > 0) {
      setError(errors.join('; '));
      setIsUploading(false);
      return;
    }

    try {
      // Process valid files
      const newImages = [];
      for (const file of validFiles) {
        const previewUrl = URL.createObjectURL(file);
        const imageData = {
          id: Date.now() + Math.random(),
          file,
          preview: previewUrl,
          name: file.name,
          size: file.size,
          timestamp: new Date().toISOString()
        };
        newImages.push(imageData);
      }

      const updatedImages = [...images, ...newImages];
      setImages(updatedImages);

      // Call parent callback
      if (onImagesUpload) {
        onImagesUpload(updatedImages);
      }

      setIsUploading(false);
    } catch (err) {
      setError("Failed to process images");
      setIsUploading(false);
    }
  };

  // Handle input change
  const handleInputChange = (e) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files);
    }
  };

  // Handle drag events
  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      handleFileSelect(files);
    }
  };

  // Remove image
  const removeImage = (imageId) => {
    const imageToRemove = images.find(img => img.id === imageId);
    if (imageToRemove) {
      URL.revokeObjectURL(imageToRemove.preview);
    }
    
    const updatedImages = images.filter(img => img.id !== imageId);
    setImages(updatedImages);
    setError("");
    
    if (onImagesUpload) {
      onImagesUpload(updatedImages);
    }
  };

  // Clear all images
  const clearAllImages = () => {
    images.forEach(img => URL.revokeObjectURL(img.preview));
    setImages([]);
    setError("");
    
    if (onImagesUpload) {
      onImagesUpload([]);
    }
  };

  // Open file dialog
  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`w-full ${className}`}>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
        <span className="text-gray-500 text-xs ml-2">
          ({images.length}/{maxImages} images)
        </span>
      </label>

      {/* Upload Area */}
      <div
        className={`
          border-2 border-dashed rounded-lg p-6 text-center transition-all duration-200 cursor-pointer
          ${isDragOver 
            ? 'border-orange-400 bg-orange-50' 
            : error 
              ? 'border-red-300 bg-red-50' 
              : 'border-gray-300 hover:border-orange-400 hover:bg-gray-50'
          }
          ${isUploading ? 'pointer-events-none opacity-50' : ''}
          ${images.length >= maxImages ? 'opacity-50 pointer-events-none' : ''}
        `}
        onClick={openFileDialog}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {isUploading ? (
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600 mb-2"></div>
            <p className="text-sm text-gray-600">Uploading...</p>
          </div>
        ) : images.length >= maxImages ? (
          <div className="flex flex-col items-center">
            <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-3" />
            <p className="text-sm text-gray-600 mb-2">Maximum images reached</p>
            <p className="text-xs text-gray-400">Remove some images to add more</p>
          </div>
        ) : (
          <>
            <Camera className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <p className="text-sm text-gray-600 mb-2">{placeholder}</p>
            <p className="text-xs text-gray-400">
              Drag and drop or click to browse
            </p>
            <p className="text-xs text-gray-400 mt-1">
              Max size: {(maxSize / (1024 * 1024)).toFixed(1)}MB per image
            </p>
            <p className="text-xs text-gray-400">
              Up to {maxImages} images
            </p>
          </>
        )}

        <input
          ref={fileInputRef}
          type="file"
          accept={acceptedTypes.join(",")}
          onChange={handleInputChange}
          multiple
          className="hidden"
        />
      </div>

      {/* Image Previews */}
      {showPreview && images.length > 0 && (
        <div className="mt-4">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-sm font-medium text-gray-700">
              Uploaded Images ({images.length})
            </h4>
            <button
              onClick={clearAllImages}
              className="text-xs text-red-600 hover:text-red-700 flex items-center gap-1"
            >
              <X className="w-3 h-3" />
              Clear All
            </button>
          </div>
          
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
            {images.map((image) => (
              <div key={image.id} className="relative group">
                <div className="relative rounded-lg overflow-hidden bg-gray-100">
                  <img
                    src={image.preview}
                    alt={image.name}
                    className="w-full h-24 object-cover"
                  />
                  
                  {/* Overlay with actions */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                    <div className="flex gap-1">
                      <button
                        onClick={() => openFileDialog()}
                        className="bg-white text-gray-700 p-1.5 rounded-full shadow-md hover:bg-gray-100 transition-colors"
                        title="Replace image"
                      >
                        <Upload className="w-3 h-3" />
                      </button>
                      <button
                        onClick={() => removeImage(image.id)}
                        className="bg-red-600 text-white p-1.5 rounded-full shadow-md hover:bg-red-700 transition-colors"
                        title="Remove image"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </div>
                  </div>

                  {/* Success indicator */}
                  <div className="absolute top-1 right-1 bg-green-600 text-white p-0.5 rounded-full">
                    <CheckCircle className="w-3 h-3" />
                  </div>
                </div>

                {/* Image info */}
                <div className="mt-1 text-xs text-gray-500 truncate">
                  <div className="truncate" title={image.name}>
                    {image.name}
                  </div>
                  <div className="text-gray-400">
                    {formatFileSize(image.size)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="mt-2 flex items-center text-red-600 text-sm">
          <AlertCircle className="w-4 h-4 mr-1" />
          {error}
        </div>
      )}

      {/* File type info */}
      <p className="text-xs text-gray-400 mt-1">
        Supported formats: {acceptedTypes.map(type => type.split('/')[1].toUpperCase()).join(', ')}
      </p>
    </div>
  );
};

// Example usage component
const ExampleUsage = () => {
  const [uploadedImages, setUploadedImages] = useState([]);

  const handleImagesUpload = (images) => {
    console.log('Images uploaded:', images);
    setUploadedImages(images);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-xl font-bold text-gray-900 mb-4">Upload Multiple Product Images</h2>
      
      {/* Multiple image upload */}
      <div className="mb-6">
        <MultipleImageUpload
          onImagesUpload={handleImagesUpload}
          label="Product Photos"
          required
          placeholder="Upload multiple product images"
          maxImages={8}
        />
      </div>

      {/* Display uploaded images info */}
      {uploadedImages.length > 0 && (
        <div className="mt-6">
          <h3 className="font-medium text-gray-900 mb-2">Upload Summary:</h3>
          <div className="bg-gray-50 p-4 rounded-lg">
            <p className="text-sm text-gray-600 mb-2">
              Total images: {uploadedImages.length}
            </p>
            <p className="text-sm text-gray-600 mb-2">
              Total size: {formatFileSize(uploadedImages.reduce((total, img) => total + img.size, 0))}
            </p>
            <div className="space-y-1">
              {uploadedImages.map((img, index) => (
                <div key={img.id} className="flex items-center text-sm text-gray-600">
                  <ImageIcon className="w-4 h-4 text-blue-600 mr-2" />
                  <span className="flex-1 truncate">{img.name}</span>
                  <span className="text-xs text-gray-400 ml-2">
                    {formatFileSize(img.size)}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Action buttons */}
      <div className="mt-6 flex gap-3">
        <button className="flex-1 bg-orange-600 text-white py-2 px-4 rounded-lg hover:bg-orange-700 transition-colors">
          Save Images
        </button>
        <button 
          onClick={() => setUploadedImages([])}
          className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Clear All
        </button>
      </div>
    </div>
  );
};

export default MultipleImageUpload;
export { ExampleUsage };
