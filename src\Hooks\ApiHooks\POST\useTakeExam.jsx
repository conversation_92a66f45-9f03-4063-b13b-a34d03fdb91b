import { useState, useCallback } from 'react';
import { takeExam } from '../../../api/apiService';

export const useTakeExam = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const submitExamResults = useCallback(async (examData) => {
    setLoading(true);
    setError(null);

    try {
      const response = await takeExam(examData);
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.message || err.message || 'حدث خطأ أثناء تسجيل نتائج الامتحان';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const reset = useCallback(() => setError(null), []);

  return {
    submitExamResults,
    loading,
    error,
    reset
  };
};
