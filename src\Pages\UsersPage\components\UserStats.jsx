import React from 'react';
import { Users, UserCheck, Clock, Building, Truck, Factory } from 'lucide-react';

const UserStats = ({ users }) => {
  // Calculate statistics
  const totalUsers = users.length;
  const driverCount = users.filter(user => user.role === 'driver').length;
  const branchCount = users.filter(user => user.role === 'branch').length;
  const factoryCount = users.filter(user => user.role === 'factory').length;
  
  // Calculate role percentages
  const driverPercentage = totalUsers > 0 ? Math.round((driverCount / totalUsers) * 100) : 0;
  const branchPercentage = totalUsers > 0 ? Math.round((branchCount / totalUsers) * 100) : 0;
  const factoryPercentage = totalUsers > 0 ? Math.round((factoryCount / totalUsers) * 100) : 0;

  // Mock recent activity (in a real app, this would come from actual data)
  const recentActivity = users.filter(user => user.lastActivity === 'Just now').length;
  const activeToday = Math.floor(totalUsers * 0.8); // Mock 80% active today

  const stats = [
    {
      title: 'Total Users',
      value: totalUsers,
      icon: Users,
      color: 'bg-blue-500',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-600',
      change: '+12%',
      changeType: 'positive'
    },
    {
      title: 'Active Today',
      value: activeToday,
      icon: UserCheck,
      color: 'bg-green-500',
      bgColor: 'bg-green-50',
      textColor: 'text-green-600',
      change: '+5%',
      changeType: 'positive'
    },
    {
      title: 'Recent Activity',
      value: recentActivity,
      icon: Clock,
      color: 'bg-orange-500',
      bgColor: 'bg-orange-50',
      textColor: 'text-orange-600',
      change: '+2',
      changeType: 'positive'
    }
  ];

  const roleStats = [
    {
      title: 'Drivers',
      count: driverCount,
      percentage: driverPercentage,
      icon: Truck,
      color: 'bg-blue-100',
      textColor: 'text-blue-800',
      borderColor: 'border-blue-200'
    },
    {
      title: 'Branch Managers',
      count: branchCount,
      percentage: branchPercentage,
      icon: Building,
      color: 'bg-purple-100',
      textColor: 'text-purple-800',
      borderColor: 'border-purple-200'
    },
    {
      title: 'Factory Workers',
      count: factoryCount,
      percentage: factoryPercentage,
      icon: Factory,
      color: 'bg-green-100',
      textColor: 'text-green-800',
      borderColor: 'border-green-200'
    }
  ];

  return (
    <div className="space-y-6">
     

      {/* Role Distribution */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Role Distribution</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {roleStats.map((role, index) => {
            const IconComponent = role.icon;
            return (
              <div key={index} className={`${role.color} ${role.borderColor} border rounded-lg p-4`}>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <IconComponent className={`w-5 h-5 ${role.textColor}`} />
                    <span className={`font-medium ${role.textColor}`}>{role.title}</span>
                  </div>
                  <span className={`text-lg font-bold ${role.textColor}`}>{role.count}</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="w-full bg-white bg-opacity-50 rounded-full h-2 mr-2">
                    <div 
                      className={`h-2 rounded-full ${role.textColor.replace('text-', 'bg-').replace('-800', '-400')}`}
                      style={{ width: `${role.percentage}%` }}
                    ></div>
                  </div>
                  <span className={`text-sm font-medium ${role.textColor}`}>{role.percentage}%</span>
                </div>
              </div>
            );
          })}
        </div>
      </div>

    
    </div>
  );
};

export default UserStats;
