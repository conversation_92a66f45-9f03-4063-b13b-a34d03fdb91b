import React  from "react";
import { BarChart3, Clock, User } from "lucide-react";
import initialShipments from "../../data/shipments";

import branches from "../../data/branhces";

const Reports = () => {
  const [shipments, setShipments] = React.useState(initialShipments);

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">Reports</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex items-center mb-4">
            <BarChart3 className="w-6 h-6 text-blue-600 mr-3" />
            <h3 className="font-medium text-gray-900">Shipments Report</h3>
          </div>
          <p className="text-gray-600 text-sm mb-4">
            Comprehensive report on all shipments and statuses
          </p>
          <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
            Generate Report
          </button>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex items-center mb-4">
            <Clock className="w-6 h-6 text-yellow-600 mr-3" />
            <h3 className="font-medium text-gray-900">Performance Report</h3>
          </div>
          <p className="text-gray-600 text-sm mb-4">
            Analysis of transit times and operational efficiency
          </p>
          <button className="w-full bg-yellow-600 text-white py-2 px-4 rounded-lg hover:bg-yellow-700 transition-colors">
            Generate Report
          </button>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex items-center mb-4">
            <User className="w-6 h-6 text-green-600 mr-3" />
            <h3 className="font-medium text-gray-900">Users Report</h3>
          </div>
          <p className="text-gray-600 text-sm mb-4">
            User activity and performance in the system
          </p>
          <button className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
            Generate Report
          </button>
        </div>
      </div>

      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900">Quick Statistics</h2>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center">
              <p className="text-3xl font-bold text-blue-600">
                {shipments.length}
              </p>
              <p className="text-gray-600 mt-1">Total Shipments</p>
            </div>
            <div className="text-center">
              <p className="text-3xl font-bold text-green-600">
                {shipments.filter((s) => s.status === "Delivered").length}
              </p>
              <p className="text-gray-600 mt-1">Delivered</p>
            </div>
            <div className="text-center">
              <p className="text-3xl font-bold text-yellow-600">
                {shipments.filter((s) => s.status === "In Transit").length}
              </p>
              <p className="text-gray-600 mt-1">In Transit</p>
            </div>
            <div className="text-center">
              <p className="text-3xl font-bold text-purple-600">
                {branches.length}
              </p>
              <p className="text-gray-600 mt-1">Number of Branches</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Reports;
