import { useState, useEffect } from "react";
import { X, Store, Upload, CheckCircle, AlertCircle } from "lucide-react";
import MultipleImageUpload from "../../utils/MultipleImageUpload";

const MarkInStoreDisplayModal = ({
  showModal,
  setShowModal,
  shipment,
  onMarkInStoreDisplay,
  setSelectedShipment,
}) => {
  const [images, setImages] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");

  // Reset form when modal opens/closes
  useEffect(() => {
    if (showModal) {
      setImages([]);
      setError("");
      setIsSubmitting(false);
    }
  }, [showModal]);

  // Close modal on ESC key press
  useEffect(() => {
    if (!showModal) return;
    const handleKeyDown = (e) => {
      if (e.key === "Escape") {
        setShowModal(false);
        setSelectedShipment(null);
      }
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [showModal, setShowModal]);

  const handleImagesUpload = (uploadedImages) => {
    setImages(uploadedImages);
    setError("");
  };

  const handleSubmit = async () => {
    if (images.length === 0) {
      setError("Please upload at least one image of the display setup");
      return;
    }

    setIsSubmitting(true);
    setError("");

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Call parent callback with shipment data and images
      if (onMarkInStoreDisplay) {
        onMarkInStoreDisplay(shipment.id, images);
      }

      setShowModal(false);
    } catch (err) {
      setError("Failed to submit display status. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!showModal || !shipment) return null;

  return (
    <div
      onClick={() => {
        setShowModal(false);
        setSelectedShipment(null);
        
      }}
      className="fixed !m-0 inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    >
      <div
        onClick={(e) => e.stopPropagation()}
        className="bg-white rounded-xl shadow-xl w-full max-w-2xl max-h-[95vh] overflow-y-auto"
      >
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold text-gray-900">
                Mark as In Store Display
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                Upload images of the display setup
              </p>
            </div>
            <button
              onClick={() => {
                setShowModal(false);
                setSelectedShipment(null);
              }}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          </div>
        </div>

        {/* Shipment Details */}
        <div className="p-6 border-b border-gray-200 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-medium text-gray-900">
                {shipment.bakeryType}
              </h3>
              <p className="text-sm text-gray-600">ID: #{shipment.id}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">
                <span className="font-medium">Quantity:</span>{" "}
                {shipment.quantity}
              </p>
              <p className="text-sm text-gray-600">
                <span className="font-medium">Branch:</span> {shipment.branch}
              </p>
            </div>
          </div>
        </div>

        {/* Image Upload Section */}
        <div className="p-6">
          <MultipleImageUpload
            onImagesUpload={handleImagesUpload}
            label="Display Setup Images"
            placeholder="Upload images of the display setup"
            maxImages={10}
            required={true}
            className="mb-4"
          />

          {/* Error Message */}
          {error && (
            <div className="mt-4 flex items-center text-red-600 text-sm">
              <AlertCircle className="w-4 h-4 mr-2" />
              {error}
            </div>
          )}

          {/* Upload Guidelines */}
          <div className="mt-4 p-4 bg-purple-50 rounded-lg">
            <h4 className="text-sm font-medium text-purple-900 mb-2">
              Display Setup Guidelines:
            </h4>
            <ul className="text-xs text-purple-800 space-y-1">
              <li>• Take photos of the product display arrangement</li>
              <li>• Show the overall display area and positioning</li>
              <li>• Include close-up shots of individual items</li>
              <li>• Ensure good lighting to showcase the products</li>
              <li>• Capture any signage or promotional materials</li>
            </ul>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 flex gap-3">
          <button
            onClick={() => setShowModal(false)}
            disabled={isSubmitting}
            className="flex-1 px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={isSubmitting || images.length === 0}
            className="flex-1 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Submitting...
              </>
            ) : (
              <>
                <Store className="w-4 h-4" />
                Mark as Display
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default MarkInStoreDisplayModal;
