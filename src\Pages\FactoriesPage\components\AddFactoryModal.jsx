import React, { useEffect, useMemo, useState } from "react";
import { Select } from "antd";
import { createProduction, getAllRegions } from "../../../api/apiService";
import { toast } from "react-toastify";

const AddFactoryModal = ({ onClose, onCreate }) => {
  const [name, setName] = useState("");
  const [regionId, setRegionId] = useState(null);
  const [code, setCode] = useState("");
  const [type, setType] = useState();
  const [regionsOptions, setRegionsOptions] = useState([]);

  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === "Escape") {
        onClose && onClose();
      }
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [onClose]);

  useEffect(() => {
    (async () => {
      try {
        const data = await getAllRegions();
        setRegionsOptions(Array.isArray(data.data) ? data.data : []);
      } catch (e) {
        console.error(e);
        setRegionsOptions([]);
      }
    })();
  }, []);

  const handleSubmit = async () => {
    if (!name.trim() || !regionId || !code || !type) return;
    try {
      const payload = {
        region_id: Number(regionId),
        name: name.trim(),
        code: Number(code),
        type: type,
      };
      await createProduction(payload).then((e) => {
        console.log(e);
        if (e.status == 201) {
          toast.success("Production created successfully");
          onCreate && onCreate(payload);
          onClose && onClose();
        }else{
          console.log(e.response)
          // toast.error();
        }
      });
    } catch (error) {
      console.error(error?.response?.data?.messages?.code[0]);
      toast.error(error?.response?.data?.messages?.code[0]);
    }
  };

  return (
    <div
      onClick={() => onClose && onClose()}
      className="fixed !m-0 overflow-auto inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 p-4"
    >
      <div
        onClick={(e) => e.stopPropagation()}
        className="bg-white rounded-xl shadow-xl w-full max-w-lg"
      >
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900">
            Add New Production
          </h2>
          <p className="text-sm text-gray-600 mt-1">
            Create a production and assign its region.
          </p>
        </div>

        <div className="p-6 space-y-5">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              production Name
            </label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              placeholder="e.g. Central Baking Plant"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              production Code
            </label>
            <input
              type="number"
              value={code}
              onChange={(e) => setCode(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              placeholder="e.g. 123"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Region
            </label>
            <Select
              size="large"
              value={regionId}
              onChange={(value) => setRegionId(value)}
              className="w-full"
              placeholder="Choose region"
              options={regionsOptions.map((r) => ({
                label: r.name,
                value: r.id,
              }))}
              allowClear
              showSearch
              optionFilterProp="label"
              dropdownStyle={{ zIndex: 9999 }}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              production Type
            </label>
            <Select
              size="large"
              value={type}
              onChange={(value) => setType(value)}
              className="w-full"
              placeholder="Select type (CFL or CML)"
              options={[
                { label: "CFL", value: "CFL" },
                { label: "CML", value: "CML" },
              ]}
              allowClear
              dropdownStyle={{ zIndex: 9999 }}
            />
          </div>
        </div>

        <div className="p-6 border-t border-gray-200 flex gap-3">
          <button
            onClick={() => onClose && onClose()}
            className="flex-1 px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            className="flex-1 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors disabled:opacity-50"
            disabled={!name.trim() || !regionId || !code || !type}
          >
            Create Factory
          </button>
        </div>
      </div>
    </div>
  );
};

export default AddFactoryModal;
