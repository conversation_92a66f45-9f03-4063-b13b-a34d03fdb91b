import React, { useMemo, useState } from "react";
import { Plus } from "lucide-react";
import { useUser } from "../../context/UserContext";
import RejionRoutesTable from "./components/RejionRoutesTable";
import initialRejions from "../../data/regions";
import stores from "../../data/stores";
import users from "../../data/users";
import AssignModal from "./components/AssignModa";
import UpdateRejionRouteModal from "./components/UpdateRejonRouteModal";
import RejionRoutesStats from "./components/RejionRoutesStats";
import { useParams, useNavigate } from "react-router-dom";
import AddNewRejionRouteModal from "./components/AddNewRejsionRouteModal";
import DistributorsTable from "./components/DistributorsTable";
import AddNewDistributorModal from "./components/AddNewDistributorModal";
import EditDistributorModal from "./components/EditDistributorModal";
import QualityTestersTable from "./components/QualityTestersTable";
import AddNewQualityTesterModal from "./components/AddNewQualityTesterModal";
import EditQualityTesterModal from "./components/EditQualityTesterModal";
import data, { getFactoriesByRegion } from "../../data/allData";
import FactoriesTable from "../FactoriesPage/components/FactoriesTable";

const RejionRoutes = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { currentUser, setUser, clearUser, isLoggedIn, hasRole } = useUser();
  const [showAddShipment, setShowAddShipment] = useState(false);
  const [selectedShipment, setSelectedShipment] = useState(null);
  const [regionRoutes, setRejionRoutes] = useState(
    data.regions.find((item) => item.id == id)
  );
  const [assignOpen, setAssignOpen] = useState(false);
  const [selectedRejion, setSelectedRejion] = useState(null);
  const [updateOpen, setUpdateOpen] = useState(false);

  const distributors = useMemo(
    () => users.filter((u) => u.role === "driver"),
    []
  );
  const testers = useMemo(() => users.filter((u) => u.role === "quality"), []);

  const [addRejionModal, setAddRejionModal] = useState(false);
  const [addDistributorModal, setAddDistributorModal] = useState(false);
  const [addTesterModal, setAddTesterModal] = useState(false);
  const [regions, setRegions] = useState([]);
  const [distributorsState, setDistributorsState] = useState(distributors);
  const [testersState, setTestersState] = useState(testers);
  const [editDistributor, setEditDistributor] = useState(null);
  const [editTester, setEditTester] = useState(null);

  const [activeTab, setActiveTab] = useState("routes"); // routes | distributors | testers | factories

  const handleCreateRegion = (region) => {
    setRejionRoutes((prev) => ({
      ...regionRoutes,
      routes: [...prev.routes, region],
    }));
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">
          <span className="text-orange-600">{regionRoutes?.name}</span>{" "}
          Management
        </h1>
        {activeTab === "routes" && currentUser?.user?.role === "super_admin" && (
          <button
            onClick={() => setAddRejionModal(true)}
            className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add New Route
          </button>
        )}
        {activeTab === "distributors" && currentUser?.user?.role === "super_admin" && (
          <button
            onClick={() => setAddDistributorModal(true)}
            className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add New Distributor
          </button>
        )}
        {activeTab === "testers" && currentUser?.user?.role === "super_admin" && (
          <button
            onClick={() => setAddTesterModal(true)}
            className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add New Quality Tester
          </button>
        )}
      </div>

      {/* Tabs */}
      <div className="flex w-full border-b border-gray-200">
        <button
          onClick={() => setActiveTab("routes")}
          className={`px-4 py-2 -mb-px border-b-2 transition-colors ${
            activeTab === "routes"
              ? "border-orange-600 text-orange-700"
              : "border-transparent text-gray-600 hover:text-gray-800"
          }`}
        >
          Routes
        </button>
        <button
          onClick={() => setActiveTab("factories")}
          className={`px-4 py-2 -mb-px border-b-2 transition-colors ${
            activeTab === "factories"
              ? "border-orange-600 text-orange-700"
              : "border-transparent text-gray-600 hover:text-gray-800"
          }`}
        >
          Factories
        </button>
      </div>

      {activeTab === "routes" && (
        <>
          {/* Shipment Statistics */}
          <RejionRoutesStats regions={regionRoutes?.routes} />
          {console.log("routes", regionRoutes)}
          <RejionRoutesTable
            setSelectedShipment={setSelectedShipment}
            data={regionRoutes?.routes}
            setShipments={setRejionRoutes}
            onEditRejion={(region) => {
              setSelectedRejion(region);
              setUpdateOpen(true);
            }}
            onAssignRejion={(region) => {
              setSelectedRejion(region);
              setAssignOpen(true);
            }}
          />
        </>
      )}

      {activeTab === "factories" && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">
              Factories in {regionRoutes?.name}
            </h2>
            {console.log(
              "regionFactories",
              getFactoriesByRegion(data.regions, +id)
            )}
            <FactoriesTable
              factories={getFactoriesByRegion(data.regions, +id)}
              onShowDetails={(record) =>
                navigate(`/factory-details/${record.id}`, {
                  state: { data: record },
                })
              }
            />
          </div>
        </div>
      )}

      {/* {selectedShipment && (
        <ShipmentDetailsModal
          selectedShipment={selectedShipment}
          setSelectedShipment={setSelectedShipment}
        />
      )} */}

      {addRejionModal && (
        <AddNewRejionRouteModal
          onClose={() => setAddRejionModal(false)}
          onCreate={handleCreateRegion}
        />
      )}

      {addDistributorModal && (
        <AddNewDistributorModal
          onClose={() => setAddDistributorModal(false)}
          onCreate={(newDistributor) => {
            setDistributorsState((prev) => [newDistributor, ...prev]);
            setAddDistributorModal(false);
          }}
        />
      )}

      {addTesterModal && (
        <AddNewQualityTesterModal
          onClose={() => setAddTesterModal(false)}
          onCreate={(newTester) => {
            setTestersState((prev) => [newTester, ...prev]);
            setAddTesterModal(false);
          }}
        />
      )}

      {editDistributor && (
        <EditDistributorModal
          distributor={editDistributor}
          onClose={() => setEditDistributor(null)}
          onSave={(updated) => {
            setDistributorsState((prev) =>
              prev.map((d) => (d.id === updated.id ? updated : d))
            );
            setEditDistributor(null);
          }}
        />
      )}

      {editTester && (
        <EditQualityTesterModal
          tester={editTester}
          onClose={() => setEditTester(null)}
          onSave={(updated) => {
            setTestersState((prev) =>
              prev.map((t) => (t.id === updated.id ? updated : t))
            );
            setEditTester(null);
          }}
        />
      )}

      <AssignModal
        open={assignOpen}
        onClose={() => setAssignOpen(false)}
        region={selectedRejion}
        allStores={stores}
        onSave={(updated) => {
          return null;
          // setRejionRoutes((prev) =>
          //   prev.map((r) => (r.id === updated.id ? updated : r))
          // );
        }}
      />

      <UpdateRejionRouteModal
        open={updateOpen}
        onClose={() => setUpdateOpen(false)}
        region={selectedRejion}
        onSave={(updated) => {
          return null;
          // setRejionRoutes((prev) =>
          //   prev.map((r) => (r.id === updated.id ? updated : r))
          // );
        }}
      />
    </div>
  );
};

export default RejionRoutes;
