import { NextResponse } from 'next/server';

export async function POST(request) {
  try {
    const body = await request.json();
    const { user_id, name, generation_id } = body;

    // Validate required fields
    if (!user_id || !name || !generation_id) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Missing required fields: user_id, name, and generation_id are required' 
        },
        { status: 400 }
      );
    }

    // Validate data types
    if (typeof user_id !== 'number' || typeof generation_id !== 'number') {
      return NextResponse.json(
        { 
          success: false, 
          message: 'user_id and generation_id must be numbers' 
        },
        { status: 400 }
      );
    }

    if (typeof name !== 'string' || name.trim().length === 0) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'name must be a non-empty string' 
        },
        { status: 400 }
      );
    }

    // Here you would typically:
    // 1. Validate user_id exists in your database
    // 2. Validate generation_id exists in your database
    // 3. Insert the new collection into your database
    // 4. Return the created collection

    // For now, we'll simulate a successful creation
    const newCollection = {
      id: Date.now(), // This would be the actual ID from your database
      user_id,
      name: name.trim(),
      generation_id,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // TODO: Replace this with actual database insertion
    // Example with a hypothetical database:
    // const result = await db.collections.create({
    //   user_id,
    //   name: name.trim(),
    //   generation_id
    // });

    return NextResponse.json(
      {
        success: true,
        message: 'Collection created successfully',
        data: newCollection
      },
      { status: 201 }
    );

  } catch (error) {
    console.error('Error creating collection:', error);
    
    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
      },
      { status: 500 }
    );
  }
}

// Handle other HTTP methods
export async function GET() {
  return NextResponse.json(
    { message: 'Method not allowed. Use POST to create a collection.' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { message: 'Method not allowed. Use POST to create a collection.' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { message: 'Method not allowed. Use POST to create a collection.' },
    { status: 405 }
  );
}
