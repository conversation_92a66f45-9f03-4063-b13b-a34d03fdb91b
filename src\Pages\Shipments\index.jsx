import React, { useEffect, useState, useCallback } from "react";
import { Plus } from "lucide-react";
import { useUser } from "../../context/UserContext";
import FactoryShipList from "../FactoryDashboard/components/FactoryShipList";
import ShipmentDetailsModal from "../../Components/Modals/ShipmentDetailsModal";
import AddShipmentModal from "../../Components/Modals/AddNewShipment";
import ShipmentStats from "./components/ShipmentStats";
import { getShipmentsData } from "../../services/shipmentApi";

const ShipmentsPage = () => {
  const { currentUser, setUser, clearUser, isLoggedIn, hasRole } = useUser();
  const [showAddShipment, setShowAddShipment] = useState(false);
  const [selectedShipment, setSelectedShipment] = useState(null);
  const [shipments, setShipments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({ current_page: 1, per_page: 10, total: 0, last_page: 1 });

  const loadShipments = useCallback(async (page, perPage) => {
    try {
      setLoading(true);
      setError(null);
      const result = await getShipmentsData({ page, per_page: perPage });
      if (result.error) {
        setError(result.error);
        setShipments([]);
        setPagination((prev) => ({ ...prev, current_page: page, per_page: perPage }));
      } else {
        setShipments(result.shipments);
        const p = result.pagination || {};
        setPagination({
          current_page: p.current_page ?? page,
          per_page: p.per_page ?? perPage,
          total: p.total ?? (Array.isArray(result.shipments) ? result.shipments.length : 0),
          last_page: p.last_page ?? 1,
        });
      }
    } catch (err) {
      console.error('Error fetching shipments:', err);
      setError('Failed to fetch shipments. Please try again.');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadShipments(pagination.current_page, pagination.per_page);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handlePaginate = useCallback((page, pageSize) => {
    loadShipments(page, pageSize);
  }, [loadShipments]);

  useEffect(() => {
    console.log("shipmentssss", shipments);
  }, [shipments]);

  // Show loading state
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-900">
            Shipment Management
          </h1>
        </div>
        <div className="flex justify-center items-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading shipments...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-900">
            Shipment Management
          </h1>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-center">
            <div className="text-red-600 mr-3">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h3 className="text-red-800 font-medium">Error Loading Shipments</h3>
              <p className="text-red-600 mt-1">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="mt-3 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">
          Shipment Management
        </h1>
        {currentUser?.user?.role === "super_admin" 
          && (
          <button
            onClick={() => setShowAddShipment(true)}
            className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add Shipment
          </button>
        )}
      </div>

      {/* Shipment Statistics */}
      <ShipmentStats shipments={shipments} />

      <FactoryShipList
        setSelectedShipment={setSelectedShipment}
        shipments={shipments}
        setShipments={setShipments}
        serverPagination={pagination}
        onPaginate={handlePaginate}
      />

      {/* {selectedShipment && (
        <ShipmentDetailsModal
          selectedShipment={selectedShipment}
          setSelectedShipment={setSelectedShipment}
        />
      )} */}
      {showAddShipment && (
        <AddShipmentModal
          showAddShipment={showAddShipment}
          setShowAddShipment={setShowAddShipment}
          setShipments={setShipments}
          shipments={shipments}
        />
      )}
    </div>
  );
};

export default ShipmentsPage;
