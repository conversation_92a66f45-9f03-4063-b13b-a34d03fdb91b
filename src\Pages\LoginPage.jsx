import React, { useState } from "react";
import { Package, MessageSquare } from "lucide-react";
import { useUser } from "../context/UserContext";
import { toast } from "react-toastify";
import { useNavigate } from "react-router-dom";
import usePostData from "./../Hooks/ApiHooks/POST/usePostData";
import { login } from "../api/apiService";
export default function LoginPage({ setCurrentPage }) {
  const [loginMethod, setLoginMethod] = useState("email");
  const { currentUser, setUser, clearUser, isLoggedIn, hasRole } = useUser();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const handleLogin = async (e) => {
    e.preventDefault();
    
    const email = (e.target.email.value || "").trim();
    const password = (e.target.password.value || "").trim();

    // Basic validations
    if (!email || !password) {
      toast.error("Please enter both email and password.");
      return;
    }


    setLoading(true);
    const data = { email, password };

    try {
      const response = await login("/login", data);

      if (response) {
        toast.success("Login successful!");
        setTimeout(()=>{
          setUser(response.data.data);
          window.location.reload();

        } , 1000);
      }
    } catch (err) {
      // Optionally surface an error
      toast.error("Login failed. Please check your credentials and try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      dir="ltr"
      className="min-h-screen bg-gradient-to-br from-orange-400 to-red-500 flex items-center justify-center p-4"
    >
      <div className="bg-white rounded-2xl shadow-2xl p-8 w-full max-w-md">
        <div className="text-center mb-8">
          <div className="bg-orange-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4">
            <Package className="w-10 h-10 text-orange-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900">
            Bakery Tracking System
          </h1>
          <p className="text-gray-600 mt-2">Login to the system</p>
        </div>

        {/* <div className="mb-6">
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setLoginMethod("email")}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                loginMethod === "email"
                  ? "bg-white text-gray-900 shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              Email
            </button>
            <button
              onClick={() => setLoginMethod("phone")}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                loginMethod === "phone"
                  ? "bg-white text-gray-900 shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              Phone Number
            </button>
          </div>
        </div> */}

        {loginMethod === "email" ? (
          <form onSubmit={handleLogin} className="space-y-4">
            <div className="flex flex-col space-y-2">
              <label htmlFor="">Email</label>
              <input
                type="email"
                placeholder="Email"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                name="email"
              />
            </div>
            <div className="flex flex-col space-y-2">
              <label htmlFor="">Password</label>

              <input
                type="password"
                placeholder="Password"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                name="password"
              />
            </div>
            <button
              disabled={loading}
              type="submit"
              onClick={() => setCurrentPage("dashboard")}
              className="w-full bg-orange-600 text-white py-3 px-4 rounded-lg hover:bg-orange-700 transition-colors font-medium"
            >
              {loading ? "Loading..." : "Login"}
            </button>
          </form>
        ) : (
          <form className="space-y-4">
            <input
              type="tel"
              placeholder="Phone Number"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 "
            />
            <button
              type="button"
              className="w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors font-medium flex items-center justify-center gap-2"
            >
              Send verification code via WhatsApp
              <MessageSquare className="w-4 h-4" />
            </button>
            <input
              type="text"
              placeholder="Verification Code"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 "
            />
            <button
              type="submit"
              onClick={() => setCurrentPage("dashboard")}
              className="w-full bg-orange-600 text-white py-3 px-4 rounded-lg hover:bg-orange-700 transition-colors font-medium"
            >
              Confirm Login
            </button>
          </form>
        )}

        {/* <div className="mt-8 text-center">
          <p className="text-sm text-gray-500">
            For testing, choose user role:
          </p>
          <div className="flex flex-wrap gap-2 mt-2 justify-center">
            {[
              {
                role: "super_admin",
                name: "Super Admin",
                path: "/super-admin-dashboard",
              },
              {
                role: "distributor",
                name: "Distributor",
                path: "/distributor-dashboard",
              },
              { role: "quality", name: "Quality", path: "/quality-dashboard" },
              { role: "store", name: "Store", path: "/store-dashboard" },
            ].map((user) => (
              <button
                key={user.role}
                onClick={() => {
                  navigate(user.path);
                  setUser({ role: user.role, name: user.name });
                  setCurrentPage("dashboard");
                }}
                className="px-3 py-1 bg-gray-200 text-gray-700 rounded-full text-xs hover:bg-gray-300 transition-colors"
              >
                {user.name}
              </button>
            ))}
          </div>
        </div> */}
      </div>
    </div>
  );
}
