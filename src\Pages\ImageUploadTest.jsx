import React, { useState } from 'react';
import UploadImage from '../utils/UploadImage';
import MultipleImageUpload from '../utils/MultipleImageUpload';

const ImageUploadTest = () => {
  const [singleImage, setSingleImage] = useState(null);
  const [multipleImages, setMultipleImages] = useState([]);
  const [uploadedImages, setUploadedImages] = useState([]);

  const handleSingleImageUpload = (file, previewUrl) => {
    console.log('Single image uploaded:', file);
    setSingleImage({ file, preview: previewUrl });
  };

  const handleMultipleImagesUpload = (images) => {
    console.log('Multiple images uploaded:', images);
    setMultipleImages(images);
  };

  const handleUploadImages = (images) => {
    console.log('Upload images:', images);
    setUploadedImages(images);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8 text-center">
          Image Upload Test Page
        </h1>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Single Image Upload */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Single Image Upload
            </h2>
            <UploadImage
              onImageUpload={handleSingleImageUpload}
              label="Single Product Image"
              placeholder="Click to upload single image"
              required
            />
            
            {singleImage && (
              <div className="mt-4 p-4 bg-green-50 rounded-lg">
                <h3 className="font-medium text-green-800 mb-2">Uploaded Image:</h3>
                <div className="text-sm text-green-700">
                  <p><strong>Name:</strong> {singleImage.file.name}</p>
                  <p><strong>Size:</strong> {(singleImage.file.size / 1024).toFixed(2)} KB</p>
                  <p><strong>Type:</strong> {singleImage.file.type}</p>
                </div>
              </div>
            )}
          </div>

          {/* Multiple Image Upload */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Multiple Image Upload
            </h2>
            <MultipleImageUpload
              onImagesUpload={handleMultipleImagesUpload}
              label="Multiple Product Images"
              placeholder="Click to upload multiple images"
              maxImages={8}
            />
            
            {multipleImages.length > 0 && (
              <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                <h3 className="font-medium text-blue-800 mb-2">
                  Uploaded Images ({multipleImages.length}):
                </h3>
                <div className="space-y-2">
                  {multipleImages.map((img, index) => (
                    <div key={img.id} className="text-sm text-blue-700">
                      <p><strong>{index + 1}.</strong> {img.name} ({(img.size / 1024).toFixed(2)} KB)</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Enhanced UploadImage with Multiple Mode */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Enhanced UploadImage (Multiple Mode)
            </h2>
            <UploadImage
              onImagesUpload={handleUploadImages}
              label="Enhanced Multiple Upload"
              placeholder="Click to upload multiple images"
              multiple={true}
              maxImages={5}
            />
            
            {uploadedImages.length > 0 && (
              <div className="mt-4 p-4 bg-purple-50 rounded-lg">
                <h3 className="font-medium text-purple-800 mb-2">
                  Uploaded Images ({uploadedImages.length}):
                </h3>
                <div className="space-y-2">
                  {uploadedImages.map((img, index) => (
                    <div key={img.id} className="text-sm text-purple-700">
                      <p><strong>{index + 1}.</strong> {img.name} ({(img.size / 1024).toFixed(2)} KB)</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Upload Summary */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Upload Summary
            </h2>
            <div className="space-y-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <h3 className="font-medium text-gray-800 mb-2">Single Image:</h3>
                <p className="text-sm text-gray-600">
                  {singleImage ? `✅ ${singleImage.file.name}` : '❌ No image uploaded'}
                </p>
              </div>
              
              <div className="p-4 bg-gray-50 rounded-lg">
                <h3 className="font-medium text-gray-800 mb-2">Multiple Images:</h3>
                <p className="text-sm text-gray-600">
                  {multipleImages.length > 0 
                    ? `✅ ${multipleImages.length} images uploaded` 
                    : '❌ No images uploaded'
                  }
                </p>
              </div>
              
              <div className="p-4 bg-gray-50 rounded-lg">
                <h3 className="font-medium text-gray-800 mb-2">Enhanced Upload:</h3>
                <p className="text-sm text-gray-600">
                  {uploadedImages.length > 0 
                    ? `✅ ${uploadedImages.length} images uploaded` 
                    : '❌ No images uploaded'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Test Instructions */}
        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-yellow-800 mb-3">
            Test Instructions
          </h3>
          <ul className="space-y-2 text-sm text-yellow-700">
            <li>• <strong>Single Image Upload:</strong> Click or drag & drop one image file</li>
            <li>• <strong>Multiple Image Upload:</strong> Click or drag & drop multiple image files at once</li>
            <li>• <strong>Enhanced Upload:</strong> Uses the updated UploadImage component with multiple mode enabled</li>
            <li>• <strong>File Types:</strong> Supports JPEG, JPG, PNG, and WebP formats</li>
            <li>• <strong>File Size:</strong> Maximum 5MB per image</li>
            <li>• <strong>Features:</strong> Drag & drop, preview, remove individual images, clear all</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default ImageUploadTest;
