import { useEffect, useState } from "react";
import { X } from "lucide-react";
import regions from "../../../data/regions";

const UpdateRejionRouteModal = ({ open, onClose, region, onSave  }) => {
  const [name, setName] = useState("");
  const [regionName, setRegionName] = useState("");

  useEffect(() => {
    setName(region?.name || "");
    setRegionName(region?.regionName || "");
  }, [region]);

  if (!open) return null;

  const handleSave = () => {
    if (!region) return;
    const trimmed = name.trim();
    if (!trimmed) return;
    onSave && onSave({ ...region, name: trimmed, regionName: regionName || null });
    onClose && onClose();
  };

  return (
    <div
      onClick={() => onClose && onClose()}
      className="fixed !my-0 inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    >
      <div
        onClick={(e) => e.stopPropagation()}
        className="bg-white rounded-xl shadow-xl w-full max-w-lg"
      >
        <div className="p-6 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-xl font-bold text-gray-900">Update Route</h2>
          <button
            onClick={() => onClose && onClose()}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6 space-y-5">
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Route Name
            </label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              placeholder="e.g. Cairo East Route"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Region
            </label>
            <select
              value={regionName}
              onChange={(e) => setRegionName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white"
            >
              <option value="">Choose a region</option>
              {regions.map((r) => (
                <option key={r.id} value={r.name}>
                  {r.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="p-6 border-t border-gray-200 flex gap-3">
          <button
            onClick={() => onClose && onClose()}
            className="flex-1 px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={!name.trim()}
            className="flex-1 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors disabled:opacity-50"
          >
            Save Changes
          </button>
        </div>
      </div>
    </div>
  );
};

export default UpdateRejionRouteModal;
