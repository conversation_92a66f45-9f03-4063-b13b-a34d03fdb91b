import React, { useEffect, useState } from "react";

import FactoryShipList from "../FactoryDashboard/components/FactoryShipList";
import AddShipmentModal from "../../Components/Modals/AddNewShipment";
import { getAllShipmements } from "../../api/apiService";

export default function SuperAdminDashboard() {
  const [shipments, setShipments] = useState([]);
  const [selectedShipment, setSelectedShipment] = useState(null);
  const [showAddShipment, setShowAddShipment] = useState(false);
  const [pagination, setPagination] = useState(null);
  const [loading, setLoading] = useState(false);

  const loadShipments = async (page = 1, perPage = 50) => {
    try {
      setLoading(true);
      const result = await getAllShipmements({ page, per_page: perPage });
      // result is expected to be { data: [...], pagination: { ... } }
      setShipments(Array.isArray(result?.data) ? result.data : []);
      setPagination(result?.pagination || null);
    } catch (e) {
      console.error("Failed to fetch shipments:", e);
      setShipments([]);
      setPagination(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadShipments(1, 50);
  }, []);

  return (
    <div className="space-y-6">
      {/* Shipments Section */}
      <div className="space-y-5">
        <div className="p-6 border-b border-gray-200 bg-white rounded-xl shadow-sm border">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-gray-900">
              Current Shipments
            </h2>
            {/* <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setShowAddShipment(true)}
              >
                Add Shipment
              </Button>
            </Space> */}
          </div>
        </div>

        <FactoryShipList
          shipments={shipments}
          setShipments={setShipments}
          setSelectedShipment={setSelectedShipment}
          serverPagination={pagination}
          onPaginate={(page, pageSize) => loadShipments(page, pageSize)}
        />
      </div>
      {/* {selectedShipment && (
        <ShipmentDetailsModal
          selectedShipment={selectedShipment}
          setSelectedShipment={setSelectedShipment}
        />
      )} */}
      {showAddShipment && (
        <AddShipmentModal
          showAddShipment={showAddShipment}
          setShowAddShipment={setShowAddShipment}
          setShipments={setShipments}
          shipments={shipments}
        />
      )}
    </div>
  );
}
