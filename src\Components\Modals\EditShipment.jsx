import { useState, useEffect, useMemo } from "react";
import { Select } from "antd";
import {
  getAllRegions,
  getRegionRoutes,
  getDistributersByRegionId,
  getProductionsByRegionId,
  getVihiclesByRegionId,
} from "../../api/apiService";
import { updateShipment } from "../../api/apiService";

const EditShipmentModal = ({
  showEditShipment,
  setShowEditShipment,
  setShipments,
  shipments,
  selectedShipment,
}) => {
  const [isUpdating, setIsUpdating] = useState(false);
  const [formData, setFormData] = useState({
    bakeryType: "",
    quantity: "",
    regionId: selectedShipment?.regionId || "",
    routeId: selectedShipment?.routeId || "",
    factoryId: selectedShipment?.factoryId || "",
    distributorId: selectedShipment?.distributorId || "",
    vehicleId: selectedShipment?.vehicleId || "",
    image: null,
    images: [], // For multiple images
  });

  useEffect(() => {
    console.log("selectedData", {
      bakeryType: "",
      quantity: "",
      regionId: selectedShipment?.region.id || "",
      routeId: selectedShipment?.route.id || "",
      image: null,
      images: [], // For multiple images
    });
  }, [formData, selectedShipment]);

  // Pre-fill form data when selectedShipment changes
  useEffect(() => {
    if (selectedShipment) {
      setFormData({
        bakeryType: selectedShipment.bakeryType || "",
        quantity: selectedShipment.quantity || "",
        regionId:
          selectedShipment.region?.id ||
          selectedShipment.regionId ||
          selectedShipment.region_id ||
          "",
        routeId:
          selectedShipment.route?.id ||
          selectedShipment.routeId ||
          selectedShipment.route_id ||
          "",
        distributor_id:
          selectedShipment.distributor?.id ||
          selectedShipment.distributor_id || // this is returned one from the server
          selectedShipment.distributorId ||
          "",
        production_id:
          selectedShipment.production?.id ||
          selectedShipment.production_id || // this is returned one from the server
          selectedShipment.productionId ||
          "",
        factoryId:
          selectedShipment.factory?.id || selectedShipment.factoryId || "",
        vehicleId:
          selectedShipment.vehicle?.id ||
          selectedShipment.vehicleId ||
          selectedShipment.vehicle_id ||
          "",
        shift: selectedShipment.shift || "",
        remarks: selectedShipment.remarks || "",
        exit_time: (() => {
          const raw =
            selectedShipment.exitTime || selectedShipment.exit_time || "";
          if (!raw) return "";
          // If already formatted as 'YYYY-MM-DDTHH:mm', return as-is
          if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/.test(raw)) return raw;
          // Try to parse to 'YYYY-MM-DDTHH:mm'
          const d = new Date(raw);
          if (isNaN(d.getTime())) return "";
          const pad = (n) => (n < 10 ? "0" + n : "" + n);
          return (
            d.getFullYear() +
            "-" +
            pad(d.getMonth() + 1) +
            "-" +
            pad(d.getDate()) +
            "T" +
            pad(d.getHours()) +
            ":" +
            pad(d.getMinutes())
          );
        })(),
        image: selectedShipment.factoryImage || null,
        images: selectedShipment.images || [],
      });
    }
  }, [selectedShipment]);

  // Remote data
  const [regions, setRegions] = useState([]);
  const [routes, setRoutes] = useState([]);
  const [distributors, setDistributors] = useState([]);
  const [productions, setProductions] = useState([]);
  const [vehicles, setVehicles] = useState([]);

  // Keep existing variable names used later in update handler
  const availableRoutes = routes;
  const availableFactories = productions;

  // Load all regions on mount
  useEffect(() => {
    let isMounted = true;
    getAllRegions()
      .then((res) => {
        if (!isMounted) return;
        setRegions(res.data || []);
      })
      .catch(() => setRegions([]));
    return () => {
      isMounted = false;
    };
  }, []);

  // When region changes, load dependent lists
  useEffect(() => {
    if (!formData.regionId) {
      setRoutes([]);
      setDistributors([]);
      setProductions([]);
      setVehicles([]);
      return;
    }
    let isMounted = true;
    Promise.all([
      getRegionRoutes(formData.regionId),
      getDistributersByRegionId(formData.regionId),
      getProductionsByRegionId(formData.regionId),
      getVihiclesByRegionId(formData.regionId),
    ])
      .then(([rs, ds, ps, vs]) => {
        if (!isMounted) return;
        setRoutes(rs.data || []);
        setDistributors(ds.data || []);
        setProductions(ps.data || []);
        setVehicles(vs.data || []);
      })
      .catch(() => {
        if (!isMounted) return;
        setRoutes([]);
        setDistributors([]);
        setProductions([]);
        setVehicles([]);
      });
    return () => {
      isMounted = false;
    };
  }, [formData.regionId]);

  return (
    <div className=" !m-0 fixed  !p-10 inset-0 top-0 bottom-0 left-0 right-0 bg-black bg-opacity-50 flex items-start justify-center z-50 ">
      <div className="bg-white rounded-xl shadow-xl w-full max-w-md max-h-[95vh] overflow-y-auto custom-scrollbar">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900">Edit Shipment</h2>
        </div>

        <div className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Bakery Type
            </label>
            <input
              type="text"
              value={formData.bakeryType}
              onChange={(e) =>
                setFormData({ ...formData, bakeryType: e.target.value })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              placeholder="e.g. White Bread"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Quantity
            </label>
            <input
              type="number"
              value={formData.quantity}
              onChange={(e) =>
                setFormData({ ...formData, quantity: e.target.value })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Region
            </label>
            <Select
              showSearch
              placeholder="Select region"
              value={formData.regionId || null}
              onChange={(value) =>
                setFormData({
                  ...formData,
                  regionId: value,
                  routeId: "",
                  factoryId: "",
                  distributorId: "",
                  vehicleId: "",
                })
              }
              optionFilterProp="label"
              className="w-full"
              options={(regions || []).map((r) => ({
                value: r.id,
                label: r.name,
              }))}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Route
            </label>
            <Select
              showSearch
              placeholder={
                formData.regionId ? "Select route" : "Select region first"
              }
              value={formData.routeId || null}
              onChange={(value) => setFormData({ ...formData, routeId: value })}
              disabled={!formData.regionId}
              optionFilterProp="label"
              className="w-full"
              options={availableRoutes.map((rt) => ({
                value: rt.id,
                label: rt.name,
              }))}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Distributer
            </label>
            <Select
              showSearch
              placeholder={
                formData.regionId ? "Select distributer" : "Select region first"
              }
              value={formData.distributor_id || null}
              onChange={(value) =>
                setFormData({ ...formData, distributor_id: value })
              }
              optionFilterProp="label"
              className="w-full"
              options={distributors.map((d) => ({
                value: d.id,
                label: d.name,
              }))}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Production
            </label>
            <Select
              showSearch
              placeholder={
                formData.regionId ? "Select factory" : "Select region first"
              }
              value={formData.production_id || null}
              onChange={(value) =>
                setFormData({ ...formData, production_id: value })
              }
              disabled={!formData.regionId}
              optionFilterProp="label"
              className="w-full"
              options={availableFactories.map((f) => ({
                value: f.id,
                label: f.name,
              }))}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Vehicle
            </label>
            <Select
              showSearch
              placeholder={
                formData.regionId ? "Select vehicle" : "Select region first"
              }
              value={formData.vehicleId || null}
              onChange={(value) =>
                setFormData({ ...formData, vehicleId: value })
              }
              disabled={!formData.regionId}
              optionFilterProp="label"
              className="w-full"
              options={vehicles.map((v) => ({
                value: v.id,
                label: v.name,
              }))}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Shift
            </label>
            <Select
              showSearch
              placeholder={"Select Shift am or pm"}
              value={formData.shift || null}
              onChange={(value) => setFormData({ ...formData, shift: value })}
              optionFilterProp="label"
              className="w-full"
              options={["am", "pm"].map((s) => ({
                value: s,
                label: s.toUpperCase(),
              }))}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Remarks
            </label>
            <textarea
              value={formData.remarks}
              onChange={(e) =>
                setFormData({ ...formData, remarks: e.target.value })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              placeholder="e.g. White Bread"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Exit Time
            </label>
            <input
              type="datetime-local"
              value={formData.exit_time || ""}
              onChange={(e) => {
                console.log("exit_time", e.target.value);
                setFormData({ ...formData, exit_time: e.target.value });
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              placeholder="Select exit date and time"
            />
          </div>
        </div>

        <div className="p-6 border-t border-gray-200 flex gap-3">
          <button
            onClick={() => setShowEditShipment(false)}
            className="flex-1 px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={async () => {
              if (isUpdating) return;
              try {
                setIsUpdating(true);
                const shipmentId = selectedShipment?.id;

                // Ensure exit_time matches "Y-m-d H:i" (e.g., 2025-11-04 13:45)
                const formattedExitTime = (() => {
                  const raw = formData.exit_time;
                  if (!raw) return undefined;
                  // If already in "YYYY-MM-DD HH:mm", keep as is
                  if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$/.test(raw)) return raw;
                  // If in datetime-local format "YYYY-MM-DDTHH:mm", replace T with space
                  if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}/.test(raw)) {
                    return raw.replace("T", " ").slice(0, 16);
                  }
                  // Fallback: try to parse as Date and format
                  const d = new Date(raw);
                  if (isNaN(d.getTime())) return undefined;
                  const pad = (n) => (n < 10 ? "0" + n : "" + n);
                  return (
                    d.getFullYear() +
                    "-" +
                    pad(d.getMonth() + 1) +
                    "-" +
                    pad(d.getDate()) +
                    " " +
                    pad(d.getHours()) +
                    ":" +
                    pad(d.getMinutes())
                  );
                })();

                const payload = {
                  id: selectedShipment?.id,
                  shipment_id: selectedShipment?.id,
                  bakeryType: formData.bakeryType,
                  quantity: formData.quantity,
                  route_id: formData.routeId,
                  distributor_id: formData.distributor_id,
                  production_id: formData.production_id,
                  factory_id: formData.factoryId,
                  vehicle_id: formData.vehicleId,
                  shift: formData.shift,
                  remarks: formData.remarks,
                  exit_time: formattedExitTime,
                };

                await updateShipment(shipmentId, payload);

                // Optimistically update local list
                if (Array.isArray(shipments) && setShipments) {
                  setShipments(
                    shipments.map((s) =>
                      s.id === selectedShipment?.id
                        ? {
                            ...s,
                            ...payload,
                            routeId: payload.route_id ?? s.routeId,
                            factoryId: payload.factory_id ?? s.factoryId,
                            vehicleId: payload.vehicle_id ?? s.vehicleId,
                          }
                        : s
                    )
                  );
                }

                setShowEditShipment(false);
              } catch (e) {
                console.error("Failed to update shipment", e);
              } finally {
                setIsUpdating(false);
              }
            }}
            className="flex-1 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors disabled:opacity-60"
            disabled={isUpdating}
          >
            {isUpdating ? "Updating..." : "Update Shipment"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default EditShipmentModal;
