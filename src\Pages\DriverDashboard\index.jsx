import initialShipments from "../../data/shipments";
import { useState, useEffect, useMemo } from "react";
import {
  Package,
  CheckCircle,
  Camera,
  Eye,
  Upload,
  Truck,
  Store,
  AlertCircle,
  MapPin,
  Clock,
  Filter,
  Search,
  Navigation,
  Phone,
  Star,
  Calendar,
  Route,
  Loader2,
  AlertTriangle,
  CheckCircle2,
  Clock1,
} from "lucide-react";
import { useUser } from "../../context/UserContext";
import ShipmentDetailsModal from "../../Components/Modals/ShipmentDetailsModal";
import MarkInTransitModal from "../../Components/Modals/MarkInTransitModal";
import MarkDeliveredModal from "../../Components/Modals/MarkDeliveredModal";
import UploadImage from "../../utils/UploadImage";
import { formatDate } from "./../../lib/formateDate";

const DriverDashboard = () => {
  const { currentUser, setUser, clearUser, isLoggedIn, hasRole } = useUser();

  // State management
  const [shipments, setShipments] = useState(initialShipments);
  const [selectedShipment, setSelectedShipment] = useState(null);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploadingForShipment, setUploadingForShipment] = useState(null);
  const [uploadedImages, setUploadedImages] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("Pending Pickup");
  const [sortBy, setSortBy] = useState("priority");
  const [isLoading, setIsLoading] = useState(false);
  const [showCompletedShipments, setShowCompletedShipments] = useState(false);
  const [notifications, setNotifications] = useState([]);

  // New modal states
  const [showMarkInTransitModal, setShowMarkInTransitModal] = useState(false);
  const [showMarkDeliveredModal, setShowMarkDeliveredModal] = useState(false);
  const [selectedShipmentForAction, setSelectedShipmentForAction] =
    useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Simulated GPS location
  const [currentLocation, setCurrentLocation] = useState({
    lat: 30.7947,
    lng: 30.9991, // Tanta coordinates
  });

  // Auto-refresh shipments every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      // In real app, this would fetch from API
      console.log("Refreshing shipments...");
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  // Priority calculation based on delivery time and distance
  const calculatePriority = (shipment) => {
    const now = new Date();
    const deliveryTime = new Date(shipment.expectedDelivery || now);
    const hoursUntilDelivery = (deliveryTime - now) / (1000 * 60 * 60);

    if (hoursUntilDelivery < 2) return "urgent";
    if (hoursUntilDelivery < 6) return "high";
    return "normal";
  };

  // Enhanced filtering and sorting
  const filteredAndSortedShipments = useMemo(() => {
    let filtered = shipments.filter((shipment) => {
      if (!shipment.driver) return false;

      const matchesSearch =
        shipment.bakeryType.toLowerCase().includes(searchTerm.toLowerCase()) ||
        shipment.branch.toLowerCase().includes(searchTerm.toLowerCase()) ||
        shipment.id.toString().includes(searchTerm);

      const matchesStatus =
        statusFilter === "all" || shipment.status === statusFilter;

      const isCompleted = shipment.status === "Delivered";
      if (!showCompletedShipments && isCompleted) return false;

      return matchesSearch && matchesStatus;
    });

    // Sort shipments
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "priority":
          const priorityOrder = { urgent: 0, high: 1, normal: 2 };
          return (
            priorityOrder[calculatePriority(a)] -
            priorityOrder[calculatePriority(b)]
          );
        case "distance":
          // In real app, calculate actual distance
          return Math.random() - 0.5;
        case "delivery_time":
          return (
            new Date(a.expectedDelivery || 0) -
            new Date(b.expectedDelivery || 0)
          );
        default:
          return 0;
      }
    });

    return filtered;
  }, [shipments, searchTerm, statusFilter, sortBy, showCompletedShipments]);

  // Enhanced status update with validation
  const handleStatusUpdate = async (shipmentId, newStatus) => {
    setIsLoading(true);

    try {
      const shipment = shipments.find((s) => s.id === shipmentId);

      // Validation: Check if required images are uploaded for status changes
      if (newStatus === "In Transit" && !shipment.driverPickupImage) {
        addNotification(
          "Please upload pickup image before marking as In Transit",
          "warning"
        );
        setIsLoading(false);
        return;
      }

      if (newStatus === "Delivered" && !shipment.driverDeliveryImage) {
        addNotification(
          "Please upload delivery image before marking as Delivered",
          "warning"
        );
        setIsLoading(false);
        return;
      }

      setShipments((prevShipments) =>
        prevShipments.map((shipment) => {
          if (shipment.id === shipmentId) {
            const updatedShipment = { ...shipment, status: newStatus };

            // Update timestamps based on status
            if (
              newStatus === "In Transit" &&
              !shipment.timestamps.driverPickup
            ) {
              updatedShipment.timestamps.driverPickup = new Date();
            } else if (
              newStatus === "Delivered" &&
              !shipment.timestamps.delivery
            ) {
              updatedShipment.timestamps.delivery = new Date();
            }

            return updatedShipment;
          }
          return shipment;
        })
      );

      addNotification(
        `Shipment ${shipmentId} marked as ${newStatus}`,
        "success"
      );
    } catch (error) {
      addNotification("Failed to update shipment status", "error");
    } finally {
      setIsLoading(false);
    }
  };

  // Enhanced image upload with validation
  const handleImageUpload = async (shipmentId, imageType, file, previewUrl) => {
    if (!file || file.size > 5 * 1024 * 1024) {
      // 5MB limit
      addNotification("Image must be less than 5MB", "warning");
      return;
    }

    setShipments((prevShipments) =>
      prevShipments.map((shipment) => {
        if (shipment.id === shipmentId) {
          return {
            ...shipment,
            [imageType]: previewUrl,
            [`${imageType}Timestamp`]: new Date(),
          };
        }
        return shipment;
      })
    );

    addNotification("Image uploaded successfully", "success");
  };

  // Handle Mark In Transit submission
  const handleMarkInTransit = async (data) => {
    setIsSubmitting(true);

    try {
      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      setShipments((prevShipments) =>
        prevShipments.map((shipment) => {
          if (shipment.id === data.shipmentId) {
            return {
              ...shipment,
              status: "In Transit",
              driverPickupImages: data.pickupImages,
              pickupNote: data.note,
              comments: [...shipment.comments, data.comment],
              timestamps: {
                ...shipment.timestamps,
                driverPickup: data.timestamp,
              },
            };
          }
          return shipment;
        })
      );

      addNotification("Shipment marked as In Transit successfully!", "success");
      setShowMarkInTransitModal(false);
      setSelectedShipmentForAction(null);
    } catch (error) {
      addNotification("Failed to update shipment status", "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle Mark Delivered submission
  const handleMarkDelivered = async (data) => {
    setIsSubmitting(true);

    try {
      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      setShipments((prevShipments) =>
        prevShipments.map((shipment) => {
          if (shipment.id === data.shipmentId) {
            return {
              ...shipment,
              status: "Delivered",
              driverDeliveryImages: data.deliveryImages,
              comments: [...shipment.comments, data.comment],
              timestamps: {
                ...shipment.timestamps,
                delivery: data.timestamp,
              },
            };
          }
          return shipment;
        })
      );

      addNotification("Shipment marked as Delivered successfully!", "success");
      setShowMarkDeliveredModal(false);
      setSelectedShipmentForAction(null);
    } catch (error) {
      addNotification("Failed to update shipment status", "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Open Mark In Transit modal
  const openMarkInTransitModal = (shipment) => {
    setSelectedShipmentForAction(shipment);
    setShowMarkInTransitModal(true);
  };

  // Open Mark Delivered modal
  const openMarkDeliveredModal = (shipment) => {
    setSelectedShipmentForAction(shipment);
    setShowMarkDeliveredModal(true);
  };

  // Notification system
  const addNotification = (message, type = "info") => {
    const id = Date.now();
    setNotifications((prev) => [...prev, { id, message, type }]);

    setTimeout(() => {
      setNotifications((prev) => prev.filter((n) => n.id !== id));
    }, 5000);
  };

  // Get priority badge color
  const getPriorityColor = (priority) => {
    switch (priority) {
      case "urgent":
        return "bg-red-100 text-red-800 border-red-200";
      case "high":
        return "bg-orange-100 text-orange-800 border-orange-200";
      default:
        return "bg-blue-100 text-blue-800 border-blue-200";
    }
  };

  // Get status color (enhanced)
  const getStatusColor = (status) => {
    switch (status) {
      case "Delivered":
        return "bg-green-100 text-green-800 border-green-200";
      case "In Transit":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "Pending Pickup":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "Delayed":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  // Get next status
  const getNextStatus = (currentStatus) => {
    switch (currentStatus) {
      case "Pending Pickup":
        return "In Transit";
      case "In Transit":
        return "Delivered";
      default:
        return currentStatus;
    }
  };

  // Calculate statistics
  const stats = useMemo(() => {
    const totalAssigned = shipments.filter(
      (s) => s.driver && s.status !== "Delivered"
    ).length;
    const delivered = shipments.filter((s) => s.status === "Delivered").length;
    const inTransit = shipments.filter((s) => s.status === "In Transit").length;
    const urgent = shipments.filter(
      (s) => calculatePriority(s) === "urgent"
    ).length;

    return { totalAssigned, delivered, inTransit, urgent };
  }, [shipments]);

  return (
    <div className="space-y-6 relative">
      {/* Notifications */}
      <div className="fixed overflow-auto top-4 right-4 z-50 space-y-2">
        {notifications.map((notification) => (
          <div
            key={notification.id}
            className={`p-4 rounded-lg shadow-lg border-l-4 ${
              notification.type === "success"
                ? "bg-green-50 border-green-500 text-green-800"
                : notification.type === "warning"
                ? "bg-yellow-50 border-yellow-500 text-yellow-800"
                : notification.type === "error"
                ? "bg-red-50 border-red-500 text-red-800"
                : "bg-blue-50 border-blue-500 text-blue-800"
            }`}
          >
            {notification.message}
          </div>
        ))}
      </div>

      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Driver Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Welcome back, {currentUser?.user?.name || "Driver"}! You have{" "}
            {stats.totalAssigned} active shipments.
          </p>
        </div>
      </div>

      {/* Enhanced Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-2xl font-bold text-gray-900">
                {stats.totalAssigned}
              </p>
              <p className="text-gray-600">Active Shipments</p>
            </div>
            <div className="bg-orange-100 p-3 rounded-full">
              <Package className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-2xl font-bold text-gray-900">
                {stats.delivered}
              </p>
              <p className="text-gray-600">Delivered Today</p>
            </div>
            <div className="bg-green-100 p-3 rounded-full">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-2xl font-bold text-gray-900">
                {stats.inTransit}
              </p>
              <p className="text-gray-600">In Transit</p>
            </div>
            <div className="bg-yellow-100 p-3 rounded-full">
              <Truck className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-2xl font-bold text-red-600">{stats.urgent}</p>
              <p className="text-gray-600">Urgent Deliveries</p>
            </div>
            <div className="bg-red-100 p-3 rounded-full">
              <AlertTriangle className="w-6 h-6 text-red-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Quick Status Overview */}
      <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-xl p-6 border border-blue-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-800">
            Today's Workflow
          </h2>
          <div className="text-sm text-gray-600">
            {new Date().toLocaleDateString("en-US", {
              weekday: "long",
              year: "numeric",
              month: "long",
              day: "numeric",
            })}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Pending Pickup */}
          <div className="bg-white rounded-lg p-4 border border-blue-200">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span className="font-medium text-gray-700">
                Ready for Pickup
              </span>
            </div>
            <p className="text-2xl font-bold text-blue-600 mb-1">
              {
                shipments.filter(
                  (s) => s.status === "Pending Pickup" && s.driver
                ).length
              }
            </p>
            <p className="text-xs text-gray-500">
              Take photos to start delivery
            </p>
          </div>

          {/* In Transit */}
          <div className="bg-white rounded-lg p-4 border border-yellow-200">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <span className="font-medium text-gray-700">In Transit</span>
            </div>
            <p className="text-2xl font-bold text-yellow-600 mb-1">
              {
                shipments.filter((s) => s.status === "In Transit" && s.driver)
                  .length
              }
            </p>
            <p className="text-xs text-gray-500">
              Take delivery photos to complete
            </p>
          </div>

          {/* Completed */}
          <div className="bg-white rounded-lg p-4 border border-green-200">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="font-medium text-gray-700">Completed</span>
            </div>
            <p className="text-2xl font-bold text-green-600 mb-1">
              {
                shipments.filter((s) => s.status === "Delivered" && s.driver)
                  .length
              }
            </p>
            <p className="text-xs text-gray-500">
              Successfully delivered today
            </p>
          </div>
        </div>
      </div>

      {/* Enhanced Filters and Search */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search shipments..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            />
          </div>

          {/* Filters */}
          <div className="flex gap-2 flex-wrap">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="Pending Pickup">Pending Pickup</option>
              <option value="In Transit">In Transit</option>
              <option value="Delivered">Delivered</option>
            </select>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            >
              <option value="priority">Sort by Priority</option>
              <option value="distance">Sort by Distance</option>
              <option value="delivery_time">Sort by Delivery Time</option>
            </select>

            <button
              onClick={() => setShowCompletedShipments(!showCompletedShipments)}
              className={`px-4 py-2 rounded-lg border transition-colors ${
                showCompletedShipments
                  ? "bg-green-100 border-green-300 text-green-700"
                  : "bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200"
              }`}
            >
              {showCompletedShipments ? "Hide" : "Show"} Completed
            </button>
          </div>
        </div>
      </div>

      {/* Enhanced Shipments List */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-bold text-gray-900">
              Your Shipments ({filteredAndSortedShipments.length})
            </h2>
            <div className="text-sm text-gray-500">
              Last updated: {new Date().toLocaleTimeString()}
            </div>
          </div>
        </div>

        <div className="divide-y divide-gray-200">
          {filteredAndSortedShipments.length === 0 ? (
            <div className="p-12 text-center">
              <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No shipments found
              </h3>
              <p className="text-gray-500">
                Try adjusting your search or filters
              </p>
            </div>
          ) : (
            filteredAndSortedShipments.map((shipment) => {
              const priority = calculatePriority(shipment);

              return (
                <div
                  key={shipment.id}
                  className="p-6 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex gap-4">
                    {/* Product Image */}
                    <div className="flex-shrink-0">
                      <div className="w-20 h-20 rounded-lg overflow-hidden bg-gray-100 border border-gray-200 relative">
                        {shipment.image ? (
                          <img
                            src={shipment.image}
                            alt={shipment.bakeryType}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <Package className="w-8 h-8 text-gray-400" />
                          </div>
                        )}

                        {/* Priority indicator */}
                        {priority === "urgent" && (
                          <div className="absolute -top-1 -right-1">
                            <div className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                              <AlertTriangle className="w-2 h-2 text-white" />
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Enhanced Shipment Details */}
                    <div className="flex-1 min-w-0">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-3">
                            <h3 className="font-semibold text-gray-900 text-lg">
                              {shipment.bakeryType}
                            </h3>
                            <span
                              className={`px-2 py-1 text-xs font-medium rounded-full border ${getPriorityColor(
                                priority
                              )}`}
                            >
                              {priority}
                            </span>
                          </div>

                          {/* Status Progress Workflow */}
                          <div className="mb-4">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium text-gray-700">
                                Delivery Progress
                              </span>
                              <span
                                className={`text-sm font-semibold ${
                                  getStatusColor(shipment.status).split(" ")[1]
                                }`}
                              >
                                {shipment.status}
                              </span>
                            </div>

                            {/* Progress Steps */}
                            <div className="flex items-center space-x-2">
                              {/* Step 1: Pickup */}
                              <div className="flex items-center">
                                <div
                                  className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-semibold ${
                                    shipment.status === "Pending Pickup"
                                      ? "bg-blue-100 text-blue-600 border-2 border-blue-300"
                                      : shipment.driverPickupImage
                                      ? "bg-green-100 text-green-600 border-2 border-green-300"
                                      : "bg-gray-100 text-gray-400 border-2 border-gray-200"
                                  }`}
                                >
                                  {shipment.driverPickupImage ? (
                                    <CheckCircle2 className="w-4 h-4" />
                                  ) : (
                                    "1"
                                  )}
                                </div>
                                <div className="ml-2">
                                  <p
                                    className={`text-xs font-medium ${
                                      shipment.status === "Pending Pickup"
                                        ? "text-blue-600"
                                        : shipment.driverPickupImage
                                        ? "text-green-600"
                                        : "text-gray-400"
                                    }`}
                                  >
                                    Pending To Pickup
                                  </p>
                                  <p className="text-xs text-gray-500">
                                    Take photo
                                  </p>
                                </div>
                              </div>

                              {/* Arrow */}
                              <div
                                className={`w-8 h-0.5 ${
                                  shipment.driverPickupImage
                                    ? "bg-green-300"
                                    : "bg-gray-200"
                                }`}
                              ></div>

                              {/* Step 2: In Transit */}
                              <div className="flex items-center">
                                <div
                                  className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-semibold ${
                                    shipment.status === "In Transit"
                                      ? "bg-yellow-100 text-yellow-600 border-2 border-yellow-300"
                                      : shipment.status === "Delivered"
                                      ? "bg-green-100 text-green-600 border-2 border-green-300"
                                      : "bg-gray-100 text-gray-400 border-2 border-gray-200"
                                  }`}
                                >
                                  {shipment.status === "Delivered" ? (
                                    <CheckCircle2 className="w-4 h-4" />
                                  ) : (
                                    "2"
                                  )}
                                </div>
                                <div className="ml-2">
                                  <p
                                    className={`text-xs font-medium ${
                                      shipment.status === "In Transit"
                                        ? "text-yellow-600"
                                        : shipment.status === "Delivered"
                                        ? "text-green-600"
                                        : "text-gray-400"
                                    }`}
                                  >
                                    In Transit
                                  </p>
                                  <p className="text-xs text-gray-500">
                                    On the way
                                  </p>
                                </div>
                              </div>

                              {/* Arrow */}
                              <div
                                className={`w-8 h-0.5 ${
                                  shipment.status === "Delivered"
                                    ? "bg-green-300"
                                    : "bg-gray-200"
                                }`}
                              ></div>

                              {/* Step 3: Delivered */}
                              <div className="flex items-center">
                                <div
                                  className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-semibold ${
                                    shipment.status === "Delivered"
                                      ? "bg-green-100 text-green-600 border-2 border-green-300"
                                      : "bg-gray-100 text-gray-400 border-2 border-gray-200"
                                  }`}
                                >
                                  {shipment.status === "Delivered" ? (
                                    <CheckCircle2 className="w-4 h-4" />
                                  ) : (
                                    "3"
                                  )}
                                </div>
                                <div className="ml-2">
                                  <p
                                    className={`text-xs font-medium ${
                                      shipment.status === "Delivered"
                                        ? "text-green-600"
                                        : "text-gray-400"
                                    }`}
                                  >
                                    Delivered
                                  </p>
                                  <p className="text-xs text-gray-500">
                                    Take photo
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>

                          <div className="grid grid-cols-1 lg:grid-cols-2 gap-x-6 gap-y-2">
                            <div className="space-y-1">
                              <p className="text-gray-600 flex items-center gap-2">
                                <Package className="w-4 h-4" />
                                Quantity:{" "}
                                <span className="font-medium">
                                  {shipment.quantity}
                                </span>
                              </p>
                              <p className="text-gray-600 flex items-center gap-2">
                                <Store className="w-4 h-4" />
                                Destination:{" "}
                                <span className="font-medium">
                                  {shipment.branch}
                                </span>
                              </p>
                              <p className="text-gray-600 flex items-center gap-2">
                                <Clock1 className="w-4 h-4" />
                                <span className="font-medium">
                                  {formatDate(shipment.timestamps.created)}
                                </span>
                              </p>
                            </div>

                            <div className="space-y-1">
                              <p className="text-gray-600 flex items-center gap-2">
                                <Truck className="w-4 h-4" />
                                Driver:{" "}
                                <span className="font-medium">
                                  {shipment.driver}
                                </span>
                              </p>
                              <p className="text-gray-600 flex items-center gap-2">
                                <Clock className="w-4 h-4" />
                                Expected:{" "}
                                <span className="font-medium">2:30 PM</span>
                              </p>
                              <p className="text-gray-600 flex items-center gap-2">
                                <Calendar className="w-4 h-4" />
                                <span className="font-medium">Today</span>
                              </p>
                            </div>
                          </div>

                          {/* Image Status Indicators */}
                          <div className="mt-4 flex gap-2">
                            {shipment.driverPickupImage && (
                              <div className="flex items-center gap-1 text-xs text-green-600 bg-green-50 px-2 py-1 rounded-full border border-green-200">
                                <CheckCircle2 className="w-3 h-3" />
                                Pickup Photo ✓
                              </div>
                            )}
                            {shipment.driverDeliveryImage && (
                              <div className="flex items-center gap-1 text-xs text-green-600 bg-green-50 px-2 py-1 rounded-full border border-green-200">
                                <CheckCircle2 className="w-3 h-3" />
                                Delivery Photo ✓
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Enhanced Action Buttons */}
                        <div className="flex  gap-2 ml-4">
                          {/* Primary Action Button */}
                          {shipment.status === "Pending Pickup" && (
                            <button
                              onClick={() => openMarkInTransitModal(shipment)}
                              className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700 transition-colors flex items-center gap-2 shadow-sm font-medium"
                            >
                              <Truck className="w-4 h-4" />
                              Mark In Transit
                            </button>
                          )}

                          {shipment.status === "In Transit" && (
                            <button
                              onClick={() => openMarkDeliveredModal(shipment)}
                              className="bg-green-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-green-700 transition-colors flex items-center gap-2 shadow-sm font-medium"
                            >
                              <CheckCircle className="w-4 h-4" />
                              Mark Delivered
                            </button>
                          )}

                          {shipment.status === "Delivered" && (
                            <div className="bg-green-50 border border-green-200 rounded-lg px-3 py-2 text-center">
                              <CheckCircle2 className="w-5 h-5 text-green-600 mx-auto mb-1" />
                              <p className="text-xs font-medium text-green-700">
                                Completed
                              </p>
                            </div>
                          )}

                          {/* Secondary Actions */}
                          <div className="flex gap-2">
                            <button
                              onClick={() => setSelectedShipment(shipment)}
                              className="bg-gray-100 text-gray-700 px-3 py-2 rounded-lg text-sm hover:bg-gray-200 transition-colors flex items-center gap-1"
                            >
                              <Eye className="w-4 h-4" />
                              Details
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })
          )}
        </div>
      </div>

      {/* Shipment Details Modal */}
      {selectedShipment && (
        <ShipmentDetailsModal
          selectedShipment={selectedShipment}
          setSelectedShipment={setSelectedShipment}
        />
      )}

      {/* Mark In Transit Modal */}
      {showMarkInTransitModal && selectedShipmentForAction && (
        <MarkInTransitModal
          isOpen={showMarkInTransitModal}
          onClose={() => {
            setShowMarkInTransitModal(false);
            setSelectedShipmentForAction(null);
          }}
          shipment={selectedShipmentForAction}
          onSubmit={handleMarkInTransit}
          isLoading={isSubmitting}
        />
      )}

      {/* Mark Delivered Modal */}
      {showMarkDeliveredModal && selectedShipmentForAction && (
        <MarkDeliveredModal
          isOpen={showMarkDeliveredModal}
          onClose={() => {
            setShowMarkDeliveredModal(false);
            setSelectedShipmentForAction(null);
          }}
          shipment={selectedShipmentForAction}
          onSubmit={handleMarkDelivered}
          isLoading={isSubmitting}
        />
      )}

      {/* Enhanced Image Upload Modal */}
    </div>
  );

  // Helper functions
  function openUploadModal(shipment) {
    setUploadingForShipment(shipment);
    setShowUploadModal(true);
  }

  function closeUploadModal() {
    setShowUploadModal(false);
    setUploadingForShipment(null);
    setUploadedImages([]);
  }
};

export default DriverDashboard;
