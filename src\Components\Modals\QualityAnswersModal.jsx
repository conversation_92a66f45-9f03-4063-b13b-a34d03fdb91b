import React from "react";
import { defaultQuestions } from "../../Pages/QualityCheckList";

const humanize = (str = "") =>
  String(str)
    .replace(/_/g, " ")
    .replace(/\s+/g, " ")
    .trim()
    .replace(/\b\w/g, (m) => m.toUpperCase());

const isEmptyObject = (obj) =>
  obj && typeof obj === "object" && !Array.isArray(obj) && Object.keys(obj).length === 0;

const QualityAnswersModal = ({
  isOpen,
  onClose,
  questions = defaultQuestions,
  testerName,
  checkedAt,
  answers: providedAnswers,
  qualityAnswers, // alternate prop name that may be used by callers
  qualityStatus,
  notes,
}) => {
  if (!isOpen) return null;



  // Normalize incoming answers from multiple possible shapes
  const normalizeAnswers = (input) => {
    if (!input) return null;
    let data = input;
    // Accept JSON strings
    if (typeof data === "string") {
      try {
        data = JSON.parse(data);
      } catch (_) {
        return null;
      }
    }

    // If already an array, try to coerce each item into { question, answer }
    if (Array.isArray(data)) {
      const coerced = data
        .filter((item) => item && typeof item === "object")
        .map((item) => {
          const question = item.question || item.text || item.label || humanize(item.id || item.key || "");
          const answer = item.answer ?? item.value ?? item.result ?? "";
          return {
            // Preserve original fields for potential future use
            ...item,
            question,
            answer,
          };
        })
        // Optional: stable sort when checklist_question_id exists
        .sort((a, b) => {
          const ai = typeof a.checklist_question_id === "number" ? a.checklist_question_id : Number.MAX_SAFE_INTEGER;
          const bi = typeof b.checklist_question_id === "number" ? b.checklist_question_id : Number.MAX_SAFE_INTEGER;
          return ai - bi;
        });
      return coerced;
    }

    // If object map of id -> value
    if (typeof data === "object") {
      return Object.entries(data).map(([id, value]) => {
        const val = typeof value === "object" && value !== null ? value.answer ?? value.value ?? value : value;
        return {
          id,
          question: humanize(id),
          answer: typeof val === "string" ? val : "",
        };
      });
    }

    return null;
  };

  const rawAnswers = providedAnswers ?? qualityAnswers;
  const answers = normalizeAnswers(rawAnswers) ??   []


  const renderAnswers = () => {
    if (!answers) {
      return (
        <div className="text-center text-gray-500 py-6">No quality answers available.</div>
      );
    }

    // If it's an array of structured answers
    if (Array.isArray(answers)) {
      if (answers.length === 0) {
        return (
          <div className="text-center text-gray-500 py-6">No quality answers available.</div>
        );
      }
      return (
        <div className="space-y-3">
          {answers.map((item, idx) => (
            <div key={idx} className="p-3 rounded-lg border bg-white">
              <div className="flex items-start justify-between gap-3">
                <div className="font-medium text-gray-900">
                  {item.question || item.text || humanize(item.id || item.key || `Question ${idx + 1}`)}
                </div>
                {item.answer && (
                  <span className={`px-2.5 py-1 text-xs rounded-full border font-medium ${
                    item.answer?.toLowerCase() === "yes"
                      ? "bg-emerald-50 text-emerald-700 border-emerald-200"
                      : item.answer?.toLowerCase() === "no"
                      ? "bg-red-50 text-red-700 border-red-200"
                      : "bg-gray-50 text-gray-700 border-gray-200"
                  }`}>
                    {humanize(item.answer)}
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      );
    }

    // Object map of id -> { answer, comment }
    if (typeof answers === "object") {
      if (isEmptyObject(answers)) {
        return (
          <div className="text-center text-gray-500 py-6">No quality answers available.</div>
        );
      }

      // Try to map ids to question text when provided
      const questionById = new Map(questions.map((q) => [q.id, q]));

      return (
        <div className="space-y-3">
          {Object.entries(answers).map(([id, value]) => {
            const q = questionById.get(id);
            const label = q?.text || humanize(id);
            const ans = typeof value === "string" ? value : value?.answer;
            const comment = typeof value === "object" ? value?.comment : undefined;
            return (
              <div key={id} className="p-3 rounded-lg border bg-white">
                <div className="flex items-start justify-between gap-3">
                  <div className="font-medium text-gray-900">{label}</div>
                  {ans && (
                    <span className={`px-2.5 py-1 text-xs rounded-full border font-medium ${
                      String(ans).toLowerCase() === "yes"
                        ? "bg-emerald-50 text-emerald-700 border-emerald-200"
                        : String(ans).toLowerCase() === "no"
                        ? "bg-red-50 text-red-700 border-red-200"
                        : "bg-gray-50 text-gray-700 border-gray-200"
                    }`}>
                      {humanize(ans)}
                    </span>
                  )}
                </div>
                {comment && (
                  <div className="mt-2 text-sm text-gray-600">{comment}</div>
                )}
              </div>
            );
          })}
        </div>
      );
    }

    return (
      <div className="text-center text-gray-500 py-6">Unsupported answers format.</div>
    );
  };

  return (
    <div
      onClick={(e) => {
        e.stopPropagation();
        onClose?.();
      }}
      className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4"
    >
      <div
        onClick={(e) => e.stopPropagation()}
        className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[85vh] overflow-hidden flex flex-col"
      >
        <div className="p-5 border-b bg-gradient-to-r from-emerald-600 to-emerald-700 text-white flex items-center justify-between">
          <div>
            <div className="text-emerald-100 text-xs font-medium">Quality Check</div>
            <div className="text-xl font-semibold">Tester Answers</div>
            {(testerName || checkedAt) && (
              <div className="text-emerald-100 text-xs mt-1">
                {testerName && <span>Tester: {testerName}</span>}
                {testerName && checkedAt && <span className="mx-2">•</span>}
                {checkedAt && (
                  <span>
                    Checked: {new Date(checkedAt).toLocaleString("en-US")}
                  </span>
                )}
              </div>
            )}
          </div>
          <div className="flex items-center gap-2">
            {qualityStatus && (
              <span className={`px-2.5 py-1 rounded-full border text-xs font-medium ${
                qualityStatus === "Confirmed"
                  ? "bg-emerald-50 text-emerald-800 border-emerald-200"
                  : qualityStatus === "Rejected"
                  ? "bg-red-50 text-red-700 border-red-200"
                  : "bg-amber-50 text-amber-800 border-amber-200"
              }`}>
                {qualityStatus}
              </span>
            )}
          </div>
          <button
            onClick={onClose}
            className="text-white/80 hover:text-white hover:bg-white/10 rounded-lg p-2"
          >
            ✕
          </button>
        </div>

        <div className="p-5 overflow-y-auto flex-1 bg-gray-50">
          {renderAnswers()}
          {notes && (
            <div className="mt-5">
              <div className="text-sm font-semibold text-gray-900 mb-2">Tester Notes</div>
              <div className="p-3 rounded-lg border bg-white text-sm text-gray-700 whitespace-pre-wrap">
                {notes}
              </div>
            </div>
          )}
        </div>

        <div className="p-4 border-t bg-white flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 rounded-lg border hover:bg-gray-50"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default QualityAnswersModal;


