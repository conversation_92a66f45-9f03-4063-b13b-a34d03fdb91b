import React, { useState } from 'react';
import { 
  Package, 
  Truck, 
  CheckCircle, 
  Clock, 
  AlertCircle, 
  TrendingUp,
  MapPin,
  Users,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

const ShipmentStats = ({ shipments = [] }) => {
  const [showAllStats, setShowAllStats] = useState(false);
  
  // Calculate statistics
  const totalShipments = shipments.length;
  const deliveredShipments = shipments.filter(s => s.status === 'Delivered').length;
  const inTransitShipments = shipments.filter(s => s.status === 'In Transit').length;
  const pendingShipments = shipments.filter(s => s.status === 'Pending').length;
  const cancelledShipments = shipments.filter(s => s.status === 'Cancelled').length;
  
  const totalQuantity = shipments.reduce((sum, s) => sum + (s.quantity || 0), 0);
  const deliveredQuantity = shipments
    .filter(s => s.status === 'Delivered')
    .reduce((sum, s) => sum + (s.quantity || 0), 0);
  
  // Get unique drivers and branches
  const uniqueDrivers = new Set(shipments.map(s => s.driver)).size;
  const uniqueBranches = new Set(shipments.map(s => s.branch)).size;
  
  // Calculate delivery rate
  const deliveryRate = totalShipments > 0 ? ((deliveredShipments / totalShipments) * 100).toFixed(1) : 0;
  
  // Calculate average quantity per shipment
  const avgQuantityPerShipment = totalShipments > 0 ? (totalQuantity / totalShipments).toFixed(1) : 0;
  
  // Get today's shipments
  const today = new Date();
  const todayShipments = shipments.filter(s => {
    const shipmentDate = new Date(s.timestamps?.created);
    return shipmentDate.toDateString() === today.toDateString();
  }).length;
  
  // Get this week's shipments
  const startOfWeek = new Date(today);
  startOfWeek.setDate(today.getDate() - today.getDay());
  const thisWeekShipments = shipments.filter(s => {
    const shipmentDate = new Date(s.timestamps?.created);
    return shipmentDate >= startOfWeek;
  }).length;

  // Primary stats (always visible)
  const primaryStats = [
    {
      title: 'Total Shipments',
      value: totalShipments,
      icon: Package,
      color: 'bg-blue-500',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-600'
    },
    {
      title: 'Delivered',
      value: deliveredShipments,
      icon: CheckCircle,
      color: 'bg-green-500',
      bgColor: 'bg-green-50',
      textColor: 'text-green-600'
    },
    {
      title: 'In Transit',
      value: inTransitShipments,
      icon: Truck,
      color: 'bg-yellow-500',
      bgColor: 'bg-yellow-50',
      textColor: 'text-yellow-600'
    },
    {
      title: 'Total Quantity',
      value: totalQuantity.toLocaleString(),
      icon: TrendingUp,
      color: 'bg-purple-500',
      bgColor: 'bg-purple-50',
      textColor: 'text-purple-600'
    }
  ];

  // Additional stats (shown when expanded)
  const additionalStats = [
    {
      title: 'Pending',
      value: pendingShipments,
      icon: Clock,
      color: 'bg-orange-500',
      bgColor: 'bg-orange-50',
      textColor: 'text-orange-600'
    },
    {
      title: 'Cancelled',
      value: cancelledShipments,
      icon: AlertCircle,
      color: 'bg-red-500',
      bgColor: 'bg-red-50',
      textColor: 'text-red-600'
    },
    {
      title: 'Active Drivers',
      value: uniqueDrivers,
      icon: Users,
      color: 'bg-indigo-500',
      bgColor: 'bg-indigo-50',
      textColor: 'text-indigo-600'
    },
    {
      title: 'Branches Served',
      value: uniqueBranches,
      icon: MapPin,
      color: 'bg-teal-500',
      bgColor: 'bg-teal-50',
      textColor: 'text-teal-600'
    }
  ];

  const metrics = [
    {
      title: 'Delivery Rate',
      value: `${deliveryRate}%`,
      description: 'Successfully delivered shipments'
    },
    {
      title: 'Avg Quantity/Shipment',
      value: avgQuantityPerShipment,
      description: 'Average items per shipment'
    },
    {
      title: "Today's Shipments",
      value: todayShipments,
      description: 'Shipments created today'
    },
    {
      title: "This Week's Shipments",
      value: thisWeekShipments,
      description: 'Shipments created this week'
    }
  ];




  

  return (
    <div className="space-y-6">
      {/* Main Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {primaryStats.map((stat, index) => {
          const IconComponent = stat.icon;
          return (
            <div
              key={index}
              className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">
                    {stat.title}
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stat.value}
                  </p>
                </div>
                <div className={`${stat.bgColor} p-3 rounded-lg`}>
                  <IconComponent className={`w-6 h-6 ${stat.textColor}`} />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Additional Stats Grid (conditionally shown) */}
      {showAllStats && (
        <>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {additionalStats.map((stat, index) => {
            const IconComponent = stat.icon;
            return (
              <div
                key={index}
                className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">
                      {stat.title}
                    </p>
                    <p className="text-2xl font-bold text-gray-900">
                      {stat.value}
                    </p>
                  </div>
                  <div className={`${stat.bgColor} p-3 rounded-lg`}>
                    <IconComponent className={`w-6 h-6 ${stat.textColor}`} />
                  </div>
                </div>
              </div>
            );
          })}
        </div>

         {/* Additional Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metrics.map((metric, index) => (
          <div
            key={index}
            className="bg-white rounded-xl shadow-sm border border-gray-200 p-6"
          >
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900 mb-1">
                {metric.value}
              </p>
              <p className="text-sm font-medium text-gray-600 mb-1">
                {metric.title}
              </p>
              <p className="text-xs text-gray-500">
                {metric.description}
              </p>
            </div>
          </div>
        ))}
      </div>

      {/* Status Distribution Chart */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Shipment Status Distribution
        </h3>
        <div className="space-y-3">
          {[
            { status: 'Delivered', count: deliveredShipments, color: 'bg-green-500' },
            { status: 'In Transit', count: inTransitShipments, color: 'bg-yellow-500' },
            { status: 'Pending', count: pendingShipments, color: 'bg-orange-500' },
            { status: 'Cancelled', count: cancelledShipments, color: 'bg-red-500' }
          ].map((item, index) => {
            const percentage = totalShipments > 0 ? ((item.count / totalShipments) * 100).toFixed(1) : 0;
            return (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${item.color}`}></div>
                  <span className="text-sm font-medium text-gray-700">
                    {item.status}
                  </span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div
                      className={`${item.color} h-2 rounded-full transition-all duration-300`}
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-600 w-12 text-right">
                    {item.count} ({percentage}%)
                  </span>
                </div>
              </div>
            );
          })}
        </div>
      </div>
        </>
      )}

      {/* Show More/Less Button */}
      <div className="flex justify-center">
        <button
          onClick={() => setShowAllStats(!showAllStats)}
          className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          {showAllStats ? (
            <>
              <ChevronUp className="w-4 h-4" />
              Show Less
            </>
          ) : (
            <>
              <ChevronDown className="w-4 h-4" />
              Show More Stats
            </>
          )}
        </button>
      </div>


    </div>
  );
};

export default ShipmentStats;
