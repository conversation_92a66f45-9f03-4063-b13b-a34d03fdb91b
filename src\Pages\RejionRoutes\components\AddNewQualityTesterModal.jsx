import { useEffect, useMemo, useState } from "react";
import { Eye, EyeOff } from "lucide-react";
import { Select } from "antd";
import regions from "../../../data/regions";
import factories from "../../../data/factories";

const AddNewQualityTesterModal = ({
  onClose,
  onCreate,
  defaultRegionId,
  defaultFactoryId,
}) => {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [status, setStatus] = useState("active");
  const [showPassword, setShowPassword] = useState(false);
  const [selectedRegionId, setSelectedRegionId] = useState(defaultRegionId ?? null);
  const [selectedFactoryId, setSelectedFactoryId] = useState(defaultFactoryId ?? null);

  const selectedRegion = useMemo(
    () => regions.find((r) => r.id === selectedRegionId) || null,
    [selectedRegionId]
  );

  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === "Escape") onClose();
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [onClose]);

  const handleSubmit = () => {
    const trimmedName = name.trim();
    const trimmedEmail = email.trim();
    const trimmedPassword = password.trim();
    if (!trimmedName || !trimmedEmail || !trimmedPassword) return;
    const selectedFactory = factories.find((f) => f.id === selectedFactoryId) || null;
    const payload = {
      id: Date.now(),
      name: trimmedName,
      email: trimmedEmail,
      password: trimmedPassword,
      role: "quality",
      branch: null,
      status,
      region: selectedRegion
        ? { id: selectedRegion.id, name: selectedRegion.name }
        : null,
      factory: selectedFactory
        ? { id: selectedFactory.id, name: selectedFactory.name }
        : null,
      created_at: new Date(),
      updated_at: new Date(),
    };
    onCreate && onCreate(payload);
    onClose && onClose();
  };

  return (
    <div
      onClick={() => onClose()}
      className=" !m-0 fixed  !p-10 inset-0 top-0 bottom-0 left-0 right-0 bg-black bg-opacity-50 flex items-center justify-center z-50 "
    >
      <div
        onClick={(e) => e.stopPropagation()}
        className="bg-white rounded-xl shadow-xl w-full max-w-lg max-h-[95vh] overflow-y-auto custom-scrollbar"
      >
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900">
            Create New Quality Tester
          </h2>
          <p className="text-sm text-gray-600 mt-1">
            Add a new quality tester.
          </p>
        </div>

        <div className="p-6 space-y-5">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Full Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              placeholder="Enter full name"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email <span className="text-red-500">*</span>
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Password <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 pr-10"
                placeholder="Enter password"
              />
              <button
                type="button"
                onClick={() => setShowPassword((v) => !v)}
                className="absolute inset-y-0 right-0 px-3 flex items-center text-gray-500 hover:text-gray-700"
                aria-label={showPassword ? "Hide password" : "Show password"}
              >
                {showPassword ? (
                  <EyeOff className="w-4 h-4" />
                ) : (
                  <Eye className="w-4 h-4" />
                )}
              </button>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Status
            </label>
            <select
              value={status}
              onChange={(e) => setStatus(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            >
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Region</label>
            <Select
              showSearch
              placeholder="Select region"
              value={selectedRegionId}
              onChange={(value) => {
                setSelectedRegionId(value);
                setSelectedFactoryId(null);
              }}
              optionFilterProp="label"
              className="w-full"
              options={regions.map((r) => ({ value: r.id, label: r.name }))}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Factory</label>
            <Select
              showSearch
              placeholder={selectedRegion ? "Select factory" : "Select region first"}
              value={selectedFactoryId}
              onChange={setSelectedFactoryId}
              disabled={!selectedRegion}
              optionFilterProp="label"
              className="w-full"
              options={factories.map((f) => ({ value: f.id, label: f.name }))}
            />
          </div>
        </div>

        <div className="p-6 border-t border-gray-200 flex gap-3">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            className="flex-1 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors disabled:opacity-50"
            disabled={!name.trim() || !email.trim() || !password.trim()}
          >
            Create Quality Tester
          </button>
        </div>
      </div>
    </div>
  );
};

export default AddNewQualityTesterModal;
