import {
  Table,
  Tag as AntdTag,
  Button,
  Space,
  Popconfirm,
  Input,
  Select,
  DatePicker,
  Row,
  Col,
  Card,
} from "antd";
import {
  Eye,
  Edit,
  Trash2,
  Search,
  Filter,
  X,
  Hash,
  Image,
  Package,
  Hash as NumberIcon,
  Building,
  Tag,
  Calendar,
  Settings,
  Search as SearchIcon,
  Filter as FilterIcon,
  Calendar as CalendarIcon,
  Clock,
  AlertTriangle,
} from "lucide-react";
import {
  SearchOutlined,
  FilterOutlined,
  ClearOutlined,
} from "@ant-design/icons";
import { useState, useMemo } from "react";
import { useUser } from "../../../context/UserContext";
import DataTable, {
  getColumnDateProps,
  getColumnFiltersProps,
  getColumnNumberRange,
  getColumnSearchProps,
} from "../../../utils/DataTable";
import TableImage from "../../../utils/TableImage";
import dayjs from "dayjs";
import { formatDate } from "../../../lib/formateDate";
import StoreDetailsModal from "./../../Stores/components/StoreDetailsModal";
import { Link, useNavigate } from "react-router-dom";

const { RangePicker } = DatePicker;

const RejionRoutesTable = ({
  showRouteNameColumn,
  setSelectedShipment,
  data,
  setShipments,
  onEditRejion,
  onAssignRejion,
}) => {
  const { currentUser, setUser, clearUser, isLoggedIn, hasRole } = useUser();

  // Filter states
  const [filters, setFilters] = useState({
    search: "",
    dateRange: null,
  });
  const [selectedStore, setSelectedStore] = useState(null);

  // Normalize regions dataset to rows suitable for the table
  const rows = useMemo(() => {
    return (data || []).map((r) => {
      const routes = Array.isArray(r) ? r : [];
      return r;
    });
  }, [data]);

  // Filter shipments based on current filters
  const filteredRows = useMemo(() => {
    return rows.filter((row) => {
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        const matchesSearch =
          row.name.toLowerCase().includes(searchTerm) ||
          row.id.toString().includes(searchTerm);
        if (!matchesSearch) return false;
      }

      if (filters.dateRange && filters.dateRange.length === 2) {
        const createdDate = dayjs(row.createdAt);
        const startDate = filters.dateRange[0];
        const endDate = filters.dateRange[1];
        if (startDate && createdDate.isBefore(startDate, "day")) return false;
        if (endDate && createdDate.isAfter(endDate, "day")) return false;
      }

      return true;
    });
  }, [rows, filters]);

  const navigate = useNavigate();

  const columns = [
    {
      title: (
        <div className="flex items-center gap-2">
          <Hash className="w-4 h-4 text-gray-600" />
          <span>ID</span>
        </div>
      ),
      dataIndex: "id",
      key: "id",
      fixed: true,
      render: (id) => (
        <Link to={`/shipments?route=${id}`} className="flex items-center gap-2">
          <Hash className="w-4 h-4 text-gray-600" />
          <span>{id}</span>
        </Link>
      ),
      sorter: (a, b) => a.id - b.id,
      ...getColumnNumberRange("id"),
    },
    // Image column removed for regions dataset
    {
      title: (
        <div className="flex items-center gap-2">
          <Package className="w-4 h-4 text-gray-600" />
          <span className="font-bold">Name</span>
        </div>
      ),
      dataIndex: "name",
      key: "name",
      sorter: (a, b) => a.name.localeCompare(b.name),
      ...getColumnSearchProps("name"),
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <Tag className="w-4 h-4 text-gray-600" />
          <span>Stores Points</span>
        </div>
      ),
      dataIndex: "route_stores",
      key: "route_stores",
      render: (route_stores = [], record) => (
        <div
          onClick={() => console.log(record)}
          className="flex flex-wrap gap-1 items-center not-target"
        >
          {Array.isArray(record?.route_stores) &&
          record.route_stores.length > 0 ? (
            record?.route_stores?.map((p, idx) => (
              <div
                onClick={() => setSelectedStore(p)}
                key={idx}
                className="flex items-center"
              >
                <div className="flex items-center px-3 gap-1 bg-orange-100 border hover:bg-orange-500 transition-all cursor-pointer hover:text-white group border-orange-500 p-1 rounded-md">
                  <span className="font-semibold text-xs text-white bg-orange-500 group-hover:bg-white group-hover:text-orange-500 min-w-[20px] flex items-center justify-center rounded-full h-5  mr-1">
                    {idx + 1}
                  </span>
                  <span>{p.name}</span>
                </div>
                {idx < route_stores.length - 1 && (
                  <span className="mx-1 text-orange-500 text-lg font-bolder select-none">
                    →
                  </span>
                )}
              </div>
            ))
          ) : (
            <div className="text-gray-500"> No Routes</div>
          )}
        </div>
      ),
    },

    // Branch, Status and 24h Progress columns removed for regions dataset
    {
      title: (
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-gray-600" />
          <span>Created At</span>
        </div>
      ),
      dataIndex: "createdAt",
      key: "createdAt",
      render: (text, record) => {
        return (
          <div className="flex flex-col gap-2">
            <div className="">
              {formatDate(record.createdAt).split("at")[0]}
            </div>
            <div className="text-xs text-gray-500 ">
              {formatDate(record.createdAt).split("at")[1]}
            </div>
          </div>
        );
      },
      ...getColumnDateProps("createdAt"),
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <Settings className="w-4 h-4 text-gray-600" />
          <span>Actions</span>
        </div>
      ),
      key: "actions",
      render: (_, record) => (
        <Space size="middle" className="not-target">
          <Button
            onClick={() => onAssignRejion && onAssignRejion(record)}
            className="text-green-600 hover:text-green-700"
          >
            Assign
          </Button>
          <Button
            onClick={() => onEditRejion && onEditRejion(record)}
            className="text-blue-600 hover:text-blue-700"
          >
            <Edit className="w-4 h-4" />
          </Button>
          {["super_admin"].includes(currentUser?.user?.role) && (
            <Popconfirm
              title="Delete the route"
              description="Are you sure to delete this route?"
              onConfirm={() => {
                setShipments(data.filter((s) => s.id !== record.id));
              }}
              onCancel={() => {}}
              okText="Delete"
              okButtonProps={{
                danger: true,
              }}
              cancelText="No"
            >
              <Button danger>
                <Trash2 className="w-4 h-4" />
              </Button>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  if (showRouteNameColumn) {
    columns.splice(1, 0, {
      title: (
        <div className="flex items-center gap-2">
          <Package className="w-4 h-4 text-gray-600" />
          <span className="font-bold">Region Name</span>
        </div>
      ),
      dataIndex: "regionName",
      key: "regionName",
      render: (text, record) => {
        return (
          <div className="flex flex-col gap-2 ">
            <div className="">{text}</div>
          </div>
        );
      },
    });
  }

  console.log("columns", columns);

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200">
      <div className="p-6">
        <div className="space-y-4">
          {/* Data Table */}
          <div className="overflow-x-auto">
            <DataTable
              searchPlaceholder={"Search Rejion"}
              onAddClick={() => console.log("add region")}
              table={{
                header: columns,
                rows: filteredRows,
              }}
              rowClassName={(record) =>
                "hover:bg-orange-100 hover:scale-[1.01] transition-all  cursor-pointer"
              }
              onRow={(record, rowIndex) => {
                return {
                  onClick: (e) => {
                    if (e.target.closest(".not-target")) {
                      return;
                    }
                    navigate(`/shipments?route=${record.id}`);
                  },
                };
              }}
            />
          </div>

          {/* Edit modal removed for regions dataset */}
        </div>
      </div>
      {selectedStore && (
        <StoreDetailsModal
          selectedStore={selectedStore}
          setShowDetailsModal={setSelectedStore}
        />
      )}
    </div>
  );
};

export default RejionRoutesTable;
