import React, { useState, useEffect } from "react";
import DataTable, {
  getColumnDateProps,
  getColumnFiltersProps,
  getColumnSearchProps,
} from "./../../utils/DataTable";
import EditBranchModal from "./components/EditBranchModal";
import {
  Store,
  MapPin,
  Phone,
  Mail,
  User,
  Plus,
  Edit,
  Trash2,
  Eye,
  Search,
  MoreVertical,
  Clock,
  Package,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  X,
  Building,
  Grid,
  List,
} from "lucide-react";

import { Table, Tag, Button, Space, Popconfirm, Select } from "antd";
import { EyeOutlined, EditOutlined, DeleteOutlined } from "@ant-design/icons";
import { formatDate } from "../../lib/formateDate";
import AddNewStoreModal from "./components/AddNewStoreModal";
import initialStores from "../../data/stores";
import StoreDetailsModal from "./components/StoreDetailsModal";

const StoresPage = () => {
  // Mock data for stores
  const [stores, setStores] = useState(initialStores);

  const [filteredBranches, setFilteredBranches] = useState(stores);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [showAddModal, setShowAddModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedStore, setSelectedStore] = useState(null);
  const [editingBranch, setEditingBranch] = useState(null);
  const [viewMode, setViewMode] = useState("list"); // 'grid' or 'list'
  const [sortBy, setSortBy] = useState("name");
  const [sortOrder, setSortOrder] = useState("asc");

  // Filter and search stores
  useEffect(() => {
    let filtered = stores.filter((store) => {
      const matchesSearch =
        store.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        store.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
        store.manager.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus =
        statusFilter === "all" || store.status === statusFilter;
      return matchesSearch && matchesStatus;
    });

    // Sort stores
    filtered.sort((a, b) => {
      let aVal = a[sortBy];
      let bVal = b[sortBy];

      if (typeof aVal === "string") {
        aVal = aVal.toLowerCase();
        bVal = bVal.toLowerCase();
      }

      if (sortOrder === "asc") {
        return aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
      } else {
        return aVal > bVal ? -1 : aVal < bVal ? 1 : 0;
      }
    });

    setFilteredBranches(filtered);
  }, [stores, searchTerm, statusFilter, sortBy, sortOrder]);

  const getStatusColor = (status) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "maintenance":
        return "bg-yellow-100 text-yellow-800";
      case "inactive":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case "active":
        return "Active";
      case "maintenance":
        return "Maintenance";
      case "inactive":
        return "Inactive";
      default:
        return "Unknown";
    }
  };

  const getPerformanceColor = (score) => {
    if (score >= 95) return "text-green-600";
    if (score >= 85) return "text-yellow-600";
    return "text-red-600";
  };

  const deleteBranch = (branchId) => {
    setStores(stores.filter((store) => store.id !== branchId));
    setShowDetailsModal(false);
  };

  const updateBranch = (updatedBranch) => {
    setStores(
      stores.map((store) =>
        store.id === updatedBranch.id ? updatedBranch : store
      )
    );
  };

  const handleEditBranch = (store) => {
    setEditingBranch(store);
    setShowEditModal(true);
  };

 

  const activeBranches = stores.filter(
    (store) => store.status === "active"
  ).length;
  const inactiveBranches = stores.filter(
    (store) => store.status === "maintenance"
  ).length;
  const totalShipments = stores.reduce(
    (total, store) => total + store.totalShipments,
    0
  );

  const columns = [
    {
      title: "Store Name",
      dataIndex: "name",
      key: "name",
      fixed: true,
      sorter: (a, b) => a.name.localeCompare(b.name),
      ...getColumnSearchProps("name"),
      render: (text, record) => (
        <div>
          <div className="font-medium text-gray-900">{record.name}</div>
          <div className="text-gray-500 text-sm">Code: {record.storeCode}</div>
        </div>
      ),
    },
    {
      title: "Address Location",
      dataIndex: "address",
      key: "address",
      render: (address) => (
        <span className="text-gray-900">{address?.slice(0, 40)}...</span>
      ),
      sorter: (a, b) => a.address.localeCompare(b.address),
      ...getColumnSearchProps("address"),
    },

    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      render: (status) => (
        <Tag className={getStatusColor(status)}>{getStatusText(status)}</Tag>
      ),
      ...getColumnFiltersProps("status", [
        { text: "Active", value: "active" },
        { text: "Maintenance", value: "maintenance" },
        { text: "Inactive", value: "inactive" },
      ]),
    },
    {
      title: "Shipments",
      dataIndex: "totalShipments",
      key: "shipments",
      render: (text, record) => (
        <div>
          <div className="text-gray-900">{record.totalShipments}</div>
          <div className="text-gray-500 text-sm">
            {record.monthlyShipments} this month
          </div>
        </div>
      ),
    },
  
    {
      title: "Created At",
      dataIndex: "created_at",
      key: "created_at",
      render: (created_at) => <div>{formatDate(created_at)}</div>,
      sorter: (a, b) => a.created_at.localeCompare(b.created_at),
      ...getColumnDateProps("created_at"),
    },
    {
      title: "Updated At",
      dataIndex: "updated_at",
      key: "updated_at",
      render: (updated_at) => <div>{formatDate(updated_at)}</div>,
      sorter: (a, b) => a.updated_at.localeCompare(b.updated_at),
      ...getColumnDateProps("updated_at"),
    },
    {
      title: "Actions",
      key: "actions",
      render: (_, record) => (
        <Space>
          <Button
            icon={<EyeOutlined />}
            className="text-blue-600 hover:text-blue-800"
            onClick={() => {
              setSelectedStore(record);
              setShowDetailsModal(true);
            }}
          />
          <Button
            // type=""
            icon={<EditOutlined />}
            className="text-blue-600 hover:text-blue-800"
            onClick={() => handleEditBranch(record)}
          />
          <Popconfirm
            title="Are you sure you want to delete this store?"
            onConfirm={() => deleteBranch(record.id)}
            okText="Delete"
            okButtonProps={{ danger: true }}
            cancelText="No"
          >
            <Button danger icon={<DeleteOutlined />} />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const table = {
    header: columns,
    rows: filteredBranches,
  };

  return (
    <div className="space-y-6 p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Store Management</h1>
          <p className="text-gray-600 mt-1">
            Manage and track all bakery stores
          </p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          Add New Store
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="bg-blue-100 p-3 rounded-full">
              <Store className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-2xl font-bold text-gray-900">
                {stores.length}
              </p>
              <p className="text-gray-600">Total Stores</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="bg-green-100 p-3 rounded-full">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-2xl font-bold text-gray-900">
                {activeBranches}
              </p>
              <p className="text-gray-600">Active Stores</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="bg-yellow-100 p-3 rounded-full">
              <AlertCircle className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-2xl font-bold text-gray-900">
                {inactiveBranches}
              </p>
              <p className="text-gray-600">Under Maintenance</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="bg-purple-100 p-3 rounded-full">
              <Package className="w-6 h-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-2xl font-bold text-gray-900">
                {totalShipments}
              </p>
              <p className="text-gray-600">Total Shipments</p>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filter Bar */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="w-5 h-5 absolute left-3 top-3 text-gray-400" />
              <input
                type="text"
                placeholder="Search stores..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500"
              />
            </div>
          </div>

          <div className="flex gap-3">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500"
            >
              <option value="all">All Stores</option>
              <option value="active">Active</option>
              <option value="maintenance">Maintenance</option>
              <option value="inactive">Inactive</option>
            </select>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500"
            >
              <option value="name">Sort by Name</option>
              <option value="establishedDate">Established Date</option>
              <option value="totalShipments">Number of Shipments</option>
            </select>

            <div className="flex border border-gray-300 rounded-lg">
              <button
                onClick={() => setViewMode("grid")}
                className={`p-2 ${
                  viewMode === "grid"
                    ? "bg-orange-100 text-orange-600"
                    : "text-gray-600"
                }`}
              >
                <Grid className="w-5 h-5" />
              </button>
              <button
                onClick={() => setViewMode("list")}
                className={`p-2 ${
                  viewMode === "list"
                    ? "bg-orange-100 text-orange-600"
                    : "text-gray-600"
                }`}
              >
                <List className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Stores Grid/List */}
      {viewMode === "grid" ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredBranches.map((store) => (
            <div
              key={store.id}
              className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
            >
              <div className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      {store.name}
                    </h3>
                    <p className="text-gray-500 text-sm">
                      Code: {store.storeCode}
                    </p>
                  </div>
                  <span
                    className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(
                      store.status
                    )}`}
                  >
                    {getStatusText(store.status)}
                  </span>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex items-center text-gray-600">
                    <MapPin className="w-4 h-4 ml-2" />
                    <span className="text-sm">
                      {store.address.slice(0, 50)}...
                    </span>
                  </div>
                </div>

                <div className="flex justify-between items-center text-sm text-gray-500 mb-4">
                  <span>{store.totalShipments} shipments</span>
                  <span>{store.averageDeliveryTime}</span>
                </div>

                <div className="flex gap-2">
                  <button
                    onClick={() => {
                      setSelectedStore(store);
                      setShowDetailsModal(true);
                    }}
                    className="flex-1 bg-orange-600 text-white py-2 px-3 rounded-lg hover:bg-orange-700 transition-colors flex items-center justify-center gap-2 text-sm"
                  >
                    <Eye className="w-4 h-4" />
                    View Details
                  </button>
                  <button className="p-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50">
                    <MoreVertical className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="overflow-x-auto">
            <DataTable searchable={false} table={table} />
          </div>
        </div>
      )}

      {filteredBranches.length === 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
          <Store className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No Stores Found
          </h3>
          <p className="text-gray-500">No stores match the search criteria</p>
        </div>
      )}

      {/* Modals */}
      {showAddModal && (
        <AddNewStoreModal
          showAddModal={showAddModal}
          setShowAddModal={setShowAddModal}
          onSubmit={(newStore) => setStores({ ...stores, newStore })}
        />
      )}
      {showDetailsModal && <StoreDetailsModal selectedStore={selectedStore} setShowDetailsModal={setShowDetailsModal} />}
      {showEditModal && (
        <EditBranchModal
          isOpen={showEditModal}
          onClose={() => setShowEditModal(false)}
          store={editingBranch}
          onUpdate={updateBranch}
          getStatusColor={getStatusColor}
          getStatusText={getStatusText}
        />
      )}
    </div>
  );
};

export default StoresPage;

// Add Store Modal Component
