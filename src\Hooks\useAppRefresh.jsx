import { useEffect, useRef } from 'react';
import { useRefresh } from '../contexts/RefreshContext';

/**
 * Custom hook to listen to app refresh events and trigger data refetching
 * @param {Function} refetchFunction - Function to call when refresh is triggered
 * @param {string} source - Optional source identifier for debugging
 * @param {boolean} enabled - Whether the hook should be enabled (default: true)
 */
export const useAppRefresh = (refetchFunction, source = 'unknown', enabled = true) => {
  const { refreshKey, lastRefreshTime } = useRefresh();
  const lastRefreshKeyRef = useRef(refreshKey);
  const lastRefreshTimeRef = useRef(lastRefreshTime);

  useEffect(() => {
    if (!enabled || !refetchFunction) return;

    // Check if refresh was triggered
    if (refreshKey !== lastRefreshKeyRef.current || lastRefreshTime !== lastRefreshTimeRef.current) {
      console.log(`App refresh detected in ${source}, triggering refetch...`);
      
      // Update refs
      lastRefreshKeyRef.current = refreshKey;
      lastRefreshTimeRef.current = lastRefreshTime;
      
      // Trigger the refetch function
      try {
        refetchFunction();
      } catch (error) {
        console.error(`Error in refetch function for ${source}:`, error);
      }
    }
  }, [refreshKey, lastRefreshTime, refetchFunction, source, enabled]);

  return {
    refreshKey,
    lastRefreshTime,
    isRefreshing: refreshKey !== lastRefreshKeyRef.current,
  };
};
