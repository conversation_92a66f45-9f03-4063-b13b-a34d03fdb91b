import React, { useState, useEffect } from "react";
import { X } from "lucide-react";

const EditBranchModal = ({ 
  isOpen, 
  onClose, 
  branch, 
  onUpdate, 
  getStatusColor, 
  getStatusText 
}) => {
  const [formData, setFormData] = useState({
    name: "",
    storeCode: "",
    address: "",
    openingHours: "07:00 - 22:00",
    status: "active",
  });

  const [errors, setErrors] = useState({});

  // Update form data when branch prop changes
  useEffect(() => {
    if (branch) {
      setFormData({
        name: branch.name || "",
        storeCode: branch.storeCode || "",
        address: branch.address || "",
        phone: branch.phone || "",
        email: branch.email || "",
        manager: branch.manager || "",
        managerPhone: branch.managerPhone || "",
        openingHours: branch.openingHours || "07:00 - 22:00",
        status: branch.status || "active",
      });
      setErrors({});
    }
  }, [branch]);

  const validateForm = () => {
    const newErrors = {};
    if (!formData.name.trim()) newErrors.name = "Branch name is required";
    if (!formData.address.trim()) newErrors.address = "Address is required";
    if (!formData.phone.trim()) newErrors.phone = "Phone number is required";
    if (!formData.manager.trim())
      newErrors.manager = "Manager name is required";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      const updatedBranch = {
        ...branch,
        ...formData,
      };
      onUpdate(updatedBranch);
      onClose();
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ""
      }));
    }
  };

  if (!isOpen || !branch) return null;

  return (
    <div className="fixed overflow-auto !m-0 inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-xl font-bold text-gray-900">Edit Branch</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Branch Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-orange-500 ${
                  errors.name ? "border-red-300" : "border-gray-300"
                }`}
                placeholder="Example: Maadi Branch"
              />
              {errors.name && (
                <p className="text-red-500 text-sm mt-1">{errors.name}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Store Code
              </label>
              <input
                type="text"
                value={formData.storeCode}
                placeholder="12345"
                onChange={(e) => handleInputChange("storeCode", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Address Location <span className="text-red-500">*</span>
              </label>
              <input
                value={formData.address}
                onChange={(e) => handleInputChange("address", e.target.value)}
                rows="3"
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-orange-500 ${
                  errors.address ? "border-red-300" : "border-gray-300"
                }`}
                placeholder="Complete branch address"
              />
              {errors.address && (
                <p className="text-red-500 text-sm mt-1">{errors.address}</p>
              )}
            </div>

          

        
         


            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Opening Hours
              </label>
              <input
                type="text"
                value={formData.openingHours}
                onChange={(e) => handleInputChange("openingHours", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500"
                placeholder="07:00 - 22:00"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Branch Status
              </label>
              <select
                value={formData.status}
                onChange={(e) => handleInputChange("status", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500"
              >
                <option value="active">Active</option>
                <option value="maintenance">Maintenance</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>

          {/* Current Status Display */}
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Current Status</h3>
            <div className="flex items-center gap-2">
              <span
                className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(
                  branch.status
                )}`}
              >
                {getStatusText(branch.status)}
              </span>
              <span className="text-sm text-gray-600">
                Last updated: {new Date(branch.establishedDate).toLocaleDateString()}
              </span>
            </div>
          </div>

          <div className="flex gap-3 mt-8">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
            >
              Update Branch
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditBranchModal;
