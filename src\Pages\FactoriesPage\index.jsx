import React, { useMemo, useState, useEffect } from "react";
import { Plus } from "lucide-react";
import {
  getColumnSearchProps,
  getColumnFiltersProps,
  getColumnDateProps,
} from "../../utils/DataTable";
import AddFactoryModal from "./components/AddFactoryModal";
import EditFactoryModal from "./components/EditFactoryModal";
import { Popconfirm } from "antd";
import regions from "../../data/regions";
import { useNavigate } from "react-router-dom";
import { getAllProductions } from "../../api/apiService";
import FactoriesTable from "./components/FactoriesTable";

const Factories = () => {
  const [factories, setFactories] = useState([]);
  const [addOpen, setAddOpen] = useState(false);
  const [editOpen, setEditOpen] = useState(false);
  const [selectedFactory, setSelectedFactory] = useState(null);

  const navigate = useNavigate();

  const renderProductionsData = async () => {
    try {
      const list = await getAllProductions();
      setFactories(Array.isArray(list.data) ? list.data : []);
    } catch (e) {
      setFactories([]);
    }
  };

  useEffect(() => {
    renderProductionsData();
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">
          Factories Management
        </h1>
        <button
          onClick={() => setAddOpen(true)}
          className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          Add Factory
        </button>
      </div>

      <FactoriesTable
        factories={factories}
        onEdit={(record) => {
          setSelectedFactory(record);
          setEditOpen(true);
        }}
        onDelete={(record) =>
          setFactories((prev) => prev.filter((f) => f.id !== record.id))
        }
        onShowDetails={(record) => {
          setSelectedFactory(record);
          navigate(`/factory-details/${record.id}`, {
            state: { data: record },
          });
        }}
      />

      {addOpen && (
        <AddFactoryModal
          onClose={() => setAddOpen(false)}
          onCreate={() => renderProductionsData()}
        />
      )}

      <EditFactoryModal
        open={editOpen}
        onClose={() => setEditOpen(false)}
        factory={selectedFactory}
        onSave={(updated) => {
          renderProductionsData();
        }}
      />
    </div>
  );
};

export default Factories;
