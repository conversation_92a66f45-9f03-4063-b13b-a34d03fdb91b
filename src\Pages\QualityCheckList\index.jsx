import React, { useMemo, useState } from "react";
import AddQualityQuestionModal from "./components/AddQualityQuestionModal";
import TestQualityModal from "../QualityDashboard/components/TestQualityModal";

export const defaultQuestions = [
  {
    id: "packaging_intact",
    text: "Is the packaging intact with no visible damage?",
    category: "Receiving",
  },
  {
    id: "label_clear",
    text: "Are labels clear and match the shipment details?",
    category: "Receiving",
  },
  {
    id: "temp_control",
    text: "Was temperature controlled during transport (if applicable)?",
    category: "Transport",
  },
  {
    id: "count_matches",
    text: "Do counts match the delivery note/invoice?",
    category: "Verification",
  },
  {
    id: "freshness_ok",
    text: "Is product freshness/quality within acceptable range?",
    category: "Quality",
  },
  {
    id: "storage_conditions",
    text: "Are storage conditions appropriate upon receipt?",
    category: "Storage",
  },
  {
    id: "hygiene_ppe",
    text: "Were hygiene/PPE guidelines followed during handling?",
    category: "Compliance",
  },
];

const answerOptions = [
  { value: "yes", label: "Yes" },
  { value: "no", label: "No" },
];

const QualityCheckList = () => {
  const [query, setQuery] = useState("");
  const [questions, setQuestions] = useState(defaultQuestions);
  const [answers, setAnswers] = useState(() => {
    return defaultQuestions.reduce((acc, q) => {
      acc[q.id] = { answer: "", comment: "" };
      return acc;
    }, {});
  });
  const [modalOpen, setModalOpen] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState(null);

  const filteredQuestions = useMemo(() => {
    const q = query.trim().toLowerCase();
    if (!q) return questions;
    return questions.filter(
      (item) =>
        item.text.toLowerCase().includes(q) ||
        (item.category || "").toLowerCase().includes(q)
    );
  }, [query, questions]);

  const grouped = useMemo(() => {
    return filteredQuestions.reduce((acc, q) => {
      if (!acc[q.category]) acc[q.category] = [];
      acc[q.category].push(q);
      return acc;
    }, {});
  }, [filteredQuestions]);

  const handleAnswerChange = (questionId, value) => {
    setAnswers((prev) => ({
      ...prev,
      [questionId]: { ...prev[questionId], answer: value },
    }));
  };

  const openAddModal = () => {
    setEditingQuestion(null);
    setModalOpen(true);
  };

  const openEditModal = (q) => {
    setEditingQuestion(q);
    setModalOpen(true);
  };

  const handleSaveQuestion = (data) => {
    if (editingQuestion) {
      setQuestions((prev) =>
        prev.map((q) =>
          q.id === editingQuestion.id
            ? { ...q, text: data.text, category: data.category }
            : q
        )
      );
    } else {
      const baseId = (data.text || "question")
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, "_")
        .replace(/^_|_$/g, "");
      const uniqueId = generateUniqueId(baseId, questions);
      const newQ = { id: uniqueId, text: data.text, category: data.category };
      setQuestions((prev) => [...prev, newQ]);
      setAnswers((prev) => ({
        ...prev,
        [uniqueId]: { answer: "", comment: "" },
      }));
    }
    setModalOpen(false);
    setEditingQuestion(null);
  };

  const handleDeleteQuestion = (qid) => {
    const ok = window.confirm("Delete this question?");
    if (!ok) return;
    setQuestions((prev) => prev.filter((q) => q.id !== qid));
    setAnswers((prev) => {
      const copy = { ...prev };
      delete copy[qid];
      return copy;
    });
  };

  function generateUniqueId(baseId, existing) {
    let candidate = baseId || "question";
    let i = 1;
    const existingIds = new Set(existing.map((q) => q.id));
    while (existingIds.has(candidate)) {
      candidate = `${baseId}_${i++}`;
    }
    return candidate;
  }
  const total = filteredQuestions.length;
  const answered = Object.values(answers).filter((a) => a.answer).length;

  return (
    <div className="p-0 md:p-6 lg:p-8">
      <div className="mb-6 space-y-4 flex md:items-center  flex-col md:flex-row  md:justify-between">
        <div className="flex items-center justify-between gap-3">
          <h1 className="   text-lg md:text-2xl font-semibold text-orange-500">
            Quality Check List
          </h1>
          <span className="text-sm text-gray-600">{total} Questions</span>
          
        </div>
        <div className="flex items-center gap-3 !m-0">
          <input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Search questions..."
            className="flex-1 border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
          />
          <button
            type="button"
            onClick={openAddModal}
            className="px-3 py-2 whitespace-nowrap md:hidden rounded-md bg-orange-500 text-white hover:bg-orange-600 text-sm"
          >
            Add 
          </button>
          <button
            type="button"
            onClick={openAddModal}
            className="px-3 py-2 hidden md:block rounded-md bg-orange-500 text-white hover:bg-orange-600 text-sm"
          >
            Add Question
          </button>
        </div>
      </div>

      <form className="space-y-6">
        {Object.entries(grouped).map(([category, categoryQuestions]) => (
          <div key={category} className="bg-white border rounded-lg">
            <div className="px-4 py-3 border-b font-medium bg-gray-50">
              {category}
            </div>
            <div className="divide-y">
              {categoryQuestions.map((q) => (
                <div key={q.id} className="p-4 space-y-3">
                  <label className="font-medium text-gray-800 block">
                    {q.text}
                  </label>

                  <div className="flex flex-wrap gap-4">
                    {answerOptions.map((opt) => (
                      <label
                        key={opt.value}
                        className="inline-flex items-center gap-2"
                      >
                        <input
                          type="radio"
                          name={q.id}
                          value={opt.value}
                          checked={answers[q.id].answer === opt.value}
                          onChange={(e) =>
                            handleAnswerChange(q.id, e.target.value)
                          }
                          className="h-4 w-4 !text-orange-500"
                        />
                        <span className="text-sm">{opt.label}</span>
                      </label>
                    ))}
                  </div>

                  <div className="flex items-center gap-2 pt-1">
                    <button
                      type="button"
                      onClick={() => openEditModal(q)}
                      className="px-3 py-1.5 rounded-md border text-sm hover:bg-gray-50"
                    >
                      Update
                    </button>
                    <button
                      type="button"
                      onClick={() => handleDeleteQuestion(q.id)}
                      className="px-3 py-1.5 rounded-md border border-red-300 text-red-600 text-sm hover:bg-red-50"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </form>
      {modalOpen && (
        <AddQualityQuestionModal
          open={modalOpen}
          onClose={() => {
            setModalOpen(false);
            setEditingQuestion(null);
          }}
          onSave={handleSaveQuestion}
          initialQuestion={editingQuestion}
        />
      )}

    </div>
  );
};

export default QualityCheckList;
