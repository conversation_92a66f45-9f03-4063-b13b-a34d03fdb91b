import { CheckCircle, Package, Plus, Truck } from "lucide-react";
import React, { useState } from "react";
import FactoryShipList from "./components/FactoryShipList";
import AddShipmentModal from "../../Components/Modals/AddNewShipment";
import initialShipments from "../../data/shipments";

// Mock data for demonstration

const FactoryDashboard = () => {
  const [showAddShipment, setShowAddShipment] = useState(false);
  const [shipments, setShipments] = useState(initialShipments);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Factory Dashboard</h1>
        <button
          onClick={() => setShowAddShipment(true)}
          className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          Add New Shipment
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="bg-blue-100 p-3 rounded-full">
              <Package className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-2xl font-bold text-gray-900">
                {shipments.length}
              </p>
              <p className="text-gray-600">Outgoing Shipments</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="bg-yellow-100 p-3 rounded-full">
              <Truck className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-2xl font-bold text-gray-900">
                {shipments.filter((s) => s.status === "In Transit").length}
              </p>
              <p className="text-gray-600">In Transit</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="bg-green-100 p-3 rounded-full">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-2xl font-bold text-gray-900">
                {shipments.filter((s) => s.status === "Delivered").length}
              </p>
              <p className="text-gray-600">Completed</p>
            </div>
          </div>
        </div>
      </div>

      <FactoryShipList shipments={shipments} setShipments={setShipments} />
      {showAddShipment && (
        <AddShipmentModal
          setShipments={setShipments}
          showAddShipment={showAddShipment}
          setShowAddShipment={setShowAddShipment}
          shipments={shipments}
        />
      )}
    </div>
  );
};

export default FactoryDashboard;
