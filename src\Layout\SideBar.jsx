import {
  User,
  Package,
  Truck,
  Store,
  Camera,
  MessageSquare,
  Clock,
  CheckCircle,
  AlertCircle,
  Plus,
  Eye,
  Edit,
  Trash2,
  BarChart3,
  Settings,
  LogOut,
  Bell,
  UserCircle,
  X,
  LocateFixed,
  CheckCheck,
  Route,
  Factory,
} from "lucide-react";
import { useUser } from "../context/UserContext";
import { useLocation, useNavigate } from "react-router-dom";

const Sidebar = ({ currentPage, setCurrentPage, isOpen, onClose }) => {
  const { currentUser, setUser, clearUser, isLoggedIn, hasRole } = useUser();
  const location = useLocation();
  const navigate = useNavigate();
  const menuItems = {
    super_admin: [
      {
        id: "dashboard",
        label: "Dashboard",
        icon: BarChart3,
        path: "/super-admin-dashboard",
      },
      {
        id: "regions",
        label: "Regions",
        icon: LocateFixed,
        path: "/regions",
      },
      {
        id: "all-routes",
        label: "All Routes",
        icon: Route,
        path: "/all-routes",
      },
      {
        id: "factories",
        label: "Factories",
        icon: Factory,
        path: "/factories",
      },
      {
        id: "shipments",
        label: "Shipments",
        icon: Package,
        path: "/shipments",
      },
      {
        id: "quality Chicklist",
        label: "Quality Checklist",
        icon: CheckCheck,
        path: "/quality-checklist",
      },
      { id: "users", label: "Users", icon: User, path: "/users" },
      { id: "admins", label: "Admins", icon: User, path: "/admins" },
      { id: "stores", label: "Stores", icon: Store, path: "/stores" },
      { id: "reports", label: "Reports", icon: BarChart3, path: "/reports" },
      { id: "profile", label: "Profile", icon: UserCircle, path: "/profile" },
    ],
    distributor: [
      {
        id: "/distributor-dashboard",
        label: "Dashboard",
        icon: BarChart3,
        path: "/distributor-dashboard",
      },

      { id: "profile", label: "Profile", icon: UserCircle, path: "/profile" },
    ],

    store: [
      {
        id: "dashboard",
        label: "Dashboard",
        icon: BarChart3,
        path: "/store-dashboard",
      },
      { id: "profile", label: "Profile", icon: UserCircle, path: "/profile" },
    ],

    driver: [
      {
        id: "My Shipments",
        label: "My Shipments",
        icon: BarChart3,
        path: "/driver-dashboard",
      },
      // {
      //   id: "driverShipments",
      //   label: "My Shipments",
      //   icon: Truck,
      //   path: "/driver-shipments",
      // },
      { id: "profile", label: "Profile", icon: UserCircle, path: "/profile" },
    ],
    quality: [
      {
        id: "dashboard",
        label: "Dashboard",
        icon: BarChart3,
        path: "/quality-dashboard",
      },
      // {
      //   id: "incoming",
      //   label: "Incoming Shipments",
      //   icon: Package,
      //   path: "/incoming-shipments",
      // },
      { id: "profile", label: "Profile", icon: UserCircle, path: "/profile" },
    ],
  };

  const handleNavigation = (path) => {
    navigate(path);
    onClose(); // Close sidebar on mobile after navigation
  };

  return (
    <div
      className={`
      fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white shadow-2xl border-r-2 border-orange-500 border-dashed border-l h-full
      transform transition-transform duration-300 ease-in-out
      ${isOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"}
    `}
    >
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="bg-orange-100 p-2 rounded-lg !ml-3">
              <Package className="w-6 h-6 text-orange-600" />
            </div>
            <div>
              <h1 className="font-bold text-gray-900">Bakery Tracking</h1>
              <p className="text-sm text-gray-600">{currentUser?.user?.name}</p>
            </div>
          </div>
          {/* Close button for mobile */}
          <button
            onClick={onClose}
            className="lg:hidden p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-600" />
          </button>
        </div>
      </div>

      <nav className="p-4">
        <ul className="space-y-2">
          {menuItems[currentUser?.user?.role]?.map((item) => {
            const Icon = item.icon;
            return (
              <li key={item.path}>
                <button
                  onClick={() => handleNavigation(item.path)}
                  className={`w-full flex items-center px-3 py-2 rounded-lg text-right transition-colors ${
                    location.pathname === item.path
                      ? "bg-orange-100 text-orange-700"
                      : "text-gray-700 hover:bg-gray-100"
                  }`}
                >
                  <Icon className="w-5 h-5 mr-3" />
                  {item.label}
                </button>
              </li>
            );
          })}
        </ul>
      </nav>

      <div className="absolute bottom-4 left-4 right-4">
        <button
          onClick={() => clearUser()}
          className=" w-full flex items-center px-3 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
        >
          <LogOut className="w-5 h-5 ml-3" />
          Logout
        </button>
      </div>
    </div>
  );
};

export default Sidebar;
