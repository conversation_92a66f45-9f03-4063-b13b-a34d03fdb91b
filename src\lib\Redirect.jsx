import React from "react";
import { Navigate } from "react-router-dom";
import { useUser } from "../context/UserContext";


const Redirect = () => {
  const { currentUser } = useUser();

  return (
    <div>
      <Navigate
        to={
          currentUser?.user?.role === "super_admin"
            ? "/super-admin-dashboard"
            : currentUser?.user?.role === "distributor"
            ? "/distributor-dashboard"
            : currentUser?.user?.role === "quality"
            ? "/quality-dashboard"
            : currentUser?.user?.role === "store"
            ? "/store-dashboard"
            : "/"
        }
        replace={true}
      />
    </div>
  );
};

export default Redirect;
