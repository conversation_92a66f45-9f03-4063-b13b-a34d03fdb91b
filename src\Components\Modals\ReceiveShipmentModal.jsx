import { useState, useEffect } from "react";
import { X, Camera, Upload, CheckCircle, AlertCircle } from "lucide-react";
import MultipleImageUpload from "../../utils/MultipleImageUpload";

const ReceiveShipmentModal = ({ 
  showModal, 
  setShowModal, 
  shipment, 
  onReceiveShipment 
}) => {
  const [images, setImages] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");

  // Reset form when modal opens/closes
  useEffect(() => {
    if (showModal) {
      setImages([]);
      setError("");
      setIsSubmitting(false);
    }
  }, [showModal]);

  // Close modal on ESC key press
  useEffect(() => {
    if (!showModal) return;
    const handleKeyDown = (e) => {
      if (e.key === "Escape") {
        setShowModal(false);
      }
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [showModal, setShowModal]);

  const handleImagesUpload = (uploadedImages) => {
    setImages(uploadedImages);
    setError("");
  };

  const handleSubmit = async () => {
    if (images.length === 0) {
      setError("Please upload at least one image of the received shipment");
      return;
    }

    setIsSubmitting(true);
    setError("");

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Call parent callback with shipment data and images
      if (onReceiveShipment) {
        onReceiveShipment(shipment.id, images);
      }
      
      setShowModal(false);
    } catch (err) {
      setError("Failed to submit shipment receipt. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!showModal || !shipment) return null;

  return (
    <div 
      onClick={() => setShowModal(false)} 
      className="fixed inset-0 !m-0 bg-black bg-opacity-50 flex items-start justify-center z-50 p-4"
    >
      <div 
        onClick={(e) => e.stopPropagation()} 
        className="bg-white rounded-xl shadow-xl w-full max-w-2xl max-h-[95vh] overflow-y-auto"
      >
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold text-gray-900">
                Receive Shipment
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                Upload images of the incoming shipment
              </p>
            </div>
            <button
              onClick={() => setShowModal(false)}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          </div>
        </div>

        {/* Shipment Details */}
        <div className="p-6 border-b border-gray-200 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-medium text-gray-900">{shipment.bakeryType}</h3>
              <p className="text-sm text-gray-600">ID: #{shipment.id}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">
                <span className="font-medium">Quantity:</span> {shipment.quantity}
              </p>
              <p className="text-sm text-gray-600">
                <span className="font-medium">Driver:</span> {shipment.driver}
              </p>
            </div>
          </div>
        </div>

        {/* Image Upload Section */}
        <div className="p-6">
          <MultipleImageUpload
            onImagesUpload={handleImagesUpload}
            label="Received Shipment Images"
            placeholder="Upload images of the received shipment"
            maxImages={10}
            required={true}
            className="mb-4"
          />

          {/* Error Message */}
          {error && (
            <div className="mt-4 flex items-center text-red-600 text-sm">
              <AlertCircle className="w-4 h-4 mr-2" />
              {error}
            </div>
          )}

          {/* Upload Guidelines */}
          <div className="mt-4 p-4 bg-blue-50 rounded-lg">
            <h4 className="text-sm font-medium text-blue-900 mb-2">
              Upload Guidelines:
            </h4>
            <ul className="text-xs text-blue-800 space-y-1">
              <li>• Take clear photos of the shipment packaging</li>
              <li>• Include images showing the condition of items</li>
              <li>• Capture any damage or issues if present</li>
              <li>• Ensure good lighting for clear visibility</li>
            </ul>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 flex gap-3">
          <button
            onClick={() => setShowModal(false)}
            disabled={isSubmitting}
            className="flex-1 px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={isSubmitting || images.length === 0}
            className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Submitting...
              </>
            ) : (
              <>
                <CheckCircle className="w-4 h-4" />
                Submit Receipt
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ReceiveShipmentModal;
