import DriverDashboard from "../Pages/DriverDashboard";
import FactoryDashboard from "../Pages/FactoryDashboard";
import Reports from "../Pages/Reports";
import ShipmentsPage from "../Pages/Shipments";
import SuperAdminDashboard from "../Pages/SuperAdminDashboard";
import UsersPage from "../Pages/UsersPage";
import AdminsPage from "../Pages/AdminsPage";
import Redirect from "../lib/Redirect";
import ImageUploadTest from "../Pages/ImageUploadTest";
import ProfilePage from "../Pages/Profile";
import ProtectedRoute from "./../Components/ProtectedRoute";
import StoresPage from "./../Pages/Stores/index";
import RejionRoutes from "./../Pages/RejionRoutes/index";
import Rejions from "../Pages/Rejions";
import QualityCheckList from "../Pages/QualityCheckList";
import DistributorDashboard from "../Pages/DistributorDashboard";
import QualityDashboard from "../Pages/QualityDashboard";
import StoreDashboard from "../Pages/StoreDashboard";
import AllRoutes from "../Pages/AllRoutes";
import Factories from "../Pages/FactoriesPage";
import FactoryDetails from "../Pages/FactoryDetails";

export const routes = [
  // new route
  {
    path: "/factory-details/:id",
    component: (
      <ProtectedRoute requiredRoles={["super_admin", "admin"]}>
        <FactoryDetails />
      </ProtectedRoute>
    ),
    nameEn: "Factory",
  },
  // new route
  {
    path: "/factories",
    component: (
      <ProtectedRoute requiredRoles={["super_admin", "admin"]}>
        <Factories />
      </ProtectedRoute>
    ),
    nameEn: "Factory",
  },
  // new route
  {
    path: "/all-routes",
    component: (
      <ProtectedRoute requiredRoles={["super_admin", "admin"]}>
        <AllRoutes />
      </ProtectedRoute>
    ),
    nameEn: "Factory",
  },
  // new route
  {
    path: "/store-dashboard",
    component: (
      <ProtectedRoute requiredRoles={["store"]}>
        <StoreDashboard />
      </ProtectedRoute>
    ),
    nameEn: "Factory",
  },
  // new route
  {
    path: "/distributor-dashboard",
    component: (
      <ProtectedRoute requiredRoles={["distributor"]}>
        <DistributorDashboard />
      </ProtectedRoute>
    ),
    nameEn: "Factory",
  },
  // new route
  {
    path: "/quality-checklist",
    component: (
      <ProtectedRoute requiredRoles={["super_admin", "admin"]}>
        <QualityCheckList />
      </ProtectedRoute>
    ),
    nameEn: "Factory",
  },
  // new route
  {
    path: "/regions",
    component: (
      <ProtectedRoute requiredRoles={["super_admin", "admin"]}>
        <Rejions />
      </ProtectedRoute>
    ),
    nameEn: "Factory",
  },
  // new route

  {
    path: "/regions/:id",
    component: (
      <ProtectedRoute requiredRoles={["super_admin", "admin"]}>
        <RejionRoutes />
      </ProtectedRoute>
    ),
    nameEn: "Factory",
  },
  {
    path: "/factory",
    component: (
      <ProtectedRoute requiredRoles={["factory"]}>
        <FactoryDashboard />
      </ProtectedRoute>
    ),
    nameEn: "Factory",
  },

  {
    path: "/driver-dashboard",
    component: (
      <ProtectedRoute requiredRoles={["driver"]}>
        <DriverDashboard />
      </ProtectedRoute>
    ),
    nameEn: "Driver",
  },

  {
    path: "/quality-dashboard",
    component: (
      <ProtectedRoute requiredRoles={["quality"]}>
        <QualityDashboard />
      </ProtectedRoute>
    ),
    nameEn: "Branch",
  },
  {
    path: "/shipments",
    component: (
      <ProtectedRoute requiredRoles={["super_admin", "admin"]}>
        <ShipmentsPage />
      </ProtectedRoute>
    ),
    nameEn: "Shipments",
  },
  {
    path: "/users",
    component: (
      <ProtectedRoute requiredRoles={["super_admin" , "admin"]}>
        <UsersPage />
      </ProtectedRoute>
    ),
    nameEn: "Users",
  },
  {
    path: "/admins",
    component: (
      <ProtectedRoute requiredRoles={["super_admin"]}>
        <AdminsPage />
      </ProtectedRoute>
    ),
    nameEn: "Admins",
  },
  {
    path: "/super-admin-dashboard",
    component: (
      <ProtectedRoute requiredRoles={["super_admin" , "admin"]}>
        <SuperAdminDashboard />
      </ProtectedRoute>
    ),
    nameEn: "Super Admin",
  },
  {
    path: "/reports",
    component: (
      <ProtectedRoute requiredRoles={["super_admin" , "admin"]}>
        <Reports />
      </ProtectedRoute>
    ),
    nameEn: "Reports",
  },
  {
    path: "/stores",
    component: (
      <ProtectedRoute requiredRoles={["super_admin" , "admin"]}>
        <StoresPage />
      </ProtectedRoute>
    ),
    nameEn: "stores",
  },
  {
    path: "/image-upload-test",
    component: <ImageUploadTest />,
    nameEn: "Image Upload Test",
  },
  // {
  //   path: "/driver-dashboard",
  //   component: (
  //     <ProtectedRoute requiredRoles={["super_admin", "driver"]}>
  //       <AllDriverShipments />
  //     </ProtectedRoute>
  //   ),
  //   nameEn: "All Driver Shipments",
  // },
  {
    path: "/profile",
    component: (
      <ProtectedRoute
        requiredRoles={[
          "super_admin",
          "factory",
          "driver",
          "branch",
          "distributor",
          "store",
          "quality",
        ]}
      >
        <ProfilePage />
      </ProtectedRoute>
    ),
    nameEn: "Profile",
  },

  {
    path: "*",
    component: <Redirect />,
    nameEn: "Not Found",
  },
];
