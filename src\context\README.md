# UserContext

A React context for managing current user state with localStorage persistence.

## Features

- **Persistent Storage**: User data is automatically saved to and loaded from localStorage
- **Type Safety**: Includes proper error handling and validation
- **Utility Methods**: Helper functions for common user operations
- **Loading State**: Built-in loading state management

## Usage

### 1. Import the hook

```jsx
import { useUser } from '../context/UserContext';
```

### 2. Use in your component

```jsx
function MyComponent() {
  const { 
    currentUser, 
    setUser, 
    clearUser, 
    updateUser, 
    isLoggedIn, 
    hasRole, 
    belongsToBranch, 
    isLoading 
  } = useUser();

  // Your component logic here
}
```

## Available Methods

### State
- `currentUser`: The current user object (null if not logged in)
- `isLoading`: Boolean indicating if user data is being loaded from localStorage

### Actions
- `setUser(user)`: Set the current user and save to localStorage
- `clearUser()`: Clear the current user and remove from localStorage
- `updateUser(updatedData)`: Update specific properties of the current user

### Utilities
- `isLoggedIn()`: Returns true if a user is currently logged in
- `hasRole(role)`: Returns true if the current user has the specified role
- `belongsToBranch(branchName)`: Returns true if the current user belongs to the specified branch

## User Object Structure

```javascript
{
  id: number,
  name: string,
  role: 'driver' | 'branch' | 'factory',
  branch: string | null
}
```

## Example Usage

```jsx
import { useUser } from '../context/UserContext';

function LoginComponent() {
  const { setUser, isLoggedIn, hasRole } = useUser();

  const handleLogin = (user) => {
    setUser(user);
  };

  const handleLogout = () => {
    clearUser();
  };

  if (isLoggedIn()) {
    return (
      <div>
        <p>Welcome, {currentUser?.user?.name}!</p>
        {hasRole('driver') && <p>You are a driver</p>}
        <button onClick={handleLogout}>Logout</button>
      </div>
    );
  }

  return (
    <div>
      <button onClick={() => handleLogin(someUser)}>Login</button>
    </div>
  );
}
```

## Error Handling

The context includes error handling for localStorage operations. If localStorage is unavailable or corrupted, the context will gracefully handle the error and continue functioning without persistence.
