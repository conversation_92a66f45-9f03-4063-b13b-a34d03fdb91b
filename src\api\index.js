import axios from "axios";

export const baseURL = "https://dd-ops.com/donuttracking/api";


const token = JSON.parse(localStorage.getItem("currentUser"))?.token;
console.log("tokentokentoken", token);

const API = axios.create({
  baseURL,
  headers: {
    // "Content-Type": "application/json",
    "Authorization":`Bearer ${token}`,
  },
});

API.interceptors.response.use(
  (res) => res,
  (err) => {
    const status = err?.response?.status;
    if (status === 401 || status === 403) {
      // Clear any client-side token cache if you keep one
      localStorage.removeItem("currentUser");
      // Avoid infinite loops
      if (window.location.pathname !== "/login") {
        window.location.replace("/login");
      }
    }
    return Promise.reject(err);
  }
);

export default API;
