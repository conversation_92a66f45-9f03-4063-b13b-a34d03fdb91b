import {
  Store,
  Package,
  CheckCheck,
  X,
  MapPin,
  CarIcon,
  ArrowRight,
  ChevronLeft,
  ChevronRight,
  Minus,
  Hash,
  MapPinIcon,
  FactoryIcon,
  RouteIcon,
  Calendar,
  Clock,
} from "lucide-react";
import React, { useState } from "react";
import QualityAnswersModal from "./QualityAnswersModal";
import { useUser } from "../../context/UserContext";
import { Image } from "antd";
import { formatDate } from "../../lib/formateDate";

const ShipmentDetailsModal = ({
  selectedShipment,
  setSelectedShipment,
  setShowDetailsModal = () => false,
}) => {
  if (!selectedShipment) return null;


  console.log("selectedShipment", selectedShipment);

  const [showQualityModal, setShowQualityModal] = useState(false);
  const [activeTab, setActiveTab] = useState("details"); // State for active tab
  const { hasRole , currentUser } = useUser();
  

  // Normalize fields from varying backend shapes
  const bakeryType =
    selectedShipment?.bakeryType || selectedShipment?.bakery_type || "-";
  const quantity = selectedShipment?.quantity;
  const vehicleNumber =
    selectedShipment?.vehicleNumber ||
    selectedShipment?.vehicle?.vehicle_number ||
    selectedShipment?.vehicle_number ||
    "-";
  const regionName =
    typeof selectedShipment?.region === "string"
      ? selectedShipment?.region
      : selectedShipment?.route?.region?.name ||
        selectedShipment?.region?.name ||
        "-";
  const productionName =
    selectedShipment?.productionName ||
    selectedShipment?.production?.name ||
    "-";
  const routeName =
    typeof selectedShipment?.route === "string"
      ? selectedShipment?.route
      : selectedShipment?.route?.name || "-";
  const distributorName =
    selectedShipment?.distributor?.name || selectedShipment?.distributor || "-";
  const stores = Array.isArray(selectedShipment?.route?.route_stores)
    ? selectedShipment?.route.route_stores
    : Array.isArray(selectedShipment?.store_names)
    ? selectedShipment?.store_names
    : [];

  const storesCount = stores.length;
  const storeNames = stores.map((rs) => rs?.store?.name || rs).filter(Boolean);

  const createdAtRaw =
    selectedShipment?.timestamps?.created ||
    selectedShipment?.createdAt ||
    selectedShipment?.created_at;

  const qualityCheckedAtRaw =
    Array.isArray(selectedShipment?.checklist_answers) &&
    selectedShipment?.checklist_answers.length > 0
      ? selectedShipment?.checklist_answers[0]?.created_at
      : null;

  const createdAtDate = createdAtRaw ? new Date(createdAtRaw) : null;
  const qualityCheckedAtDate = qualityCheckedAtRaw
    ? new Date(qualityCheckedAtRaw)
    : null;

  const qualityNotedAtRaw = selectedShipment?.quality_noted_at;
  const qualityNotedAtDate = qualityNotedAtRaw
    ? new Date(qualityNotedAtRaw)
    : null;
  const timelineTimestamps = {
    created: createdAtDate,
    qualityCheckedAt: qualityNotedAtDate,
    checkListCreatedAt: qualityCheckedAtDate,
  };
  const qualityStatus =
    selectedShipment?.qualityStatus ||
    (typeof selectedShipment?.quality_status === "string"
      ? selectedShipment?.quality_status.charAt(0).toUpperCase() +
        selectedShipment?.quality_status.slice(1).toLowerCase()
      : undefined);

  const stages = [
    {
      key: "created",
      label: "Issued from Distributor",
      icon: Package,
      status: "completed",
    },
    {
      key: "checkListCreatedAt",
      label: "Quality Check",
      icon: CheckCheck,
      status:
        qualityStatus.toLowerCase() === "pending"
          ? "Pending"
          : qualityStatus.toLowerCase() === "confirmed"
          ? "Confirmed"
          : "Rejected",
    },
  ];

  // A helper variable to determine if the tickets tab should be shown
  const showTicketsTab =
    hasRole("super_admin") &&
    Array.isArray(selectedShipment?.issues) &&
    selectedShipment?.issues.length > 0;

  const getStatusBadge = (status) => {
    const colors = {
      completed: "bg-emerald-50 text-emerald-700 border-emerald-200",
      Confirmed: "bg-emerald-50 text-emerald-700 border-emerald-200",
      Pending: "bg-amber-50 text-amber-700 border-amber-200",
      Rejected: "bg-red-50 text-red-700 border-red-200",
    };
    return colors[status] || "bg-gray-50 text-gray-700 border-gray-200";
  };

  return (
    <div
      onClick={(e) => {
        e.stopPropagation();
        setShowDetailsModal(false);
        setSelectedShipment(null);
      }}
      className="fixed inset-0 !m-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-in fade-in duration-200"
    >
      <div
        onClick={(e) => e.stopPropagation()}
        className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col animate-in zoom-in-95 duration-200"
      >
        {/* Header */}
        <div className="relative bg-gradient-to-r from-blue-600 to-blue-700 p-6 text-white">
          <div className="flex justify-between items-start">
            <div className="w-full mt-3">
              <p className="text-blue-100 text-sm font-medium mb-1">
                Shipment Tracking
              </p>
              <div className="flex items-center justify-between w-full gap-3">
                <h2 className="text-2xl font-bold">
                  #{selectedShipment?.id} - {bakeryType}
                </h2>
                <h2 className="text-lg text-slate-200 ">
                  {createdAtDate &&
                  createdAtDate instanceof Date &&
                  !isNaN(createdAtDate.getTime())
                    ? createdAtDate.toLocaleString("en-GB", {
                        day: "2-digit",
                        month: "short",
                        year: "numeric",
                        hour: "2-digit",
                        minute: "2-digit",
                      })
                    : "Invalid Date"}
                </h2>
              </div>
            </div>
            <button
              onClick={(e) => {
                e.stopPropagation();
                setSelectedShipment(null);
                setShowDetailsModal(false);
              }}
              className="text-white/80 hover:text-white hover:bg-white/10 rounded-lg p-2 transition-all duration-200"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="overflow-y-auto flex-1 p-6">
          {/* Tabs Navigation */}
          <div className="border-b border-gray-200 mb-6">
            <nav className="-mb-px flex space-x-6" aria-label="Tabs">
              <button
                onClick={() => setActiveTab("details")}
                className={`whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                  activeTab === "details"
                    ? "border-blue-600 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                Shipment Details
              </button>
              <button
                onClick={() => setActiveTab("tickets")}
                className={`whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm transition-colors duration-200 flex items-center ${
                  activeTab === "tickets"
                    ? "border-blue-600 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                Store Complaints
                <span className="ml-2 bg-blue-100 text-blue-700 text-xs font-semibold px-2.5 py-0.5 rounded-full">
                  {selectedShipment?.issues.length}
                </span>
              </button>
            </nav>
          </div>

          {/* Tab Content */}
          <div>
            {/* Details Tab */}
            {activeTab === "details" && (
              <div className={`grid grid-cols-1 ${hasRole("store") ?"lg:!grid-cols-1":"lg:!grid-cols-2"} gap-6 animate-in fade-in duration-300"`}>
                {/* Shipment Info Cards */}
                <div className="space-y-4">
                  <h3 className="font-semibold text-gray-900 text-lg mb-4">
                    Shipment Details
                  </h3>
                  <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-100">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="bg-blue-600 rounded-lg p-2">
                        <Package className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <p className="text-xs text-gray-600 font-medium">
                          Bakery Type
                        </p>
                        <p className="font-semibold text-gray-900">
                          {bakeryType}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center justify-between pt-3 border-t border-blue-200">
                      <span className="text-sm text-gray-600">Quantity</span>
                      <span className="font-bold text-blue-700 text-lg">
                        {quantity}
                      </span>
                    </div>
                  </div>
                  <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-4 border border-purple-100">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="bg-purple-600 rounded-lg p-2">
                        <CarIcon className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <p className="text-xs text-gray-600 font-medium">
                          Vehicle Number
                        </p>
                        <p className="font-semibold text-gray-900">
                          {vehicleNumber}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-4 border border-green-100">
                    <div className="flex items-start gap-3 mb-3">
                      <div className="bg-green-600 rounded-lg p-2">
                        <MapPin className="w-5 h-5 text-white" />
                      </div>
                      <div className="flex-1">
                        <p className="text-xs text-gray-600 font-bold mb-2">
                          Location
                        </p>
                        <div className="space-y-2">
                          <div className="flex gap-7 items-center">
                            <span className="text-sm text-gray-600 flex items-center gap-1">
                              <MapPinIcon className="w-4 h-4 text-green-600" />
                              Region:
                            </span>
                            <span className="font-medium text-gray-900">
                              {regionName}
                            </span>
                          </div>
                          {!hasRole("store") && (

                            <div className="flex gap-7 items-center">
                            <span className="text-sm text-gray-600 flex items-center gap-1">
                              <FactoryIcon className="w-4 h-4 text-purple-600" />
                              Production:
                            </span>
                            <span className="font-medium text-gray-900">
                              {productionName}
                            </span>
                          </div>
                          )}
                          <div className="flex gap-7 items-center">
                            <span className="text-sm text-gray-600 flex items-center gap-1">
                              <RouteIcon className="w-4 h-4 text-blue-600" />
                              Route:
                            </span>
                            <span className="font-medium text-gray-900">
                              {routeName}
                            </span>
                          </div>
                          {
                           !hasRole("store") &&  
                          <div className="flex gap-7 items-center">
                            <span className="text-sm text-gray-600 flex items-center gap-1">
                              <Hash className="w-4 h-4 text-orange-600" />
                              Distributor:
                            </span>
                            <span className="font-medium text-gray-900">
                              {distributorName}
                            </span>
                          </div>
                          }
                          {
                            !hasRole("store") && (
                          <div className="flex gap-7 items-center">
                            <span className="text-sm text-gray-600 flex items-center gap-1">
                              <Calendar className="w-4 h-4 text-indigo-600" />
                              Exit Date:
                            </span>
                            <span className="font-medium text-sm text-gray-900">
                              {formatDate(selectedShipment?.exit_time) || "-"}
                            </span>
                          </div>

                            )
                          }

                          <div className="flex gap-7 items-center">
                            <span className="text-sm text-gray-600 flex items-center gap-1">
                              <Calendar className="w-4 h-4 !whitespace-nowrap text-purple-600" />
                              Created At:
                            </span>
                            <span className="font-medium text-gray-900 font-mono text-sm">
                              {formatDate(
                                selectedShipment?.rawCreatedAt || createdAtRaw
                              )}
                            </span>
                          </div>
                        {
                          !hasRole("store") && (
                            <div className="flex gap-7">
                            <span className="text-sm text-gray-600">
                              Stores ({storesCount || 0}):
                            </span>
                            <div className="flex flex-wrap gap-x-2 gap-y-4">
                              {(storeNames || []).map((storeName, index) => {
                                return (
                                  <div
                                    key={index}
                                    className="flex items-center"
                                  >
                                    <span className="relative bg-orange-100 px-2 text-sm py-1 rounded-lg border border-orange-600 whitespace-nowrap text-gray-900">
                                      {storeName}
                                      <div className="absolute top-0 left-0 -translate-x-1/2 -translate-y-1/2 bg-orange-600 rounded-full w-4 h-4 flex items-center justify-center text-white text-xs">
                                        {index + 1}
                                      </div>
                                    </span>
                                  </div>
                                );
                              })}
                              {(!storeNames || storeNames.length === 0) && (
                                <span className="text-sm text-gray-500 italic">
                                  No stores assigned
                                </span>
                              )}
                            </div>
                          </div>
                          )
                        }
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Timeline */}
              {
                !hasRole("store") &&   <div>
                <h3 className="font-semibold text-gray-900 text-lg mb-4">
                  Tracking Timeline
                </h3>
                {["confirmed", "rejected"].includes(qualityStatus?.toLowerCase()) && (
                  <div className="mb-4">
                    <button
                      onClick={() => setShowQualityModal(true)}
                      className="px-3 py-2 rounded-lg bg-emerald-600 hover:bg-emerald-700 text-white text-sm font-medium shadow"
                    >
                      View Quality Answers
                    </button>
                  </div>
                )}
                {["confirmed", "rejected"].includes(qualityStatus?.toLowerCase()) && (
                  <div className="mb-4">
                    <div className="text-sm font-semibold text-gray-900 mb-2">
                      Quality Notes
                    </div>
                    <div className="p-3 rounded-lg border bg-white text-sm text-gray-700 whitespace-pre-wrap">
                      {selectedShipment?.quality_note || "No notes provided."}
                    </div>
                  </div>
                )},
                {

                <div className="relative">
                  <div className="absolute left-4 top-8 bottom-8 w-0.5 bg-gradient-to-b from-gray-200 via-gray-300 to-gray-200"></div>
                  <div className="space-y-6">
                    {stages.map((stage) => {
                      const Icon = stage.icon;
                      const isCompleted =
                        stage.status === "completed" ||
                        stage.status === "Confirmed";
                      const isPending = stage.status === "Pending";
                      const isRejected = stage.status === "Rejected";
                      const timestamp = timelineTimestamps[stage.key];

                      return (
                        <div
                          key={stage.key}
                          className="relative flex items-start gap-4"
                        >
                          <div className="relative z-10 flex-shrink-0">
                            <div
                              className={`w-10 h-10 rounded-full flex items-center justify-center shadow-lg transition-all duration-300 ${
                                isCompleted
                                  ? "bg-gradient-to-br from-emerald-400 to-emerald-600"
                                  : isPending
                                  ? "bg-gradient-to-br from-amber-400 to-amber-600"
                                  : isRejected
                                  ? "bg-gradient-to-br from-red-400 to-red-600"
                                  : "bg-gray-200"
                              }`}
                            >
                              <Icon className="w-5 h-5 text-white" />
                            </div>
                            {isPending && (
                              <div className="absolute inset-0 rounded-full bg-amber-400 animate-ping opacity-75"></div>
                            )}
                          </div>
                          <div className="flex-1 bg-white rounded-xl p-4 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200">
                            <div className="flex items-start justify-between gap-3 mb-2">
                              <p className="font-medium text-gray-900">
                                {stage.label}
                              </p>
                              <span
                                className={`text-xs px-2.5 py-1 rounded-full border font-medium whitespace-nowrap ${getStatusBadge(
                                  stage.status
                                )}`}
                              >
                                {stage.status === "completed"
                                  ? "Completed"
                                  : stage.status}
                              </span>
                            </div>
                            {timestamp &&
                              timestamp instanceof Date &&
                              !isNaN(timestamp.getTime()) && (
                                <div className="flex items-center gap-2 mt-2 pt-2 border-t border-gray-100">
                                  <svg
                                    className="w-4 h-4 text-gray-400"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                                    />
                                  </svg>
                                  <span className="text-xs text-gray-600 font-medium">
                                    {timestamp.toLocaleString("en-US", {
                                      month: "short",
                                      day: "numeric",
                                      year: "numeric",
                                      hour: "2-digit",
                                      minute: "2-digit",
                                    })}
                                  </span>
                                </div>
                              )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
                }
              </div>
              }
              </div>
            )}

            {/* Tickets Tab */}
            {activeTab == "tickets" && (
              <div className="animate-in fade-in duration-300">
                <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-4 border border-gray-200">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="bg-gray-700 rounded-lg p-2">
                      <Store className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="text-xs text-gray-600 font-medium">
                        Stores Complaint tickets
                      </p>
                      <p className="font-semibold text-gray-900">
                        {selectedShipment?.issues.length} stores
                      </p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    {selectedShipment?.issues.map((s, idx) => (
                      <div>
                        <div
                          key={idx}
                          className="n gap-3 p-3 rounded-lg border bg-white shadow-sm"
                        >
                          <div className="flex items-start justify-betwee">
                            <div className="flex-1">
                              <div className="font-medium text-gray-900">
                                {s?.store_name}
                              </div>
                              {s?.note && (
                                <div className="text-sm text-gray-600 mt-1 whitespace-pre-wrap">
                                  {s?.note}
                                </div>
                              )}
                            </div>
                            <div className="flex flex-col items-end gap-1 flex-shrink-0">
                              <p className="text-xs flex items-center gap-1 text-gray-500 mt-1">
                                <Hash className="w-3.5 h-3.5" />
                                <span className="font-medium text-gray-800">
                                  {s?.store_code}
                                </span>
                              </p>
                              {s.vehicle_number && (
                                <p className="text-xs flex items-center gap-1 text-gray-500 mt-1">
                                  <CarIcon className="w-3.5 h-3.5" />
                                  <span className="font-medium text-gray-800">
                                    {s.vehicle_number}
                                  </span>
                                </p>
                              )}
                              <span className="text-[11px] text-gray-500 mt-1">
                                {s?.created_at ? (
                                  <span>
                                    {" "}
                                    <span className="font-bold text-black">
                                      created at :
                                    </span>{" "}
                                    {new Date(s.created_at).toLocaleString(
                                      "en-US"
                                    )}
                                  </span>
                                ) : (
                                  "-"
                                )}
                              </span>
                              <span className="text-[11px] text-gray-500 mt-1">
                                {s?.delivery_datetime ? (
                                  <span>
                                    {" "}
                                    <span className="font-bold text-black">
                                      delivery time
                                    </span>{" "}
                                    :{" "}
                                    {new Date(
                                      s.delivery_datetime
                                    ).toLocaleString("en-US")}
                                  </span>
                                ) : (
                                  "-"
                                )}
                              </span>
                            </div>
                          </div>
                          <div className=" mt-5 grid grid-cols-3 gap-2">
                            {s.photos &&
                              s.photos.length > 0 &&
                              s?.photos.map((img, idx) => (
                                <Image
                                  className=" rounded-xl cursor-pointer hover:scale-105 transition-all"
                                  src={
                                    "https://dd-ops.com/donuttracking" +
                                    img.photo_url
                                  }
                                  key={idx}
                                  alt={`complaint-image-${idx}`}
                                />
                              ))}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {["confirmed", "rejected"].includes(qualityStatus.toLowerCase()) && (
        <QualityAnswersModal
          isOpen={showQualityModal}
          onClose={() => setShowQualityModal(false)}
          answers={selectedShipment?.checklist_answers}
          testerName={selectedShipment?.qualityTester}
          checkedAt={selectedShipment?.quality_noted_at}
          qualityStatus={qualityStatus}
          notes={selectedShipment?.quality_note}
        />
      )}
    </div>
  );
};

export default ShipmentDetailsModal;
