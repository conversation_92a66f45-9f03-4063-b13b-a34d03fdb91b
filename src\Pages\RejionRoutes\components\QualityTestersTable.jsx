import {
  Table,
  Tag as AntdTag,
  Button,
  Space,
  Popconfirm,
  DatePicker,
  Modal,
  Input,
} from "antd";
import {
  Edit,
  Trash2,
  Hash,
  Package,
  Tag,
  Calendar,
  Settings,
  Factory,
  LocateFixed,
  LocateFixedIcon,
  Lock,
} from "lucide-react";
import { useMemo, useState } from "react";
import { useUser } from "../../../context/UserContext";
import DataTable, {
  getColumnDateProps,
  getColumnNumberRange,
  getColumnSearchProps,
} from "../../../utils/DataTable";
import dayjs from "dayjs";
import { formatDate } from "../../../lib/formateDate";
import EditQualityTesterModal from "./EditQualityTesterModal";

const { RangePicker } = DatePicker;

const QualityTestersTable = ({
  data,
  setData,
  onEdit,
  onDelete,
  hiddenColumns,
}) => {
  const { currentUser } = useUser();
  const [rowData, setRowData] = useState(null);
  const [revealMap, setRevealMap] = useState({});
  const [isRevealModalOpen, setIsRevealModalOpen] = useState(false);
  const [revealTarget, setRevealTarget] = useState(null);
  const [revealInput, setRevealInput] = useState("");
  const rows = useMemo(() => {
    console.log("testersData", data);
    const source = Array.isArray(data) && data.length > 0 ? data : [];
    return source.map((u) => ({
      id: u.id,
      name: u.name,
      factory: u.factory,
      region: u.region,
      email: u.email,
      password: u.password,
      status: u.status,
      createdAt: u.created_at || new Date(),
    }));
  }, [data]);

  const columns = [
    {
      title: (
        <div className="flex items-center gap-2">
          <Hash className="w-4 h-4 text-gray-600" />
          <span>ID</span>
        </div>
      ),
      dataIndex: "id",
      key: "id",
      fixed: true,
      render: (id) => (
        <div className="flex items-center gap-2">
          <Hash className="w-4 h-4 text-gray-600" />
          <span>{id}</span>
        </div>
      ),
      sorter: (a, b) => a.id - b.id,
      ...getColumnNumberRange("id"),
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <Package className="w-4 h-4 text-gray-600" />
          <span className="font-bold">Name</span>
        </div>
      ),
      dataIndex: "name",
      key: "name",
      sorter: (a, b) => a.name.localeCompare(b.name),
      ...getColumnSearchProps("name"),
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <Factory className="w-4 h-4 text-gray-600" />
          <span className="font-bold">Factory</span>
        </div>
      ),
      dataIndex: "factory",
      key: "factory",
      render: (factory) => (
        <div className="flex items-center gap-2">
          <Factory className="w-4 h-4 text-gray-600" />
          <span>{factory?.name}</span>
        </div>
      ),
      sorter: (a, b) => a.factory.localeCompare(b.factory),
      // sorter: (a, b) => a.name.localeCompare(b.name),
      // ...getColumnSearchProps("name"),
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <LocateFixed className="w-4 h-4 text-gray-600" />
          <span className="font-bold">Reigon</span>
        </div>
      ),
      dataIndex: "region",
      key: "region",
      render: (region) => (
        <div className="flex items-center gap-2">
          <LocateFixedIcon className="w-4 h-4 text-gray-600" />
          <span>{region?.name}</span>
        </div>
      ),
      sorter: (a, b) => a.name.localeCompare(b.name),
      // sorter: (a, b) => a.name.localeCompare(b.name),
      // ...getColumnSearchProps("name"),
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <Tag className="w-4 h-4 text-gray-600" />
          <span>Email</span>
        </div>
      ),
      dataIndex: "email",
      key: "email",
      ...getColumnSearchProps("email"),
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <Lock className="w-4 h-4 text-gray-600" />
          <span>Password</span>
        </div>
      ),
      dataIndex: "password",
      key: "password",
      render: (password, record) => {
        const isRevealed = !!revealMap[record.id];
        return (
          <div className="flex items-center gap-2">
            <Lock className="w-4 h-4 text-gray-600" />
            <span>{isRevealed ? password : "••••••••"}</span>
            {!isRevealed && (
              <Button
                type="link"
                onClick={() => {
                  setRevealTarget(record);
                  setRevealInput("");
                  setIsRevealModalOpen(true);
                }}
                className="p-0"
              >
                Show
              </Button>
            )}
          </div>
        );
      },
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <Tag className="w-4 h-4 text-gray-600" />
          <span>Status</span>
        </div>
      ),
      dataIndex: "status",
      key: "status",
      render: (status) => (
        <AntdTag color={status === "active" ? "green" : "red"}>
          {status}
        </AntdTag>
      ),
      filters: [
        { text: "Active", value: "active" },
        { text: "Inactive", value: "inactive" },
      ],
      onFilter: (value, record) => record.status === value,
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-gray-600" />
          <span>Created At</span>
        </div>
      ),
      dataIndex: "createdAt",
      key: "createdAt",
      render: (text, record) => {
        return (
          <div className="flex flex-col gap-2">
            <div className="">
              {formatDate(record.createdAt).split("at")[0]}
            </div>
            <div className="text-xs text-gray-500 ">
              {formatDate(record.createdAt).split("at")[1]}
            </div>
          </div>
        );
      },
      ...getColumnDateProps("createdAt"),
    },
    {
      title: (
        <div className="flex items-center gap-2">
          <Settings className="w-4 h-4 text-gray-600" />
          <span>Actions</span>
        </div>
      ),
      key: "actions",
      render: (_, record) => (
        <Space size="middle">
          <Button
            onClick={() => {
              onEdit && onEdit(record);
              setRowData(record);
            }}
            className="text-blue-600 hover:text-blue-700"
          >
            <Edit className="w-4 h-4" />
          </Button>
          {["super_admin"].includes(currentUser?.user?.role) && (
            <Popconfirm
              title="Delete quality tester"
              description="Are you sure you want to delete this tester?"
              onConfirm={() => {
                setData((prev) => prev.filter((t) => t.id !== record.id));
              }}
              okText="Delete"
              okButtonProps={{ danger: true }}
              cancelText="Cancel"
            >
              <Button danger>
                <Trash2 className="w-4 h-4" />
              </Button>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ].filter((c) => !hiddenColumns.includes(c.dataIndex));

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200">
      <div className="p-6">
        <div className="space-y-4">
          <div className="overflow-x-auto">
            <DataTable
              searchPlaceholder={"Search for quality testers"}
              onAddClick={() => console.log("add new quality tester")}
              table={{
                header: columns,
                rows: rows,
              }}
            />
          </div>
        </div>
      </div>

      <Modal
        title="Enter your password to reveal"
        open={isRevealModalOpen}
        onOk={() => {
          if (!revealTarget) return;
          // In a real app, validate revealInput against the current user's password
          // Here we just require a non-empty input to proceed
          if (!revealInput) return;
          setRevealMap((prev) => ({ ...prev, [revealTarget.id]: true }));
          setIsRevealModalOpen(false);
          setRevealInput("");
          setRevealTarget(null);
          // Auto-hide after 30 seconds
          setTimeout(() => {
            setRevealMap((prev) => {
              const copy = { ...prev };
              delete copy[revealTarget?.id];
              return copy;
            });
          }, 30000);
        }}
        okText="Reveal"
        onCancel={() => {
          setIsRevealModalOpen(false);
          setRevealInput("");
          setRevealTarget(null);
        }}
      >
        <Input.Password
          autoFocus
          placeholder="Your password"
          value={revealInput}
          onChange={(e) => setRevealInput(e.target.value)}
        />
      </Modal>

      <EditQualityTesterModal
        tester={rowData}
        setRowData={setRowData}
        onClose={() => setRowData(null)}
        onSave={(updated) => {
          setData((prev) =>
            prev.map((t) => (t.id === updated.id ? updated : t))
          );
          setRowData(null);
        }}
      />
    </div>
  );
};

export default QualityTestersTable;
