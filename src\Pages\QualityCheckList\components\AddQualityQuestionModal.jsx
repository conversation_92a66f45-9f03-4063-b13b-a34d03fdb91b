import React, { useEffect, useState } from "react";

const CATEGORIES = [
  "Receiving",
  "Transport",
  "Verification",
  "Quality",
  "Storage",
  "Compliance",
];

const AddQualityQuestionModal = ({ open, onClose, onSave, initialQuestion }) => {
  const [text, setText] = useState("");
  const [category, setCategory] = useState(CATEGORIES[0]);
  const [error, setError] = useState("");

  useEffect(() => {
    if (!open) return;
    if (initialQuestion) {
      setText(initialQuestion.text || "");
      setCategory(initialQuestion.category || CATEGORIES[0]);
    } else {
      setText("");
      setCategory(CATEGORIES[0]);
    }
    setError("");
  }, [open, initialQuestion]);

  if (!open) return null;

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!text.trim()) {
      setError("Question text is required");
      return;
    }
    onSave({ text: text.trim(), category });
  };

  return (
    <div className="fixed overflow-auto inset-0 z-50 flex items-center justify-center">
      <div className="absolute inset-0 bg-black/30" onClick={onClose} />
      <div className="relative bg-white w-[95%] max-w-lg rounded-lg shadow-lg">
        <div className="px-4 py-3 border-b flex items-center justify-between">
          <h2 className="font-semibold text-lg">
            {initialQuestion ? "Update Question" : "Add Question"}
          </h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">✕</button>
        </div>
        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          <div className="space-y-1">
            <label className="text-sm font-medium">Question</label>
            <input
              type="text"
              value={text}
              onChange={(e) => setText(e.target.value)}
              placeholder="Enter question text"
              className="w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
            />
          </div>
          <div className="space-y-1">
            <label className="text-sm font-medium">Category</label>
            <select
              value={category}
              onChange={(e) => setCategory(e.target.value)}
              className="w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
            >
              {CATEGORIES.map((c) => (
                <option key={c} value={c}>{c}</option>
              ))}
            </select>
          </div>
          {error ? <div className="text-sm text-red-600">{error}</div> : null}

          <div className="flex items-center justify-end gap-2 pt-2">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 rounded-md border text-sm hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 rounded-md bg-orange-500 text-white text-sm hover:bg-orange-600"
            >
              {initialQuestion ? "Update" : "Add"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddQualityQuestionModal;


