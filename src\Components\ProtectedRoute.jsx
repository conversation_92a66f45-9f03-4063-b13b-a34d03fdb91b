import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useUser } from '../context/UserContext';
import Redirect from '../lib/Redirect';

const ProtectedRoute = ({ 
  children, 
  requiredRole = null, 
  requiredRoles = null, // Array of allowed roles
  requiredBranch = null,
  requiredBranches = null, // Array of allowed branches
  fallbackPath = '/login' 
}) => {
  const { currentUser, isLoading, hasRole, belongsToBranch } = useUser();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }
  
  
  // If user is not logged in, redirect to login page
  if (!currentUser) {
    return <Navigate to={fallbackPath} state={{ from: location }} replace />;
  }

  // Check role permissions (supports both single role and multiple roles)
  const checkRoleAccess = () => {
    if (requiredRoles && Array.isArray(requiredRoles)) {
      // Check if user has any of the required roles
      return requiredRoles.some(role => hasRole(role)); // flase
    } else if (requiredRole) {
      // Check single role
      return hasRole(requiredRole);
    }
    return true; // No role requirement
  };

  // Check branch permissions (supports both single branch and multiple branches)
  const checkBranchAccess = () => {
    if (requiredBranches && Array.isArray(requiredBranches)) {
      // Check if user belongs to any of the required branches
      return requiredBranches.some(branch => belongsToBranch(branch));
    } else if (requiredBranch) {
      // Check single branch
      return belongsToBranch(requiredBranch);
    }
    return true; // No branch requirement
  };

  // Check if user has required role access
  if (!checkRoleAccess()) { 
    const allowedRoles = requiredRoles || [requiredRole];
    return (
   <Redirect />
    );
  }

  // Check if user has required branch access
  if (!checkBranchAccess()) {
    const allowedBranches = requiredBranches || [requiredBranch];
    return (
      <Redirect />
    );
  }

  // If all checks pass, render the protected content
  return children;
};

// Higher-order component for easier usage
export const withProtectedRoute = (Component, options = {}) => {
  return (props) => (
    <ProtectedRoute {...options}>
      <Component {...props} />
    </ProtectedRoute>
  );
};

// Hook for checking permissions in components
export const usePermissions = () => {
  const { currentUser, hasRole, belongsToBranch } = useUser();

  return {
    canAccess: (requiredRole = null, requiredRoles = null, requiredBranch = null, requiredBranches = null) => {
      if (!currentUser) return false;
      
      // Check role access
      if (requiredRoles && Array.isArray(requiredRoles)) {
        if (!requiredRoles.some(role => hasRole(role))) return false;
      } else if (requiredRole && !hasRole(requiredRole)) {
        return false;
      }
      
      // Check branch access
      if (requiredBranches && Array.isArray(requiredBranches)) {
        if (!requiredBranches.some(branch => belongsToBranch(branch))) return false;
      } else if (requiredBranch && !belongsToBranch(requiredBranch)) {
        return false;
      }
      
      return true;
    },
    hasRole,
    belongsToBranch,
    isLoggedIn: !!currentUser
  };
};

export default ProtectedRoute;