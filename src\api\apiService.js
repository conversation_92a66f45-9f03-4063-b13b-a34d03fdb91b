// apiService.js
import { createAPIPayload } from "./apiPayload";
import API from "./index";

// Example: Fetch data

export const login = async (endpoint, data) => {
  const payload = await createAPIPayload(data);
  if (!payload) throw new Error("User not authenticated");
  return API.post(endpoint, payload);
};

//  DATA APIS

export const getVihiclesByRegionId = async (regionId) => {
  const payload = await createAPIPayload();
  if (!payload) throw new Error("User not authenticated");
  return API.get(`/vehicles/${regionId}`)
    .then((response) => response.data)
    .catch((error) => {
      console.log(error);
    });
};

export const getProductionsByRegionId = async (regionId) => {
  const payload = await createAPIPayload();
  if (!payload) throw new Error("User not authenticated");
  return API.get(`/productions/region/${regionId}`)
    .then((response) => response.data)
    .catch((error) => {
      console.log(error);
    });
};

export const getAllRoutes = async () => {
  const payload = await createAPIPayload();
  if (!payload) throw new Error("User not authenticated");
  return API.get("/routes/all-routes", payload)
    .then((response) => response.data)
    .catch((error) => {
      console.log(error);
    });
};

export const getStoresByRouteId = async (routeId) => {
  const payload = await createAPIPayload();
  if (!payload) throw new Error("User not authenticated");
  return API.get(`/routes/${routeId}/stores`)
    .then((response) => response.data)
    .catch((error) => {
      console.log(error);
    });
};

export const getCheckListQuestions = async () => {
  const payload = await createAPIPayload();
  if (!payload) throw new Error("User not authenticated");
  return API.get("/checklist-questions")
    .then((response) => response.data)
    .catch((error) => {
      console.log(error);
    });
};

export const getProductionsData = async () => {
  const payload = await createAPIPayload();
  if (!payload) throw new Error("User not authenticated");
  return API.get("/productions")
    .then((response) => response.data)
    .catch((error) => {
      console.log(error);
    });
};

// DISTRIBUTER APIS
export const getDistributerShipments = async () => {
  const payload = await createAPIPayload();
  if (!payload) throw new Error("User not authenticated");
  return API.get("/shipments/shipments-for-distributor", payload)
    .then((response) => response.data)
    .catch((error) => {
      console.log(error);
    });
};

export const getDistributersByRegionId = async (regionId) => {
  const payload = await createAPIPayload();
  if (!payload) throw new Error("User not authenticated");
  return API.get(`/regions/${regionId}/distributors`)
    .then((response) => response.data)
    .catch((error) => {
      console.log(error);
    });
};

// QUALITY APIS
export const getQualityShipments = async (page) => {
  const payload = await createAPIPayload();
  if (!payload) throw new Error("User not authenticated");
  return API.post(`/quality/shipments?page=${page}`)
    .then((response) => response.data)
    .catch((error) => {
      console.log(error);
    });
};

// STORE APIS
export const getStoreShipments = async (params = {}) => {
  const payload = await createAPIPayload();
  if (!payload) throw new Error("User not authenticated");
  return API.get("/shipments/shipments-for-store", params)
    .then((response) => response.data)
    .catch((error) => {
      console.log(error);
    });
};

export const AddStoreIssue = async (params = {}) => {
  const payload = await createAPIPayload();
  if (!payload) throw new Error("User not authenticated");
  return API.post("/issues/insert", params)
    .then((response) => response.data)
    .catch((error) => {
      console.log(error);
    });
};

export const qualityAnswerChickList = async (data) => {
  const payload = await createAPIPayload(data);
  if (!payload) throw new Error("User not authenticated");
  return API.post("/quality/shipments/add-answers", payload);
};

// Helpers: confirm or reject quality checklist answers
export const confirmQualityChecklist = async (data) => {
  return qualityAnswerChickList({ ...data, quality_status: "confirmed" });
};

export const rejectQualityChecklist = async (data) => {
  return qualityAnswerChickList({ ...data, quality_status: "rejected" });
};

export const getRegionRoutes = async (regId) => {
  const payload = await createAPIPayload();
  if (!payload) throw new Error("User not authenticated");
  return API.get(`/routes/region/${regId}`)
    .then((response) => response.data)
    .catch((error) => {
      console.log(error);
    });
};

// +++++++++++++++++++++++++++++++++++++++++++++
// ==============Super Admin APIs===============
// +++++++++++++++++++++++++++++++++++++++++++++
export const getAllShipmements = async (params = {}, page = 1) => {
  const payload = await createAPIPayload(params);
  if (!payload) throw new Error("User not authenticated");
  return API.get(`shipments/get-all-shipments?page=${page}`, payload)
    .then((response) => response.data)
    .catch((error) => {
      console.log(error);
    });
};

export const getAllRegions = async () => {
  const payload = await createAPIPayload();
  if (!payload) throw new Error("User not authenticated");
  return API.get("/regions/all-regions")
    .then((response) => response.data)
    .catch((error) => {
      console.log(error);
    });
};

export const updateShipment = async (id, data) => {
  const payload = await createAPIPayload(data);
  if (!payload) throw new Error("User not authenticated");
  return API.put(`/shipments/update-shipment/${id}`, payload);
};

export const updateRegion = async (id, data) => {
  const payload = await createAPIPayload(data);
  if (!payload) throw new Error("User not authenticated");
  return API.put(`regions/edit-region/${id}`, payload);
}

export const DeleteRegion = async (id) => {
  const payload = await createAPIPayload();
  if (!payload) throw new Error("User not authenticated");
  return API.delete(`/regions/delete-region/${id}`);
}



export const getAllProductions = async (params = {}) => {
  const payload = await createAPIPayload(params);
  if (!payload) throw new Error("User not authenticated");
  return API.get("/productions/all-productions", payload)
    .then((response) => response.data)
    .catch((error) => {
      console.log(error);
    });
}

export const deleteShipment = async (id) => {
  const payload = await createAPIPayload();
  if (!payload) throw new Error("User not authenticated");
  return API.delete(`/shipments/delete-shipment/${id}`);
};

export const createRegion = async (data) => {
  const payload = await createAPIPayload(data);
  if (!payload) throw new Error("User not authenticated");
  return API.post(`/regions/add-region`, payload);
}

export const createProduction = async (data) => {
  const payload = await createAPIPayload(data);
  if (!payload) throw new Error("User not authenticated");
  return API.post(`/productions/add-production`, payload);
}


export const updateProduction = async (id, data) => {
  const payload = await createAPIPayload(data);
  if (!payload) throw new Error("User not authenticated");
  return API.put(`/productions/edit/${id}`, payload);
}


export const deleteProduction  = async (id) => {
  const payload = await createAPIPayload();
  if (!payload) throw new Error("User not authenticated");
  return API.delete(`/productions/delete-production/${id}`, payload);

}



export const fetchData = (endpoint, params = {}) => {
  return API.get(endpoint, { params });
};

// Example: Create data
export const postData = (endpoint, data) => {
  return API.post(endpoint, data);
};

// Example: Update data
export const updateData = (endpoint, data) => {
  return API.put(endpoint, data);
};

// Example: Delete data
export const deleteData = (endpoint) => {
  return API.delete(endpoint);
};

// Shipment API
export const CreateShipment = async (data) => {
  const payload = await createAPIPayload(data);
  if (!payload) throw new Error("User not authenticated");
  return API.post("/shipments/add-shipments", payload);
};

export const GetShipments = async (params = {}) => {
  const payload = await createAPIPayload(params);
  if (!payload) throw new Error("User not authenticated");
  return API.post("/shipments/show_one", payload);
};

export const updateCollection = async (data) => {
  const payload = await createAPIPayload(data);
  if (!payload) throw new Error("User not authenticated");
  return API.post(`/collections/update.php`, payload);
};

export const deleteCollection = async (id) => {
  const payload = await createAPIPayload({ id });
  if (!payload) throw new Error("User not authenticated");
  return API.post(`/collections/delete.php`, payload);
};
