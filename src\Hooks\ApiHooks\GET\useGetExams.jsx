import { useState, useEffect, useCallback, useLayoutEffect } from 'react';
import { getExams } from '../../../api/apiService';

export const useGetExams = (params = {}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [data, setData] = useState(null);

  const fetchExams = useCallback(async (newParams = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await getExams({ ...params, ...newParams });
      setData(response.data);
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.message || err.message || 'حدث خطأ أثناء جلب بيانات الامتحانات';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [params]);

  // Auto-fetch on mount if required params are provided
  useLayoutEffect(() => {
    if (params.user_id && params.generation_id) {
      fetchExams();
    }
  }, [params.user_id, params.generation_id]);

  return {
    exams: data?.data?.exams || [],
    totalExams: data?.data?.total_exams || 0,
    loading,
    error,
    fetchExams,
    reset: () => {
      setError(null);
      setData(null);
    }
  };
};
