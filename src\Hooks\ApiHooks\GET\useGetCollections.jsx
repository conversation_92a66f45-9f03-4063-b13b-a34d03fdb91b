import { useState, useEffect } from 'react';
import { getCollections } from '../../../api/apiService';

export const useGetCollections = (params = {}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [data, setData] = useState([]);

  const fetchCollections = async (fetchParams = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await getCollections({ ...params, ...fetchParams });
      setData(response.data);
      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.message || err.message || 'حدث خطأ أثناء جلب المجموعات';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCollections();
  }, []);

  return {
    collections: data,
    loading,
    error,
    refetch: fetchCollections,
    reset: () => {
      setError(null);
      setData([]);
    }
  };
};
