import React, { useEffect, useState } from "react";
import { Plus } from "lucide-react";
import { useUser } from "../../context/UserContext";
import { getAllRegions } from "../../api/apiService";
import stores from "../../data/stores";
// import AssignModal from "./components/AssignModa";
// import UpdateRejionModal from "./components/UpdateRejionModal";
// import RejionsStats from "./components/RejionStats";
import RejionsTable from "./components/RejonsTable";
import UpdateRejionModal from "./components/UpdateRejionModal";
import AddNewRejion from "./components/AddNewRejionModal";

const Rejions = () => {
  const { currentUser, setUser, clearUser, isLoggedIn, hasRole } = useUser();
  const [selectedShipment, setSelectedShipment] = useState(null);
  const [regions, setRejions] = useState([]);
  const [assignOpen, setAssignOpen] = useState(false);
  const [selectedRejion, setSelectedRejion] = useState(null);
  const [updateOpen, setUpdateOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [addRejionModal, setAddRejionModal] = useState(false);

  const handleCreateRegion = (region) => {
    setRejions((prev) => [...prev, region]);
  };

  const getRegionsData = async () => {
    try {
      setLoading(true);
      const data = await getAllRegions().finally(() => {
        setLoading(false);
      });
      // Ensure array and basic normalization

      setRejions(data?.data);
    } catch (e) {
      console.error("Failed to load regions", e);
    }
  };

  useEffect(() => {
    getRegionsData();
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Rejions Management</h1>
        {currentUser?.user?.role === "super_admin" && (
          <button
            onClick={() => setAddRejionModal(true)}
            className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add Region
          </button>
        )}
      </div>

      {/* Shipment Statistics */}
      {/* <RejionsStats regions={regions} /> */}

      <RejionsTable
        loading={loading}
        setSelectedShipment={setSelectedShipment}
        data={regions}
        setShipments={setRejions}
        onEditRejion={(region) => {
          setSelectedRejion(region);
          console.log("region", region);
          setUpdateOpen(true);
        }}
        onDeleteRegion={(region) => {
          getRegionsData();
        }}
        onAssignRejion={(region) => {
          setSelectedRejion(region);
          setAssignOpen(true);
        }}
      />

      <AddNewRejion
        open={addRejionModal}
        onClose={() => setAddRejionModal(false)}
        onCreate={(payload) => handleCreateRegion(payload)}
      />

      <UpdateRejionModal
        open={updateOpen}
        onClose={() => setUpdateOpen(false)}
        region={selectedRejion}
        onSave={(updated) => {
          setRejions((prev) =>
            prev.map((r) => (r.id === updated.id ? updated : r))
          );
        }}
      />
    </div>
  );
};

export default Rejions;
