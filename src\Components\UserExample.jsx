import { useUser } from '../context/UserContext';
import users from '../data/users';

const UserExample = () => {
  const { 
    currentUser, 
    setUser, 
    clearUser, 
    updateUser, 
    isLoggedIn, 
    hasRole, 
    belongsToBranch, 
    isLoading 
  } = useUser();

  const handleLogin = (user) => {
    setUser(user);
  };

  const handleLogout = () => {
    clearUser();
  };

  const handleUpdateUser = () => {
    if (currentUser) {
      updateUser({ name: currentUser?.user?.name + ' (Updated)' });
    }
  };

  if (isLoading) {
    return <div className="p-4">Loading user data...</div>;
  }

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">User Context Example</h2>
      
      {isLoggedIn() ? (
        <div className="bg-green-100 p-4 rounded-lg mb-4">
          <h3 className="text-lg font-semibold mb-2">Current User:</h3>
          <p><strong>Name:</strong> {currentUser?.user?.name}</p>
          <p><strong>Role:</strong> {currentUser?.user?.role}</p>
          <p><strong>Branch:</strong> {currentUser.branch || 'N/A'}</p>
          
          <div className="mt-4 space-x-2">
            <button 
              onClick={handleUpdateUser}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              Update User
            </button>
            <button 
              onClick={handleLogout}
              className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
            >
              Logout
            </button>
          </div>
        </div>
      ) : (
        <div className="bg-gray-100 p-4 rounded-lg mb-4">
          <h3 className="text-lg font-semibold mb-2">No user logged in</h3>
          <p>Select a user to login:</p>
        </div>
      )}

      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Available Users:</h3>
        {users.map(user => (
          <div key={user.id} className="flex items-center justify-between bg-white p-3 rounded border">
            <div>
              <p className="font-medium">{user.name}</p>
              <p className="text-sm text-gray-600">Role: {user.role} | Branch: {user.branch || 'N/A'}</p>
            </div>
            <button
              onClick={() => handleLogin(user)}
              className="bg-green-500 text-white px-3 py-1 rounded hover:bg-green-600"
            >
              Login
            </button>
          </div>
        ))}
      </div>

      <div className="mt-6 p-4 bg-gray-50 rounded">
        <h3 className="font-semibold mb-2">Context Methods:</h3>
        <ul className="text-sm space-y-1">
          <li><strong>isLoggedIn():</strong> {isLoggedIn() ? 'true' : 'false'}</li>
          <li><strong>hasRole('driver'):</strong> {hasRole('driver') ? 'true' : 'false'}</li>
          <li><strong>hasRole('factory'):</strong> {hasRole('factory') ? 'true' : 'false'}</li>
          <li><strong>hasRole('branch'):</strong> {hasRole('branch') ? 'true' : 'false'}</li>
          {currentUser?.branch && (
            <li><strong>belongsToBranch('{currentUser.branch}'):</strong> {belongsToBranch(currentUser.branch) ? 'true' : 'false'}</li>
          )}
        </ul>
      </div>
    </div>
  );
};

export default UserExample;
