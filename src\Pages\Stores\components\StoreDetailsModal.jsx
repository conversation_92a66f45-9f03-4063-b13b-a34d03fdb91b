import { Building, Clock, Edit, Package, Trash2, X } from "lucide-react";

// Store Details Modal
const StoreDetailsModal = ({ selectedStore, setShowDetailsModal }) => {
  const getStatusColor = (status) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "maintenance":
        return "bg-yellow-100 text-yellow-800";
      case "inactive":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };
  const getStatusText = (status) => {
    switch (status) {
      case "active":
        return "Active";
      case "maintenance":
        return "Maintenance";
      case "inactive":
        return "Inactive";
      default:
        return "Unknown";
    }
  };

  if (!selectedStore) return null;

  return (
    <div className="fixed overflow-auto !m-0 inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-xl font-bold text-gray-900">
            {selectedStore.name} Details
          </h2>
          <button
            onClick={() => setShowDetailsModal(false)}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6">
          <div className="">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
              <div className="space-y-6 col-span-1 border border-gray-200 rounded-lg">
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 mb-3 flex items-center">
                    <Building className="w-5 h-5 ml-2" />
                    Basic Information
                  </h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Name:</span>
                      <span className="font-medium">{selectedStore.name}</span>
                    </div>

                    <div className="flex justify-between items-start">
                      <span className="text-gray-600">Address:</span>
                      <span className="font-medium text-right max-w-xs">
                        {selectedStore.address}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Status:</span>
                      <span
                        className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(
                          selectedStore.status
                        )}`}
                      >
                        {getStatusText(selectedStore.status)}
                      </span>
                    </div>
                    <div className="flex justify-between items-start">
                      <span className="text-gray-600">Region:</span>
                      <span className="font-medium text-right max-w-xs">
                        {selectedStore?.region?.name}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <h3 className="font-medium text-gray-900 mb-3 flex items-center">
                  <Package className="w-5 h-5 ml-2" />
                  Shipment Statistics
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-start">
                    <p className="text-2xl font-bold text-blue-600">
                      {selectedStore.totalShipments}
                    </p>
                    <p className="text-gray-600 text-sm">Total Shipments</p>
                  </div>
                  <div className="text-end">
                    <p className="text-2xl font-bold text-green-600">
                      {selectedStore.monthlyShipments}
                    </p>
                    <p className="text-gray-600 text-sm">This Month</p>
                  </div>
                </div>
                <div className="mt-4">
                  {selectedStore.lastShipment && (
                    <div className="flex justify-between mt-2">
                      <span className="text-gray-600">Last Shipment:</span>
                      <span className="font-medium">
                        {new Date(
                          selectedStore.lastShipment
                        ).toLocaleDateString("en-US")}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="bg-gray-50 rounded-lg p-4 col-span-3">
              <h3 className="font-medium text-gray-900 mb-3 flex items-center">
                <Clock className="w-5 h-5 ml-2" />
                Additional Information
              </h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">Opening Hours:</span>
                  <span className="font-medium">
                    {selectedStore.openingHours}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 mt-8">
            <button
              onClick={() => {
                // handleEditBranch(selectedBranch);
                setShowDetailsModal(false);
              }}
              className="flex-1 bg-orange-600 text-white py-2 px-4 rounded-lg hover:bg-orange-700 transition-colors flex items-center justify-center gap-2"
            >
              <Edit className="w-4 h-4" />
              Edit Store
            </button>
            <button
              onClick={() => deleteBranch(selectedStore.id)}
              className="flex-1 bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors flex items-center justify-center gap-2"
            >
              <Trash2 className="w-4 h-4" />
              Delete Store
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StoreDetailsModal;
