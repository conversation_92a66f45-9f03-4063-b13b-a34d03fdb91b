export const formatDate = (date) => {
  if (!date) return ""; // تجنّب الأخطاء لو التاريخ فاضي

  const sampleDate = new Date(date);

  const formatted = sampleDate.toLocaleDateString("en-US", {
    weekday: "long", // يوم الأسبوع
    year: "numeric", // السنة
    month: "long", // الشهر (اسم كامل)
    day: "numeric", // اليوم
    hour: "numeric", // الساعة
    minute: "numeric", // الدقيقة
    second: "numeric", // الثانية
  });

  return formatted;
};