// Mock data for demonstration (arrays for each status images)
const initialShipments = [
  {
    id: 1,
    bakeryType: "White Bread",
    quantity: 100,
    driver: "<PERSON>",
    branch: "Maadi Branch",
    region: "Riyadh",
    route: "Route A1",
    vehicleNumber: "CAI-1023",
    status: "In Transit",
    qualityStatus: "Pending",
    storeStatus: "Pending",
    qualityNotes: "",
    storesTickets: null,
    comments: [
      { user: "Factory", message: "Sealed 10 boxes.", time: "08:45" },
      { user: "Driver", message: "Picked up from gate A.", time: "09:05" },
    ],
    timestamps: {
      created: new Date("2024-01-15T08:00:00"),
      driverPickup: new Date("2024-01-15T09:00:00"),
      delivery: null,
      branchReceive: null,
      display: null,
      qualityCheckedAt: null,
      storeStatusUpdatedAt: null,
    },
  },
  {
    id: 2,
    bakeryType: "Cakes",
    quantity: 50,
    driver: "<PERSON><PERSON><PERSON>",
    branch: "Riyadh Branch",
    region: "Riyadh",
    route: "Route N2",
    vehicleNumber: "CAI-2044",
    status: "Delivered",
    qualityStatus: "Confirmed",
    storeStatus: "Confirmed",
    qualityNotes: "All checks passed. Packaging intact and counts verified.",
    storesTickets: [
      {
        store: "Riyadh Branch",
        storeCode:"12345",
        status: "Confirmed",
        updatedAt: new Date("2024-01-15T10:35:00"),
        notes: "Goods received and damaged.",
        vehicleNumber: "ABC-1234",
        images: [
          "https://res.cloudinary.com/dbzn1y8rt/image/upload/v1759838107/bf5hero39mrmoaua16yi.jpg",
          "https://res.cloudinary.com/dbzn1y8rt/image/upload/v1759838090/gf9hcztqqdxlr2ivmuhe.jpg",
          "https://res.cloudinary.com/dbzn1y8rt/image/upload/v1759838042/izg2x0bnucykdy69putx.jpg",
        ],
      },
      {
        store: "Tagamoa Branch",
        storeCode:"12345",
        status: "Rejected",
        updatedAt: new Date("2024-01-15T10:40:00"),
        notes: "Goods have been damaged",
        vehicleNumber: "CAI-2044",
      },
    ],
    comments: [
      { user: "Driver", message: "Received in good condition", time: "10:30" },
      { user: "Branch", message: "Stored in chiller (rack 2).", time: "10:40" },
    ],
    timestamps: {
      created: new Date("2024-01-15T07:00:00"),
      driverPickup: new Date("2024-01-15T08:00:00"),
      delivery: new Date("2024-01-15T10:30:00"),
      branchReceive: new Date("2024-01-15T10:35:00"),
      display: new Date("2024-01-15T10:50:00"),
      qualityCheckedAt: new Date("2024-01-15T10:30:00"),
      storeStatusUpdatedAt: new Date("2024-01-15T10:35:00"),
    },
  },
  {
    id: 3,
    bakeryType: "Croissants",
    quantity: 200,
    driver: "Sara Ibrahim",
    branch: "Heliopolis Branch",
    region: "Riyadh",
    route: "Route H3",
    vehicleNumber: "CAI-3310",
    status: "Pending Pickup",
    qualityStatus: "Rejected",
    storeStatus: "Pending",
    qualityNotes: "conditions are not good",
    storesTickets: null,
    comments: [
      { user: "Factory", message: "Order ready on rack 3.", time: "06:10" },
    ],
    timestamps: {
      created: new Date("2024-01-16T06:00:00"),
      driverPickup: null,
      delivery: null,
      branchReceive: null,
      display: null,
      qualityCheckedAt: new Date("2024-01-16T09:00:00"),
      storeStatusUpdatedAt: null,
    },
  },
  {
    id: 4,
    bakeryType: "Pita Bread",
    quantity: 300,
    driver: "Omar Khaled",
    branch: "Riyadh Branch",
    region: "Riyadh",
    route: "Route D7",
    vehicleNumber: "CAI-8891",
    status: "Received",
    qualityStatus: "Confirmed",
    storeStatus: "Confirmed",
    qualityNotes: "Minor delay reported; product condition acceptable.",
    storesTickets: [
      {
        store: "Riyadh Branch",
        storeCode:"12345",
        status: "Confirmed",
        updatedAt: new Date("2024-01-16T11:00:00"),
        notes: "Received with slight delay.",
        vehicleNumber: "ABC-1234",
      },
      {
        store: "Garden City Branch",
        storeCode:"12345",
        status: "Confirmed",
        updatedAt: new Date("2024-01-16T11:10:00"),
        notes: "Stored immediately",
        vehicleNumber: "ABC-1234",
      },
    ],
    comments: [
      { user: "Driver", message: "Traffic on corniche.", time: "10:50" },
      { user: "Branch", message: "Arrived slightly late.", time: "11:00" },
    ],
    timestamps: {
      created: new Date("2024-01-16T07:30:00"),
      driverPickup: new Date("2024-01-16T08:00:00"),
      delivery: new Date("2024-01-16T10:45:00"),
      branchReceive: new Date("2024-01-16T11:00:00"),
      display: null,
      qualityCheckedAt: new Date("2024-01-16T10:45:00"),
      storeStatusUpdatedAt: new Date("2024-01-16T11:00:00"),
    },
  },
  {
    id: 5,
    bakeryType: "Donuts",
    quantity: 120,
    driver: "Youssef Hassan",
    branch: "Riyadh Branch",
    region: "Riyadh Region",
    route: "Route G5",
    vehicleNumber: "GIZ-4120",
    status: "In Transit",
    qualityStatus: "Pending",
    storeStatus: "Pending",
    qualityNotes: "",
    storesTickets: null,
    comments: [
      { user: "Factory", message: "Sealed boxes: 12", time: "07:10" },
      { user: "Driver", message: "Left factory, ETA 09:05", time: "07:20" },
    ],
    timestamps: {
      created: new Date("2024-01-17T06:45:00"),
      driverPickup: new Date("2024-01-17T07:15:00"),
      delivery: null,
      branchReceive: null,
      display: null,
      qualityCheckedAt: null,
      storeStatusUpdatedAt: null,
    },
  },
  {
    id: 6,
    bakeryType: "Baguettes",
    quantity: 400,
    driver: "Fatma Adel",
    branch: "Riyadh Branch",
    region: "Riyadh Region",
    route: "Route AX2",
    vehicleNumber: "ALX-7733",
    status: "Received",
    qualityStatus: "Confirmed",
    storeStatus: "Confirmed",
    qualityNotes: "Temperature maintained within acceptable range.",
    storesTickets: [
      {
        store: "Riyadh Branch",
        storeCode:"12345",
        status: "Confirmed",
        updatedAt: new Date("2024-01-17T12:10:00"),
        notes: "Stored properly.",
        vehicleNumber: "ABC-1234",
      },
      {
        store: "Smouha Branch",
        storeCode:"12345",
        status: "Confirmed",
        updatedAt: new Date("2024-01-17T12:25:00"),
        notes: "Display prepared",
        vehicleNumber: "ABC-1234",
      },
    ],
    comments: [
      { user: "Driver", message: "Traffic caused delay", time: "12:00" },
      { user: "Branch", message: "All items intact", time: "12:10" },
    ],
    timestamps: {
      created: new Date("2024-01-17T09:00:00"),
      driverPickup: new Date("2024-01-17T09:30:00"),
      delivery: new Date("2024-01-17T11:55:00"),
      branchReceive: new Date("2024-01-17T12:10:00"),
      display: new Date("2024-01-17T12:25:00"),
      qualityCheckedAt: new Date("2024-01-17T11:55:00"),
      storeStatusUpdatedAt: new Date("2024-01-17T12:10:00"),
    },
  },
  {
    id: 7,
    bakeryType: "Muffins",
    quantity: 75,
    driver: "Karim Nasser",
    branch: "Zamalek Branch",
    region: "Riyadh",
    route: "Route Z4",
    vehicleNumber: "CAI-5582",
    status: "Pending Pickup",
    qualityStatus: "Pending",
    storeStatus: "Pending",
    qualityNotes: "",
    storesTickets: null,
    comments: [],
    timestamps: {
      created: new Date("2024-01-18T05:30:00"),
      driverPickup: null,
      delivery: null,
      branchReceive: null,
      display: null,
      qualityCheckedAt: null,
      storeStatusUpdatedAt: null,
    },
  },
  {
    id: 8,
    bakeryType: "Bagels",
    quantity: 180,
    driver: "Mona Hassan",
    branch: "New Cairo Branch",
    region: "Riyadh",
    route: "Route NC1",
    vehicleNumber: "CAI-9921",
    status: "In Transit",
    qualityStatus: "Pending",
    storeStatus: "Pending",
    qualityNotes: "",
    storesTickets: null,
    comments: [{ user: "Driver", message: "On the way.", time: "07:45" }],
    timestamps: {
      created: new Date("2024-01-18T07:00:00"),
      driverPickup: new Date("2024-01-18T07:30:00"),
      delivery: null,
      branchReceive: null,
      display: null,
      qualityCheckedAt: null,
      storeStatusUpdatedAt: null,
    },
  },
  {
    id: 9,
    bakeryType: "Focaccia",
    quantity: 60,
    driver: "Ali Said",
    branch: "Riyadh Branch",
    region: "Riyadh Region",
    route: "Route OC8",
    vehicleNumber: "GIZ-6617",
    status: "Delivered",
    qualityStatus: "Confirmed",
    storeStatus: "Confirmed",
    qualityNotes: "Excellent freshness noted upon receipt.",
    storesTickets: [
      {
        store: "Riyadh Branch",
        storeCode:"12345",
        status: "Confirmed",
        updatedAt: new Date("2024-01-18T14:00:00"),
        notes: "Excellent freshness.",
        vehicleNumber: "ABC-1234",
      },
      {
        store: "Sheikh Zayed Branch",
        storeCode:"12345",
        status: "Confirmed",
        updatedAt: new Date("2024-01-18T14:15:00"),
        notes: "Displayed on arrival",
        vehicleNumber: "ABC-1234",
      },
    ],
    comments: [
      { user: "Branch", message: "Excellent freshness", time: "14:00" },
    ],
    timestamps: {
      created: new Date("2024-01-18T09:00:00"),
      driverPickup: new Date("2024-01-18T09:20:00"),
      delivery: new Date("2024-01-18T13:50:00"),
      branchReceive: new Date("2024-01-18T14:00:00"),
      display: null,
      qualityCheckedAt: new Date("2024-01-18T13:50:00"),
      storeStatusUpdatedAt: new Date("2024-01-18T14:00:00"),
    },
  },
  {
    id: 10,
    bakeryType: "Cookies",
    quantity: 90,
    driver: "Layla Mostafa",
    branch: "Shubra Branch",
    region: "Riyadh",
    route: "Route SH6",
    vehicleNumber: "CAI-1470",
    status: "In Transit",
    qualityStatus: "Pending",
    storeStatus: "Pending",
    qualityNotes: "",
    storesTickets: null,
    comments: [
      { user: "Driver", message: "Departed, ETA 10:00", time: "07:10" },
    ],
    timestamps: {
      created: new Date("2024-01-19T06:30:00"),
      driverPickup: new Date("2024-01-19T07:00:00"),
      delivery: null,
      branchReceive: null,
      display: null,
      qualityCheckedAt: null,
      storeStatusUpdatedAt: null,
    },
  },
];

export default initialShipments;
