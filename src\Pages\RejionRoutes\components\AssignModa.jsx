import { useEffect, useMemo, useState } from "react";
import { X, GripVertical, Plus, Trash2 } from "lucide-react";
import { Select } from "antd";

const AssignModal = ({
  open,
  onClose,
  region,
  allStores = [],
  onSave,
}) => {
  const [points, setPoints] = useState([]);
  const [dragIndex, setDragIndex] = useState(null);
  const [storeToAdd, setStoreToAdd] = useState(undefined);

  useEffect(() => {
    if (region) {
      const initial = Array.isArray(region.points) ? region.points : [];
      setPoints(initial);
    }
  }, [region]);

  const availableStoreOptions = useMemo(() => {
    const assignedIds = new Set(points.map((p) => p.id));
    return allStores
      .filter((s) => !assignedIds.has(s.id))
      .map((s) => ({ label: s.name, value: s.id }));
  }, [allStores, points]);

  const addSelectedStore = () => {
    if (!storeToAdd) return;
    const store = allStores.find((s) => s.id === storeToAdd);
    if (!store) return;
    if (points.some((p) => p.id === store.id)) return;
    setPoints((prev) => [...prev, store]);
    setStoreToAdd(undefined);
  };

  const removePoint = (id) => {
    setPoints((prev) => prev.filter((p) => p.id !== id));
  };

  const onDropAt = (targetIdx) => {
    if (dragIndex === null || dragIndex === targetIdx) return;
    const updated = [...points];
    const [moved] = updated.splice(dragIndex, 1);
    updated.splice(targetIdx, 0, moved);
    setPoints(updated);
    setDragIndex(null);
  };

  const handleSave = () => {
    if (!region) return;
    onSave && onSave({ ...region, points });
    onClose && onClose();
  };

  if (!open) return null;

  return (
    <div
      onClick={() => onClose && onClose()}
      className="fixed !my-0 inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    >
      <div
        onClick={(e) => e.stopPropagation()}
        className="bg-white rounded-xl shadow-xl w-full max-w-3xl max-h-[90vh] overflow-y-auto"
      >
        <div className="p-6 border-b border-gray-200 flex justify-between items-center">
          <div>
            <h2 className="text-xl font-bold text-gray-900">Assign & Reorder Route</h2>
            <p className="text-sm text-gray-600 mt-1">
              {region?.name || "Region"}
            </p>
          </div>
          <button onClick={() => onClose && onClose()} className="text-gray-400 hover:text-gray-600">
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6 space-y-5">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Add Store to Route</label>
            <div className="flex gap-2">
              <Select
                className="flex-1"
                size="large"
                placeholder="Select a store"
                value={storeToAdd}
                onChange={setStoreToAdd}
                options={availableStoreOptions}
                showSearch
                filterOption={(input, option) => option?.label?.toLowerCase().includes(input.toLowerCase())}
              />
              <button
                type="button"
                onClick={addSelectedStore}
                disabled={!storeToAdd}
                className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors disabled:opacity-50"
              >
                <Plus className="inline w-4 h-4 mr-1" /> Add
              </button>
            </div>
          </div>

          {points.length > 0 ? (
            <div>
              <p className="text-xs text-gray-500 mb-2">Drag rows to reorder the route sequence.</p>
              <ul className="divide-y divide-gray-200 border border-gray-200 rounded-lg">
                {points.map((p, idx) => (
                  <li
                    key={p.id}
                    draggable
                    onDragStart={() => setDragIndex(idx)}
                    onDragOver={(e) => e.preventDefault()}
                    onDrop={() => onDropAt(idx)}
                    onDragEnd={() => setDragIndex(null)}
                    className="flex items-center justify-between p-3 bg-white"
                    title="Drag to reorder"
                  >
                    <div className="flex items-center gap-3">
                      <GripVertical className="w-4 h-4 text-gray-400" />
                      <span className="inline-flex items-center justify-center w-6 h-6 text-xs font-medium rounded-full bg-orange-100 text-orange-700">
                        {idx + 1}
                      </span>
                      <div className="flex flex-col">
                        <span className="text-sm text-gray-800">{p.name}</span>
                        <span className="text-xs text-gray-500">#{p.storeCode || p.id}</span>
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={() => removePoint(p.id)}
                      className="text-sm text-red-600 hover:text-red-700 flex items-center gap-1"
                    >
                      <Trash2 className="w-4 h-4" /> Remove
                    </button>
                  </li>
                ))}
              </ul>
            </div>
          ) : (
            <div className="text-sm text-gray-500">No stores in this route yet.</div>
          )}
        </div>

        <div className="p-6 border-t border-gray-200 flex gap-3">
          <button
            onClick={() => onClose && onClose()}
            className="flex-1 px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="flex-1 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
          >
            Save Changes
          </button>
        </div>
      </div>
    </div>
  );
};

export default AssignModal;
