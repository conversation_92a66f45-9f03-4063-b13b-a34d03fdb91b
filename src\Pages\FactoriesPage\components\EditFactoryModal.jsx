import React, { useEffect, useState } from "react";
import { X } from "lucide-react";
import { Select } from "antd";
import { getAllRegions, updateProduction } from "../../../api/apiService";

const EditFactoryModal = ({ open, onClose, factory, onSave }) => {
  const [name, setName] = useState("");
  const [regionId, setRegionId] = useState(null);
  const [code, setCode] = useState("");
  const [type, setType] = useState();
  const [regionsOptions, setRegionsOptions] = useState([]);

  useEffect(() => {
    if (!open) return;
    const handleKeyDown = (e) => {
      if (e.key === "Escape") {
        onClose && onClose();
      }
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [open, onClose]);

  useEffect(() => {
    (async () => {
      try {
        const data = await getAllRegions();
        setRegionsOptions(Array.isArray(data.data) ? data.data : []);
      } catch (e) {
        console.error(e);
        setRegionsOptions([]);
      }
    })();
  }, []);

  useEffect(() => {
    setName(factory?.name || "");
    setRegionId(factory?.region?.id ?? factory?.region_id ?? null);
    setCode(
      factory?.code === 0 || factory?.code ? String(factory.code) : ""
    );
    setType(factory?.type);
  }, [factory]);

  if (!open) return null;

  const handleSave = async () => {
    const trimmed = name.trim();
    if (!factory || !trimmed || !regionId || !code || !type) return;
    const payload = {
      id: factory.id,
      region_id: Number(regionId),
      name: trimmed,
      code: Number(code),
      type: type,
    };
    try {
      const res = await updateProduction(factory.id, payload);
      if (res?.status === 200 || res?.status === 201) {
        onSave && onSave(payload);
        onClose && onClose();
      }
    } catch (e) {
      console.error(e);
    }
  };

  return (
    <div
      onClick={() => onClose && onClose()}
      className="fixed !m-0 inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    >
      <div
        onClick={(e) => e.stopPropagation()}
        className="bg-white rounded-xl shadow-xl w-full max-w-lg"
      >
        <div className="p-6 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-xl font-bold text-gray-900">Update Production</h2>
          <button
            onClick={() => onClose && onClose()}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6 space-y-5">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">production Name</label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              placeholder="e.g. Central Baking Plant"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">production Code</label>
            <input
              type="number"
              value={code}
              onChange={(e) => setCode(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              placeholder="e.g. 123"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Select Region</label>
            <Select
              size="large"
              value={regionId}
              onChange={(value) => setRegionId(value)}
              className="w-full"
              placeholder="Choose region"
              options={regionsOptions.map((r) => ({ label: r.name, value: r.id }))}
              allowClear
              showSearch
              optionFilterProp="label"
              dropdownStyle={{ zIndex: 9999 }}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">production Type</label>
            <Select
              size="large"
              value={type}
              onChange={(value) => setType(value)}
              className="w-full"
              placeholder="Select type (CFL or CML)"
              options={[
                { label: "CFL", value: "CFL" },
                { label: "CML", value: "CML" },
              ]}
              allowClear
              dropdownStyle={{ zIndex: 9999 }}
            />
          </div>
        </div>

        <div className="p-6 border-t border-gray-200 flex gap-3">
          <button
            onClick={() => onClose && onClose()}
            className="flex-1 px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="flex-1 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors disabled:opacity-50"
            disabled={!name.trim() || !regionId || !code || !type}
          >
            Save Changes
          </button>
        </div>
      </div>
    </div>
  );
};

export default EditFactoryModal;


