import { useState, useEffect, useCallback } from "react";
import { getExamStudents } from "../../../api/apiService";

export const useGetExamStudents = (exam_id , groupId = null) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [students, setStudents] = useState([]);
  const [totalStudents, setTotalStudents] = useState(0);

  const fetchStudents = useCallback(async (id) => {
    if (!id) return;

    setLoading(true);
    setError(null);

    try {
      const response = await getExamStudents({ exam_id: +id });
      if (response.data.status === "success") {
        if(groupId){
          const filteredStudents = response.data.data.students.filter(
            (student) => student?.collection_id == groupId
          );
          setStudents(filteredStudents || []);
          setTotalStudents(filteredStudents.length || 0);
        }else{

          setStudents(response.data.data.students || []);
          setTotalStudents(response.data.data.total_students || 0);
        }
        return response.data.data;
      } else {
        throw new Error(response.data.message || "فشل في جلب بيانات الطلاب");
      }
    } catch (err) {
      const errorMessage =
        err.response?.data?.message ||
        err.message ||
        "حدث خطأ أثناء جلب بيانات الطلاب";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (exam_id && typeof exam_id === "number") {
      fetchStudents(exam_id);
    }
  }, [exam_id, fetchStudents]);

  return {
    students,
    totalStudents,
    loading,
    error,
    fetchStudents,
    reset: () => {
      setError(null);
      setStudents([]);
      setTotalStudents(0);
    },
  };
};
