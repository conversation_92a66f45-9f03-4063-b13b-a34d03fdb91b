import { useMemo, useState } from 'react';
import { Map, ListOrdered, PlusCircle, CalendarClock, ChevronDown, ChevronUp } from 'lucide-react';

const RejionRoutesStats = ({ regions = [] }) => {
  const [expanded, setExpanded] = useState(false);

  const stats = useMemo(() => {
    const totalRejions = regions.length;
    const totalPoints = regions.reduce((sum, r) => sum + (Array.isArray(r.points) ? r.points.length : 0), 0);
    const avgPoints = totalRejions > 0 ? (totalPoints / totalRejions).toFixed(1) : 0;
    const mostRecent = regions.reduce((latest, r) => {
      const d = new Date(r.createdAt);
      return !latest || d > latest ? d : latest;
    }, null);

    return { totalRejions, totalPoints, avgPoints, mostRecent };
  }, [regions]);

  const primaryStats = [
    { title: 'Total Rejions', value: stats.totalRejions, icon: Map, bg: 'bg-blue-50', color: 'text-blue-600' },
    { title: 'Total Store Points', value: stats.totalPoints, icon: ListOrdered, bg: 'bg-green-50', color: 'text-green-600' },
    { title: 'Avg Points/Rejion', value: stats.avgPoints, icon: PlusCircle, bg: 'bg-purple-50', color: 'text-purple-600' },
    { title: 'Most Recent', value: stats.mostRecent ? stats.mostRecent.toLocaleDateString() : '-', icon: CalendarClock, bg: 'bg-orange-50', color: 'text-orange-600' },
  ];

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {primaryStats.map((stat, i) => {
          const Icon = stat.icon;
          return (
            <div key={i} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                </div>
                <div className={`${stat.bg} p-3 rounded-lg`}>
                  <Icon className={`w-6 h-6 ${stat.color}`} />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {expanded && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Rejions Overview</h3>
          <div className="space-y-3">
            {regions.map((r) => (
              <div key={r.id} className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 rounded-full bg-orange-500" />
                  <span className="text-sm font-medium text-gray-700">{r.name}</span>
                </div>
                <div className="text-sm text-gray-600">
                  {Array.isArray(r.points) ? r.points.length : 0} points
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="flex justify-center">
        <button
          onClick={() => setExpanded(!expanded)}
          className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          {expanded ? (
            <>
              <ChevronUp className="w-4 h-4" />
              Show Less
            </>
          ) : (
            <>
              <ChevronDown className="w-4 h-4" />
              Show More Stats
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default RejionRoutesStats;
