import React, { useEffect, useMemo, useState } from "react";
import { Plus } from "lucide-react";
import RejionRoutesTable from "./../RejionRoutes/components/RejionRoutesTable";
import stores from "../../data/stores";
import AssignModal from "./../RejionRoutes/components/AssignModa";
import UpdateRejionRouteModal from "./../RejionRoutes/components/UpdateRejonRouteModal";
import AddNewRejionRouteModal from "./../RejionRoutes/components/AddNewRejsionRouteModal";
import { getAllRoutes } from "../../api/apiService";

const AllRoutes = () => {
  const [selectedShipment, setSelectedShipment] = useState(null);
  const [regionRoutes, setRejionRoutes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    const fetchRoutes = async () => {
      try {
        setLoading(true);
        setError("");
        const routes = await getAllRoutes();
        const normalized = (routes?.data || []).map((r) => ({
          id: r.id ?? r.route_id ?? r._id,
          name: r.name ?? r.route_name ?? "Unnamed Route",
          regionName: r.regionName ?? r.region?.name ?? r.region_name ?? "",
          route_stores: Array.isArray(r.route_stores)
            ? r.route_stores
            : Array.isArray(r.stores)
            ? r.stores
            : [],
          createdAt: r.createdAt ?? r.created_at ?? r.created ?? new Date().toISOString(),
        }));
        setRejionRoutes(normalized);
      } catch (e) {
        setError("Failed to load routes");
        console.error(e);
      } finally {
        setLoading(false);
      }
    };
    fetchRoutes();
  }, []);



  const [assignOpen, setAssignOpen] = useState(false);
  const [selectedRejion, setSelectedRejion] = useState(null);
  const [updateOpen, setUpdateOpen] = useState(false);


  const [addRejionModal, setAddRejionModal] = useState(false);


  const handleCreateRegion = (route) => {
    setRejionRoutes((prev) => [...prev, route]);
  };


  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">
         All Routes Management
        </h1>
          <button
            onClick={() => setAddRejionModal(true)}
            className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add New Route
          </button>
     
      </div>

 
          {/* Shipment Statistics */}
          {/* <RejionRoutesStats regions={regionRoutes?.routes} /> */}

          <RejionRoutesTable
            showRouteNameColumn
            setSelectedShipment={setSelectedShipment}
            data={regionRoutes}
            setShipments={setRejionRoutes}
            onEditRejion={(region) => {
              setSelectedRejion(region);
              setUpdateOpen(true);
            }}
            onAssignRejion={(region) => {
              setSelectedRejion(region);
              setAssignOpen(true);
            }}
          />


      {addRejionModal && (
        <AddNewRejionRouteModal
          withRegoinInput
          onClose={() => setAddRejionModal(false)}
          onCreate={handleCreateRegion}
        />
      )}

    

      <AssignModal
        open={assignOpen}
        onClose={() => setAssignOpen(false)}
        region={selectedRejion}
        allStores={stores}
        onSave={(updated) => {
          setRejionRoutes((prev) =>
            prev.map((r) => (r.id === updated.id ? updated : r))
          );
        }}
      />

      <UpdateRejionRouteModal
        open={updateOpen}
        onClose={() => setUpdateOpen(false)}
        region={selectedRejion}
        onSave={(updated) => {
          setRejionRoutes((prev) =>
            prev.map((r) => (r.id === updated.id ? { ...r, ...updated } : r))
          );
        }}
      />
    </div>
  );
};

export default AllRoutes;
